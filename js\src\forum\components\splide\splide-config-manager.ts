import { defaultConfig } from '../../../common/config';
import { ARRAY_CONSTANTS } from '../../../common/config/constants';
import type {
  ISplideConfigManager,
  ConfigCalculationResult,
  SplideFullConfig,
  ResponsiveConfig,
  SplideBreakpoints,
  ValidationResult
} from '../../../common/config/types';

/**
 * Splide configuration constants
 * Based on Splide.js documentation requirements for loop mode
 */
const SPLIDE_CONFIG_CONSTANTS = {
  MIN_SLIDES_FOR_LOOP: 2, // Splide requires minimum 2 slides for loop mode
  // Responsive perPage values
  MOBILE_PER_PAGE: 1,
  TABLET_PER_PAGE: 1,
  DESKTOP_PER_PAGE: 1,
} as const;

/**
 * Splide configuration manager
 * Handles all Splide configuration generation, validation, and responsive calculations
 */
export class SplideConfigManager implements ISplideConfigManager {
  /**
   * Calculate complete Splide configuration based on slide count and transition time
   */
  calculateConfiguration(slideCount: number, transitionTime: number): ConfigCalculationResult {
    const enableLoop = this.shouldEnableLoop(slideCount);
    const responsiveConfig = this.calculateResponsiveConfig();
    const finalConfig = this.buildFinalConfig(transitionTime, enableLoop);

    return {
      enableLoop,
      responsiveConfig,
      finalConfig
    };
  }

  /**
   * Validate Splide configuration
   */
  validateConfiguration(config: SplideFullConfig): ValidationResult {
    const errors: string[] = [];

    // Validate required properties
    if (config.autoplay && typeof config.autoplay === 'object' &&
      (typeof config.autoplay.interval !== 'number' || config.autoplay.interval <= ARRAY_CONSTANTS.EMPTY_LENGTH)) {
      errors.push('Invalid autoplay interval configuration');
    }

    if (typeof config.gap !== 'string' || !config.gap.match(/^\d+px$/)) {
      errors.push('Invalid gap configuration - must be in format "Npx"');
    }

    if (typeof config.perPage !== 'number' || config.perPage <= ARRAY_CONSTANTS.EMPTY_LENGTH) {
      errors.push('Invalid perPage configuration');
    }

    // Validate breakpoints
    if (config.breakpoints && typeof config.breakpoints === 'object') {
      for (const [width, breakpointConfig] of Object.entries(config.breakpoints)) {
        const widthNum = Number(width);
        if (Number.isNaN(widthNum) || widthNum <= ARRAY_CONSTANTS.EMPTY_LENGTH) {
          errors.push(`Invalid breakpoint width: ${width}`);
        }
        if (typeof breakpointConfig.perPage !== 'number' || breakpointConfig.perPage <= ARRAY_CONSTANTS.EMPTY_LENGTH) {
          errors.push(`Invalid perPage for breakpoint ${width}`);
        }
        if (typeof breakpointConfig.gap !== 'string' || !breakpointConfig.gap.match(/^\d+px$/)) {
          errors.push(`Invalid gap for breakpoint ${width}`);
        }
      }
    }

    return {
      isValid: errors.length === ARRAY_CONSTANTS.EMPTY_LENGTH,
      errors
    };
  }

  /**
   * Determine if loop mode should be enabled based on slide count
   */
  private shouldEnableLoop(slideCount: number): boolean {
    // Splide.js handles loop mode much better than Swiper.js
    // Enable loop mode if we have enough slides
    return slideCount >= SPLIDE_CONFIG_CONSTANTS.MIN_SLIDES_FOR_LOOP;
  }

  /**
   * Calculate responsive configuration
   */
  private calculateResponsiveConfig(): ResponsiveConfig {
    return {
      mobile: {
        perPage: SPLIDE_CONFIG_CONSTANTS.MOBILE_PER_PAGE,
        gap: '10px'
      },
      tablet: {
        perPage: SPLIDE_CONFIG_CONSTANTS.TABLET_PER_PAGE,
        gap: '12px'
      },
      desktop: {
        perPage: SPLIDE_CONFIG_CONSTANTS.DESKTOP_PER_PAGE,
        gap: defaultConfig.slider.splide.gap
      }
    };
  }

  /**
   * Build final Splide configuration object
   */
  private buildFinalConfig(
    transitionTime: number,
    enableLoop: boolean
  ): SplideFullConfig {
    const breakpoints: SplideBreakpoints = {
      320: { perPage: SPLIDE_CONFIG_CONSTANTS.MOBILE_PER_PAGE, gap: '10px' },
      768: { perPage: SPLIDE_CONFIG_CONSTANTS.TABLET_PER_PAGE, gap: '12px' },
      1024: { perPage: SPLIDE_CONFIG_CONSTANTS.DESKTOP_PER_PAGE, gap: defaultConfig.slider.splide.gap }
    };

    let splideType: 'loop' | 'slide' = 'slide';
    if (enableLoop) {
      splideType = 'loop';
    }

    let autoplayConfig: { interval: number; pauseOnHover: boolean } | false = false;
    if (transitionTime > ARRAY_CONSTANTS.EMPTY_LENGTH) {
      autoplayConfig = {
        interval: transitionTime,
        pauseOnHover: true
      };
    }

    return {
      type: splideType,
      autoplay: autoplayConfig,
      gap: defaultConfig.slider.splide.gap,
      focus: defaultConfig.slider.splide.focus,
      perPage: defaultConfig.slider.splide.perPage,
      breakpoints,
      pagination: defaultConfig.slider.splide.pagination,
      arrows: defaultConfig.slider.splide.arrows,
      speed: 600
    };
  }
}
