/* Header Advertisement Extension Styles */

/* Advertisement Container */
.adContainer {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto 20px auto;
  position: relative !important;
  overflow: hidden;
}

/* Forced container styles - used to override conflicting CSS */
.adContainer--forced {
  display: block !important;
  min-height: 300px !important;
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  max-width: 1200px !important;
  width: 100% !important;
  margin: 0 auto 20px auto !important;
  overflow: hidden !important;
}

/* Splide Advertisement Styles */
.adSplide {
  width: 100%;
  height: 300px;
  position: relative;
}

/* Swiper Advertisement Styles */
.adSwiper {
  width: 100%;
  height: 300px;
  position: relative;
}

/* Forced Splide styles - used to override conflicting CSS */
.adSplide--forced {
  width: 100% !important;
  height: 300px !important;
  position: relative !important;
  display: block !important;
  min-height: 300px !important;
  background: transparent !important;
  border: none !important;
  border-radius: 12px !important;
  overflow: hidden !important;
}

/* Mobile-specific Splide styles */
.adSplide--mobile {
  height: 200px !important;
  min-height: 200px !important;
}

/* Forced Swiper styles - used to override conflicting CSS */
.adSwiper--forced {
  width: 100% !important;
  height: 300px !important;
  position: relative !important;
  display: block !important;
  min-height: 300px !important;
  background: transparent !important;
  border: none !important;
  border-radius: 12px !important;
  overflow: hidden !important;
}

/* Mobile-specific Swiper styles */
.adSwiper--mobile {
  height: 200px !important;
  min-height: 200px !important;
}

.adSplide .splide__track {
  height: 100%;
}

.adSplide .splide__list {
  height: 100%;
}

/* Forced track styles */
.splide__track--forced {
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
}

/* Forced list styles */
.splide__list--forced {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  margin: 0 !important;
  padding: 0 !important;
  list-style: none !important;
}

/* Forced slide styles */
.splide__slide--forced {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.adSplide .splide__slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s ease;
}

/* Slide image styles */
.splide__slide__image {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  display: block !important;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.adSplide .splide__slide img:hover {
  transform: scale(1.02);
}

/* Slide effects for better visual hierarchy */
.adSplide .splide__slide {
  text-align: center;
  font-size: 18px;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
  overflow: hidden;
  transition: opacity 0.3s ease, transform 0.3s ease;
  opacity: 0.9;
  transform: scale(0.98);
}

.adSplide .splide__slide.is-active {
  opacity: 1;
  transform: scale(1);
}

.adSplide .splide__slide.is-next,
.adSplide .splide__slide.is-prev {
  opacity: 0.8;
  transform: scale(0.98);
}

/* Splide Navigation - Enhanced Design Based on Reference */
.adSplide .splide__arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  color: #333;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  overflow: hidden;
}

.adSplide .splide__arrow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.adSplide .splide__arrow:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 0.95) 100%);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow:
    0 6px 18px rgba(0, 0, 0, 0.2),
    0 3px 6px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transform: translateY(-50%) scale(1.05);
}

.adSplide .splide__arrow:hover::before {
  opacity: 1;
}

.adSplide .splide__arrow:active {
  transform: translateY(-50%) scale(0.95);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.adSplide .splide__arrow--prev {
  left: 15px;
}

.adSplide .splide__arrow--next {
  right: 15px;
}

.adSplide .splide__arrow svg {
  width: 22px;
  height: 22px;
  fill: #333;
  transition: fill 0.3s ease;
  z-index: 1;
  position: relative;
  pointer-events: none; /* Ensure clicks go to button, not SVG */
}

.adSplide .splide__arrow:hover svg {
  fill: #000;
}

/* Ensure proper arrow direction - prev arrow should point left, next arrow should point right */
.adSplide .splide__arrow--prev svg {
  transform: rotate(0deg); /* Default left-pointing arrow */
}

.adSplide .splide__arrow--next svg {
  transform: rotate(180deg); /* Rotate to point right */
}

/* Splide Pagination */
.adSplide .splide__pagination {
  bottom: 10px;
  display: flex;
  justify-content: center;
  gap: 8px;
}

.adSplide .splide__pagination__page {
  background: rgba(255, 255, 255, 0.7);
  opacity: 1;
  border: none;
  border-radius: 50%;
  width: 10px;
  height: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.adSplide .splide__pagination__page.is-active {
  background: #fff;
  transform: scale(1.2);
}

.adSwiper .swiper-wrapper {
  height: 100%;
}

/* Forced wrapper styles */
.swiper-wrapper--forced {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
}



/* Forced slide styles */
.swiper-slide--forced {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.adSwiper .swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s ease;
}

/* Slide image styles */
.swiper-slide__image {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  display: block !important;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.adSwiper .swiper-slide img:hover {
  transform: scale(1.02);
}

/* Partial slide effects for better visual hierarchy */
.adSwiper .swiper-slide {
  text-align: center;
  font-size: 18px;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
  overflow: hidden;
  /* Let Swiper calculate width automatically for partial slide display */
  transition: opacity 0.3s ease, transform 0.3s ease;
  opacity: 0.7;
  transform: scale(0.95);
}

.adSwiper .swiper-slide-active {
  opacity: 1;
  transform: scale(1);
}

.adSwiper .swiper-slide-next,
.adSwiper .swiper-slide-prev {
  opacity: 0.8;
  transform: scale(0.98);
}

/* Swiper Navigation - Default Swiper styles (fallback) */
.adSwiper .swiper-button-next,
.adSwiper .swiper-button-prev {
  color: #fff;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 40px;
  height: 40px;
}

.adSwiper .swiper-button-next:after,
.adSwiper .swiper-button-prev:after {
  font-size: 16px;
}

/* Custom navigation button styles - Higher specificity to override default */
.adSwiper .swiper-navigation-button {
  position: absolute !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 100 !important;
  width: 40px !important;
  height: 40px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  color: #333 !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 18px !important;
  font-weight: bold !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.3s ease !important;

  &:hover {
    background: rgba(255, 255, 255, 1) !important;
    transform: translateY(-50%) scale(1.1) !important;
  }
}

.adSwiper .swiper-navigation-button--prev {
  left: 15px !important;
}

.adSwiper .swiper-navigation-button--next {
  right: 15px !important;
}

/* Swiper Pagination */
.adSwiper .swiper-pagination {
  bottom: 10px;
}

.adSwiper .swiper-pagination-bullet {
  background: rgba(255, 255, 255, 0.7);
  opacity: 1;
}

.adSwiper .swiper-pagination-bullet-active {
  background: #fff;
}

/* Header Icon - Mobile Only */
.HeaderIcon-container {
  display: none; /* Hidden by default */
}

.HeaderIcon-container.mobile-only {
  display: inline-flex;
  align-items: center;
  margin-left: 12px;
  vertical-align: middle;
}

.HeaderIcon-image {
  height: 24px;
  width: auto;
  transition: opacity 0.2s ease, transform 0.2s ease;
  border-radius: 4px;
}

.HeaderIcon-image:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

/* Ensure proper alignment within navigation */
#app-navigation .App-backControl {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* Legacy support for existing ID */
#wusong8899HeaderAdvIcon {
  display: none; /* Hidden by default */
}

/* Show header icon only on mobile devices */
@media (max-width: 768px) {
  .HeaderIcon-container.mobile-only {
    display: inline-flex;
  }

  #wusong8899HeaderAdvIcon {
    display: inline-flex;
    align-items: center;
    margin-left: 12px;
    vertical-align: middle;
  }
}

#wusong8899HeaderAdvIcon img {
  height: 24px;
  width: auto;
  transition: opacity 0.2s ease, transform 0.2s ease;
  border-radius: 4px;
}

#wusong8899HeaderAdvIcon img:hover {
  opacity: 0.8;
  transform: scale(1.05);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .adContainer {
    max-width: 100%;
    width: 95%;
    margin: 0 auto 20px auto;
    padding: 0 10px;
  }

  .adSwiper {
    height: 200px;
  }

  /* Mobile forced styles */
  .adContainer--forced {
    min-height: 200px !important;
  }

  .adSwiper--forced,
  .adSwiper--mobile {
    height: 200px !important;
    min-height: 200px !important;
  }

  /* Adjust partial slide display for mobile */
  .adSwiper .swiper-slide {
    opacity: 0.6;
    transform: scale(0.9);
  }

  .adSwiper .swiper-slide-active {
    opacity: 1;
    transform: scale(1);
  }

  /* Mobile header icon positioning */
  .HeaderIcon-container.mobile-only {
    margin-left: 8px;
  }

  .HeaderIcon-image {
    height: 20px;
  }

  .adSwiper .swiper-button-next,
  .adSwiper .swiper-button-prev {
    width: 30px;
    height: 30px;
  }

  .adSwiper .swiper-button-next:after,
  .adSwiper .swiper-button-prev:after {
    font-size: 12px;
  }

  /* Mobile custom navigation button styles */
  .adSwiper .swiper-navigation-button {
    width: 30px !important;
    height: 30px !important;
    font-size: 14px !important;
  }

  /* Mobile Splide arrow styles */
  .adSplide .splide__arrow {
    width: 38px;
    height: 38px;
    box-shadow:
      0 3px 8px rgba(0, 0, 0, 0.15),
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .adSplide .splide__arrow--prev {
    left: 10px;
  }

  .adSplide .splide__arrow--next {
    right: 10px;
  }

  .adSplide .splide__arrow svg {
    width: 18px;
    height: 18px;
    pointer-events: none; /* Ensure clicks go to button, not SVG */
  }

  /* Maintain proper arrow direction on mobile */
  .adSplide .splide__arrow--prev svg {
    transform: rotate(0deg); /* Left-pointing arrow */
  }

  .adSplide .splide__arrow--next svg {
    transform: rotate(180deg); /* Right-pointing arrow */
  }

  .adSplide .splide__arrow:hover {
    transform: translateY(-50%) scale(1.02);
  }

  /* Adjust slide display for mobile */
  .adSplide .splide__slide {
    opacity: 0.8;
    transform: scale(0.95);
  }

  .adSplide .splide__slide.is-active {
    opacity: 1;
    transform: scale(1);
  }

  #wusong8899HeaderAdvIcon img {
    height: 20px;
  }

  .HeaderIcon-container {
    margin-left: 8px;
  }

  .HeaderIcon-image {
    height: 20px;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .adContainer {
    max-width: 100%;
    width: 90%;
    margin: 0 auto 20px auto;
    padding: 0 5px;
  }
}
