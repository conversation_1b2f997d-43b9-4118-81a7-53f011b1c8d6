{"version": 3, "file": "forum.js", "sources": ["../src/forum/utils/dom-utils.ts", "../src/common/config/constants.ts", "../src/common/config/defaults.ts", "../src/forum/components/splide/splide-config-manager.ts", "../src/forum/utils/mobile-detection.ts", "../src/forum/components/splide/splide-dom-builder.ts", "../src/forum/components/splide/slide-data-manager.ts", "../node_modules/.pnpm/@splidejs+splide@4.1.4/node_modules/@splidejs/splide/dist/js/splide.esm.js", "../src/forum/components/splide/splide-lifecycle-manager.ts", "../src/forum/components/slideshow-manager.ts", "../src/forum/utils/error-handler.ts", "../src/forum/utils/config-manager.ts", "../src/forum/index.ts"], "sourcesContent": ["import type { DOMElementOptions, StylesObject } from '../../common/config/types';\n\n/**\n * DOM utility functions for safe DOM manipulation\n */\n\n/**\n * Safely query a single element\n */\nexport const querySelector = (selector: string): Element | false => {\n    try {\n        return document.querySelector(selector) || false;\n    } catch {\n        return false;\n    }\n};\n\n/**\n * Safely query multiple elements\n */\nexport const querySelectorAll = (selector: string): NodeListOf<Element> => {\n    try {\n        return document.querySelectorAll(selector);\n    } catch {\n        return document.querySelectorAll(''); // Returns empty NodeList\n    }\n};\n\n/**\n * Safely get element by ID\n */\nexport const getElementById = (id: string): HTMLElement | false => {\n    try {\n        return document.getElementById(id) || false;\n    } catch {\n        return false;\n    }\n};\n\n/**\n * Safely create element with options\n */\nexport const createElement = (\n    tagName: string,\n    options: DOMElementOptions = {},\n    innerHTML = ''\n): HTMLElement => {\n    try {\n        const element = document.createElement(tagName);\n\n        // Set attributes\n        for (const [key, value] of Object.entries(options)) {\n            if (key === 'className') {\n                element.className = String(value);\n            } else if (key === 'id') {\n                element.id = String(value);\n            } else {\n                element.setAttribute(key, String(value));\n            }\n        }\n\n        if (innerHTML) {\n            element.innerHTML = innerHTML;\n        }\n\n        return element;\n    } catch {\n        return document.createElement('div'); // Fallback\n    }\n};\n\n/**\n * Safely append child element\n */\nexport const appendChild = (parent: Element, child: Element): void => {\n    try {\n        parent.appendChild(child);\n    } catch {\n        // Silently handle append errors\n    }\n};\n\n/**\n * Safely prepend child element\n */\nexport const prependChild = (parent: Element, child: Element): void => {\n    try {\n        parent.prepend(child);\n    } catch {\n        // Silently handle prepend errors\n    }\n};\n\n/**\n * Safely remove element\n */\nexport const removeElement = (element: Element): void => {\n    try {\n        element.remove();\n    } catch {\n        // Silently handle removal errors\n    }\n};\n\n/**\n * Safely set styles on element\n */\nexport const setStyles = (element: HTMLElement, styles: StylesObject): void => {\n    try {\n        for (const [property, value] of Object.entries(styles)) {\n            element.style.setProperty(property, String(value));\n        }\n    } catch {\n        // Silently handle style errors\n    }\n};\n", "/**\n * Application constants for Header Advertisement extension\n */\n\n// Mobile detection constants\nexport const MO<PERSON>LE_DETECTION = {\n  USER_AGENT_SUBSTR_START: 0,\n  USER_AGENT_SUBSTR_LENGTH: 4,\n} as const;\n\n// Error handling constants\nexport const ERROR_HANDLING = {\n  MAX_ERROR_LOG_ENTRIES: 50,\n  DOM_READY_TIMEOUT: 5000,\n  SLIDE_NUMBER_MIN: 1,\n  SLIDE_NUMBER_MAX: 30,\n  TRANSITION_TIME_MIN: 1000,\n  TRANSITION_TIME_MAX: 30_000,\n  CONFIG_MAX_SLIDES_MIN: 1,\n  CONFIG_MAX_SLIDES_MAX: 50,\n} as const;\n\n// Admin component constants\nexport const ADMIN_CONSTANTS = {\n  SAVE_DEBOUNCE_DELAY: 500,\n  DEFAULT_MAX_SLIDES: 30,\n  EMPTY_SLIDES_COUNT: 0,\n} as const;\n\n// UI styling constants\nexport const UI_STYLES = {\n  HEADER_ICON_HEIGHT: 24,\n  HEADER_ICON_MARGIN_TOP: 8,\n} as const;\n\n// Mobile layout constants\nexport const MOBILE_LAYOUT = {\n  SCREEN_WIDTH_MULTIPLIER: 2,\n  SCREEN_WIDTH_OFFSET: 50,\n  CONTAINER_MARGIN_MULTIPLIER: 0.254,\n} as const;\n\n// Slideshow constants\nexport const SLIDESHOW_CONSTANTS = {\n  SLIDE_INCREMENT: 1,\n  INITIAL_SLIDE_INDEX: 1,\n  VALIDATION_ERRORS_EMPTY: 0,\n} as const;\n\n// Array and index constants\nexport const ARRAY_CONSTANTS = {\n  EMPTY_LENGTH: 0,\n  FIRST_INDEX: 0,\n  NOT_FOUND_INDEX: -1,\n  NEXT_ITEM_OFFSET: 1,\n  LAST_ITEM_OFFSET: -1,\n} as const;\n\n// Timing constants\nexport const TIMING = {\n  CHECK_INTERVAL: 10,\n  DATA_CHECK_INTERVAL: 100,\n  DEFAULT_TRANSITION_TIME: 5000,\n} as const;\n\n// DOM element constants\nexport const DOM_ELEMENTS = {\n  SPLIDE_AD_CONTAINER_ID: 'splideAdContainer',\n  HEADER_ICON_ID: 'wusong8899HeaderAdvIcon',\n} as const;\n\n// CSS class constants\nexport const CSS_CLASSES = {\n  SPLIDE: 'splide',\n  SPLIDE_TRACK: 'splide__track',\n  SPLIDE_LIST: 'splide__list',\n  SPLIDE_SLIDE: 'splide__slide',\n  SPLIDE_ARROW_NEXT: 'splide__arrow--next',\n  SPLIDE_ARROW_PREV: 'splide__arrow--prev',\n  SPLIDE_PAGINATION: 'splide__pagination',\n  AD_SPLIDE: 'adSplide',\n} as const;\n\n// CSS selector constants\nexport const CSS_SELECTORS = {\n  APP_NAVIGATION_BACK_CONTROL: '#app-navigation .App-backControl',\n  CONTENT_CONTAINER: '#content .container',\n  NAV_ITEMS: '.item-nav',\n  SPLIDE_PAGINATION_EL: '.splide__pagination',\n  SPLIDE_ARROW_NEXT_EL: '.splide__arrow--next',\n  SPLIDE_ARROW_PREV_EL: '.splide__arrow--prev',\n} as const;\n\n\n\n// Extension configuration constants\nexport const EXTENSION_CONFIG = {\n  ID: 'wusong8899-header-advertisement',\n  TRANSLATION_PREFIX: 'wusong8899-header-advertisement',\n  MAX_SLIDES: 30,\n  HEADER_ICON_URL: 'https://ex.cc/assets/files/date/test.png',\n} as const;\n", "import type { RootConfig, Environment } from './types';\r\nimport {\r\n  EXTENSION_CONFIG,\r\n  TIMING,\r\n  DOM_ELEMENTS,\r\n  CSS_CLASSES\r\n} from './constants';\r\n\r\nexport const defaultConfig: RootConfig = {\r\n  env: (process.env.NODE_ENV as Environment) || 'production',\r\n  app: {\r\n    extensionId: EXTENSION_CONFIG.ID,\r\n    translationPrefix: EXTENSION_CONFIG.TRANSLATION_PREFIX,\r\n  },\r\n  slider: {\r\n    maxSlides: EXTENSION_CONFIG.MAX_SLIDES,\r\n    defaultTransitionTime: TIMING.DEFAULT_TRANSITION_TIME,\r\n    checkTime: TIMING.CHECK_INTERVAL,\r\n    dataCheckInterval: TIMING.DATA_CHECK_INTERVAL,\r\n    dom: {\r\n      containerId: DOM_ELEMENTS.SPLIDE_AD_CONTAINER_ID,\r\n      splideClass: CSS_CLASSES.AD_SPLIDE,\r\n    },\r\n    splide: {\r\n      gap: '15px',\r\n      type: 'loop',\r\n      focus: 'center',\r\n      perPage: 1, // 显示1个完整幻灯片\r\n      pagination: true,\r\n      arrows: true,\r\n    },\r\n  },\r\n  ui: {\r\n    headerIconId: DOM_ELEMENTS.HEADER_ICON_ID,\r\n    headerIconUrl: EXTENSION_CONFIG.HEADER_ICON_URL,\r\n  },\r\n};\r\n", "import { defaultConfig } from '../../../common/config';\nimport { ARRAY_CONSTANTS } from '../../../common/config/constants';\nimport type {\n  ISplideConfigManager,\n  ConfigCalculationResult,\n  SplideFullConfig,\n  ResponsiveConfig,\n  SplideBreakpoints,\n  ValidationResult\n} from '../../../common/config/types';\n\n/**\n * Splide configuration constants\n * Based on Splide.js documentation requirements for loop mode\n */\nconst SPLIDE_CONFIG_CONSTANTS = {\n  MIN_SLIDES_FOR_LOOP: 2, // Splide requires minimum 2 slides for loop mode\n  // Responsive perPage values\n  MOBILE_PER_PAGE: 1,\n  TABLET_PER_PAGE: 1,\n  DESKTOP_PER_PAGE: 1,\n} as const;\n\n/**\n * Splide configuration manager\n * Handles all Splide configuration generation, validation, and responsive calculations\n */\nexport class SplideConfigManager implements ISplideConfigManager {\n  /**\n   * Calculate complete Splide configuration based on slide count and transition time\n   */\n  calculateConfiguration(slideCount: number, transitionTime: number): ConfigCalculationResult {\n    const enableLoop = this.shouldEnableLoop(slideCount);\n    const responsiveConfig = this.calculateResponsiveConfig();\n    const finalConfig = this.buildFinalConfig(transitionTime, enableLoop);\n\n    return {\n      enableLoop,\n      responsiveConfig,\n      finalConfig\n    };\n  }\n\n  /**\n   * Validate Splide configuration\n   */\n  validateConfiguration(config: SplideFullConfig): ValidationResult {\n    const errors: string[] = [];\n\n    // Validate required properties\n    if (config.autoplay && typeof config.autoplay === 'object' &&\n      (typeof config.autoplay.interval !== 'number' || config.autoplay.interval <= ARRAY_CONSTANTS.EMPTY_LENGTH)) {\n      errors.push('Invalid autoplay interval configuration');\n    }\n\n    if (typeof config.gap !== 'string' || !config.gap.match(/^\\d+px$/)) {\n      errors.push('Invalid gap configuration - must be in format \"Npx\"');\n    }\n\n    if (typeof config.perPage !== 'number' || config.perPage <= ARRAY_CONSTANTS.EMPTY_LENGTH) {\n      errors.push('Invalid perPage configuration');\n    }\n\n    // Validate breakpoints\n    if (config.breakpoints && typeof config.breakpoints === 'object') {\n      for (const [width, breakpointConfig] of Object.entries(config.breakpoints)) {\n        const widthNum = Number(width);\n        if (Number.isNaN(widthNum) || widthNum <= ARRAY_CONSTANTS.EMPTY_LENGTH) {\n          errors.push(`Invalid breakpoint width: ${width}`);\n        }\n        if (typeof breakpointConfig.perPage !== 'number' || breakpointConfig.perPage <= ARRAY_CONSTANTS.EMPTY_LENGTH) {\n          errors.push(`Invalid perPage for breakpoint ${width}`);\n        }\n        if (typeof breakpointConfig.gap !== 'string' || !breakpointConfig.gap.match(/^\\d+px$/)) {\n          errors.push(`Invalid gap for breakpoint ${width}`);\n        }\n      }\n    }\n\n    return {\n      isValid: errors.length === ARRAY_CONSTANTS.EMPTY_LENGTH,\n      errors\n    };\n  }\n\n  /**\n   * Determine if loop mode should be enabled based on slide count\n   */\n  private shouldEnableLoop(slideCount: number): boolean {\n    // Splide.js handles loop mode much better than Swiper.js\n    // Enable loop mode if we have enough slides\n    return slideCount >= SPLIDE_CONFIG_CONSTANTS.MIN_SLIDES_FOR_LOOP;\n  }\n\n  /**\n   * Calculate responsive configuration\n   */\n  private calculateResponsiveConfig(): ResponsiveConfig {\n    return {\n      mobile: {\n        perPage: SPLIDE_CONFIG_CONSTANTS.MOBILE_PER_PAGE,\n        gap: '10px'\n      },\n      tablet: {\n        perPage: SPLIDE_CONFIG_CONSTANTS.TABLET_PER_PAGE,\n        gap: '12px'\n      },\n      desktop: {\n        perPage: SPLIDE_CONFIG_CONSTANTS.DESKTOP_PER_PAGE,\n        gap: defaultConfig.slider.splide.gap\n      }\n    };\n  }\n\n  /**\n   * Build final Splide configuration object\n   */\n  private buildFinalConfig(\n    transitionTime: number,\n    enableLoop: boolean\n  ): SplideFullConfig {\n    const breakpoints: SplideBreakpoints = {\n      320: { perPage: SPLIDE_CONFIG_CONSTANTS.MOBILE_PER_PAGE, gap: '10px' },\n      768: { perPage: SPLIDE_CONFIG_CONSTANTS.TABLET_PER_PAGE, gap: '12px' },\n      1024: { perPage: SPLIDE_CONFIG_CONSTANTS.DESKTOP_PER_PAGE, gap: defaultConfig.slider.splide.gap }\n    };\n\n    let splideType: 'loop' | 'slide' = 'slide';\n    if (enableLoop) {\n      splideType = 'loop';\n    }\n\n    let autoplayConfig: { interval: number; pauseOnHover: boolean } | false = false;\n    if (transitionTime > ARRAY_CONSTANTS.EMPTY_LENGTH) {\n      autoplayConfig = {\n        interval: transitionTime,\n        pauseOnHover: true\n      };\n    }\n\n    return {\n      type: splideType,\n      autoplay: autoplayConfig,\n      gap: defaultConfig.slider.splide.gap,\n      focus: defaultConfig.slider.splide.focus,\n      perPage: defaultConfig.slider.splide.perPage,\n      breakpoints,\n      pagination: defaultConfig.slider.splide.pagination,\n      arrows: defaultConfig.slider.splide.arrows,\n      speed: 600,\n      // Ensure proper direction and accessibility\n      direction: 'ltr', // Left-to-right navigation\n      arrowPath: 'M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z', // Material Design arrow path\n      i18n: {\n        prev: 'Previous slide',\n        next: 'Next slide',\n        first: 'Go to first slide',\n        last: 'Go to last slide',\n        slideX: 'Go to slide %s',\n        pageX: 'Go to page %s',\n        play: 'Start autoplay',\n        pause: 'Pause autoplay'\n      }\n    };\n  }\n}\n", "import { MOBILE_DETECTION } from '../../common/config/constants';\n\n/**\n * Mobile detection utility functions\n */\n\n/**\n * Check if the current device is mobile\n */\nexport const isMobileDevice = (): boolean => {\n    try {\n        const { userAgent } = navigator;\n        const mobileIndicator = userAgent.substring(\n            MOBILE_DETECTION.USER_AGENT_SUBSTR_START,\n            MOBILE_DETECTION.USER_AGENT_SUBSTR_LENGTH\n        );\n        return mobileIndicator === 'Mobi';\n    } catch {\n        return false;\n    }\n};\n", "import * as DOMUtils from '../../utils/dom-utils';\nimport { isMobileDevice } from '../../utils/mobile-detection';\nimport { defaultConfig } from '../../../common/config';\nimport type { ISplideeDOMBuilder } from '../../../common/config/types';\n\n/**\n * Splide DOM builder\n * Handles all DOM creation and manipulation for Splide components\n */\nexport class SplideD<PERSON><PERSON>uilder implements ISplideeDOMBuilder {\n  private container: HTMLElement | undefined;\n\n  /**\n   * Create main container element\n   */\n  createContainer(): HTMLElement {\n    this.removeExistingNavigation();\n\n    const container = DOMUtils.createElement('div', {\n      id: defaultConfig.slider.dom.containerId,\n      className: 'adContainer adContainer--forced'\n    });\n\n    this.container = container;\n    return container;\n  }\n\n  /**\n   * Create Splide element\n   */\n  createSplideElement(container: HTMLElement): HTMLElement {\n    let className = `splide ${defaultConfig.slider.dom.splideClass} adSplide--forced`;\n\n    // Add mobile-specific class if on mobile device\n    if (isMobileDevice()) {\n      className += ' adSplide--mobile';\n    }\n\n    const splide = DOMUtils.createElement('div', {\n      className\n    });\n\n    DOMUtils.appendChild(container, splide);\n    return splide;\n  }\n\n  /**\n   * Create Splide track\n   */\n  createSplideTrack(splide: HTMLElement): HTMLElement {\n    const track = DOMUtils.createElement('div', {\n      className: 'splide__track splide__track--forced'\n    });\n\n    const list = DOMUtils.createElement('ul', {\n      className: 'splide__list splide__list--forced'\n    });\n\n    DOMUtils.appendChild(track, list);\n    DOMUtils.appendChild(splide, track);\n    return list;\n  }\n\n  /**\n   * Create individual slide\n   */\n  createSlide(imageSrc: string, imageLink: string): HTMLElement {\n    const slide = DOMUtils.createElement('li', {\n      className: 'splide__slide splide__slide--forced'\n    });\n\n    let clickHandler = '';\n    if (imageLink) {\n      clickHandler = `window.location.href=\"${imageLink}\"`;\n    }\n\n    // Create image with CSS class instead of inline styles\n    slide.innerHTML = `<img onclick='${clickHandler}' src='${imageSrc}' class='splide__slide__image' />`;\n\n    return slide;\n  }\n\n  /**\n   * Create pagination element\n   */\n  createPagination(splide: HTMLElement): void {\n    const pagination = DOMUtils.createElement('ul', {\n      className: 'splide__pagination'\n    });\n    DOMUtils.appendChild(splide, pagination);\n  }\n\n  /**\n   * Create navigation elements\n   * Note: This method is deprecated. Splide.js auto-generates arrows with proper SVG content\n   * when arrows: true is set in configuration and no manual arrows exist in DOM.\n   */\n  createNavigation(_splide: HTMLElement): void {\n    // Deprecated: Let Splide.js handle arrow creation automatically\n    // Manual arrow creation was causing issues because empty buttons were created\n    // without proper SVG content, making them invisible and non-functional\n\n    // const prevButton = DOMUtils.createElement('button', {\n    //   className: 'splide__arrow splide__arrow--prev splide__arrow--prev--custom'\n    // });\n    // const nextButton = DOMUtils.createElement('button', {\n    //   className: 'splide__arrow splide__arrow--next splide__arrow--next--custom'\n    // });\n\n    // DOMUtils.appendChild(splide, prevButton);\n    // DOMUtils.appendChild(splide, nextButton);\n  }\n\n  /**\n   * Append slideshow to DOM\n   */\n  appendToDOM(container: HTMLElement): void {\n    const contentContainer = DOMUtils.querySelector(\"#content .container\");\n    if (contentContainer) {\n      DOMUtils.prependChild(contentContainer, container);\n    }\n  }\n\n  /**\n   * Clean up DOM elements\n   */\n  cleanup(): void {\n    if (this.container) {\n      DOMUtils.removeElement(this.container);\n      delete this.container;\n    }\n  }\n\n  /**\n   * Get the current container element\n   */\n  getContainer(): HTMLElement | undefined {\n    return this.container;\n  }\n\n  /**\n   * Remove existing navigation elements\n   */\n  private removeExistingNavigation(): void {\n    const existingContainer = DOMUtils.querySelector(`#${defaultConfig.slider.dom.containerId}`);\n    if (existingContainer) {\n      DOMUtils.removeElement(existingContainer);\n    }\n\n    const navElements = DOMUtils.querySelectorAll(\".item-nav\");\n    for (const element of navElements) {\n      DOMUtils.removeElement(element);\n    }\n  }\n}\n", "import app from 'flarum/forum/app';\nimport { defaultConfig } from '../../../common/config';\nimport { SLIDESHOW_CONSTANTS, ARRAY_CONSTANTS } from '../../../common/config/constants';\nimport type {\n  ISlideDataManager,\n  ProcessedSlideData,\n  SlideDataRaw,\n  ValidationResult\n} from '../../../common/config/types';\n\n/**\n * Slide data manager\n * Handles fetching, processing, and validation of slide data from Flarum settings\n */\nexport class SlideDataManager implements ISlideDataManager {\n  private readonly maxSlides = defaultConfig.slider.maxSlides;\n\n  /**\n   * Fetch and process slide data from forum settings\n   */\n  fetchSlideData(): ProcessedSlideData[] {\n    const rawData: SlideDataRaw[] = [];\n\n    // Collect raw slide data from forum settings\n    for (let slideIndex = SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX; slideIndex <= this.maxSlides; slideIndex += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT) {\n      const imageSrc = this.getForumAttribute(`wusong8899-header-advertisement.Image${slideIndex}`);\n      const imageLink = this.getForumAttribute(`wusong8899-header-advertisement.Link${slideIndex}`);\n\n      if (imageSrc) {\n        rawData.push({\n          imageSrc: String(imageSrc),\n          imageLink: String(imageLink || ''),\n          slideIndex\n        });\n      }\n    }\n\n    // Process and validate the data\n    return this.processSlideData(rawData);\n  }\n\n  /**\n   * Validate slide data\n   */\n  validateSlideData(data: SlideDataRaw[]): ValidationResult {\n    const errors: string[] = [];\n\n    if (!Array.isArray(data)) {\n      errors.push('Slide data must be an array');\n      return { isValid: false, errors };\n    }\n\n    if (data.length === ARRAY_CONSTANTS.EMPTY_LENGTH) {\n      errors.push('No slide data provided');\n      return { isValid: false, errors };\n    }\n\n    for (const slide of data) {\n      if (!slide.imageSrc || typeof slide.imageSrc !== 'string') {\n        errors.push(`Invalid image source for slide ${slide.slideIndex}`);\n      }\n\n      if (slide.imageLink && typeof slide.imageLink !== 'string') {\n        errors.push(`Invalid image link for slide ${slide.slideIndex}`);\n      }\n\n      if (typeof slide.slideIndex !== 'number' || slide.slideIndex < SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX) {\n        errors.push(`Invalid slide index: ${slide.slideIndex}`);\n      }\n\n      // Basic URL validation for image source\n      if (slide.imageSrc && !this.isValidUrl(slide.imageSrc)) {\n        errors.push(`Invalid image URL format for slide ${slide.slideIndex}`);\n      }\n\n      // Basic URL validation for image link (if provided)\n      if (slide.imageLink && !this.isValidUrl(slide.imageLink)) {\n        errors.push(`Invalid link URL format for slide ${slide.slideIndex}`);\n      }\n    }\n\n    return {\n      isValid: errors.length === ARRAY_CONSTANTS.EMPTY_LENGTH,\n      errors\n    };\n  }\n\n  /**\n   * Get transition time from forum settings\n   */\n  getTransitionTime(): number {\n    const transitionTime = this.getForumAttribute('wusong8899-header-advertisement.TransitionTime');\n    if (transitionTime) {\n      const parsedTime = Number.parseInt(String(transitionTime), 10);\n      if (!Number.isNaN(parsedTime) && parsedTime > ARRAY_CONSTANTS.EMPTY_LENGTH) {\n        return parsedTime;\n      }\n    }\n    return defaultConfig.slider.defaultTransitionTime;\n  }\n\n  /**\n   * Process raw slide data into processed format\n   */\n  private processSlideData(rawData: SlideDataRaw[]): ProcessedSlideData[] {\n    return rawData.map((slide) => ({\n      imageSrc: slide.imageSrc,\n      imageLink: slide.imageLink,\n      slideIndex: slide.slideIndex,\n      isValid: this.isSlideValid(slide)\n    })).filter((slide) => slide.isValid);\n  }\n\n  /**\n   * Check if a single slide is valid\n   */\n  private isSlideValid(slide: SlideDataRaw): boolean {\n    return Boolean(\n      slide.imageSrc &&\n      typeof slide.imageSrc === 'string' &&\n      this.isValidUrl(slide.imageSrc) &&\n      typeof slide.slideIndex === 'number' &&\n      slide.slideIndex > ARRAY_CONSTANTS.EMPTY_LENGTH\n    );\n  }\n\n  /**\n   * Basic URL validation\n   */\n  private isValidUrl(url: string): boolean {\n    try {\n      const urlObject = new URL(url);\n      return Boolean(urlObject);\n    } catch {\n      // Check for relative URLs or data URLs\n      return url.startsWith('/') || url.startsWith('./') || url.startsWith('data:');\n    }\n  }\n\n  /**\n   * Safely read a forum attribute if available\n   */\n  private getForumAttribute(key: string): unknown {\n    try {\n      const forum = app && app.forum;\n      const attrFn = forum && forum.attribute;\n      if (typeof attrFn === 'function') {\n        return attrFn.call(forum, key);\n      }\n      return;\n    } catch {\n      return;\n    }\n  }\n}\n", "function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\n/*!\n * Splide.js\n * Version  : 4.1.4\n * License  : MIT\n * Copyright: 2022 Naotoshi Fujita\n */\nvar MEDIA_PREFERS_REDUCED_MOTION = \"(prefers-reduced-motion: reduce)\";\nvar CREATED = 1;\nvar MOUNTED = 2;\nvar IDLE = 3;\nvar MOVING = 4;\nvar SCROLLING = 5;\nvar DRAGGING = 6;\nvar DESTROYED = 7;\nvar STATES = {\n  CREATED: CREATED,\n  MOUNTED: MOUNTED,\n  IDLE: IDLE,\n  MOVING: MOVING,\n  SCROLLING: SCROLLING,\n  DRAGGING: DRAGGING,\n  DESTROYED: DESTROYED\n};\n\nfunction empty(array) {\n  array.length = 0;\n}\n\nfunction slice(arrayLike, start, end) {\n  return Array.prototype.slice.call(arrayLike, start, end);\n}\n\nfunction apply(func) {\n  return func.bind.apply(func, [null].concat(slice(arguments, 1)));\n}\n\nvar nextTick = setTimeout;\n\nvar noop = function noop() {};\n\nfunction raf(func) {\n  return requestAnimationFrame(func);\n}\n\nfunction typeOf(type, subject) {\n  return typeof subject === type;\n}\n\nfunction isObject(subject) {\n  return !isNull(subject) && typeOf(\"object\", subject);\n}\n\nvar isArray = Array.isArray;\nvar isFunction = apply(typeOf, \"function\");\nvar isString = apply(typeOf, \"string\");\nvar isUndefined = apply(typeOf, \"undefined\");\n\nfunction isNull(subject) {\n  return subject === null;\n}\n\nfunction isHTMLElement(subject) {\n  try {\n    return subject instanceof (subject.ownerDocument.defaultView || window).HTMLElement;\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction toArray(value) {\n  return isArray(value) ? value : [value];\n}\n\nfunction forEach(values, iteratee) {\n  toArray(values).forEach(iteratee);\n}\n\nfunction includes(array, value) {\n  return array.indexOf(value) > -1;\n}\n\nfunction push(array, items) {\n  array.push.apply(array, toArray(items));\n  return array;\n}\n\nfunction toggleClass(elm, classes, add) {\n  if (elm) {\n    forEach(classes, function (name) {\n      if (name) {\n        elm.classList[add ? \"add\" : \"remove\"](name);\n      }\n    });\n  }\n}\n\nfunction addClass(elm, classes) {\n  toggleClass(elm, isString(classes) ? classes.split(\" \") : classes, true);\n}\n\nfunction append(parent, children) {\n  forEach(children, parent.appendChild.bind(parent));\n}\n\nfunction before(nodes, ref) {\n  forEach(nodes, function (node) {\n    var parent = (ref || node).parentNode;\n\n    if (parent) {\n      parent.insertBefore(node, ref);\n    }\n  });\n}\n\nfunction matches(elm, selector) {\n  return isHTMLElement(elm) && (elm[\"msMatchesSelector\"] || elm.matches).call(elm, selector);\n}\n\nfunction children(parent, selector) {\n  var children2 = parent ? slice(parent.children) : [];\n  return selector ? children2.filter(function (child) {\n    return matches(child, selector);\n  }) : children2;\n}\n\nfunction child(parent, selector) {\n  return selector ? children(parent, selector)[0] : parent.firstElementChild;\n}\n\nvar ownKeys = Object.keys;\n\nfunction forOwn(object, iteratee, right) {\n  if (object) {\n    (right ? ownKeys(object).reverse() : ownKeys(object)).forEach(function (key) {\n      key !== \"__proto__\" && iteratee(object[key], key);\n    });\n  }\n\n  return object;\n}\n\nfunction assign(object) {\n  slice(arguments, 1).forEach(function (source) {\n    forOwn(source, function (value, key) {\n      object[key] = source[key];\n    });\n  });\n  return object;\n}\n\nfunction merge(object) {\n  slice(arguments, 1).forEach(function (source) {\n    forOwn(source, function (value, key) {\n      if (isArray(value)) {\n        object[key] = value.slice();\n      } else if (isObject(value)) {\n        object[key] = merge({}, isObject(object[key]) ? object[key] : {}, value);\n      } else {\n        object[key] = value;\n      }\n    });\n  });\n  return object;\n}\n\nfunction omit(object, keys) {\n  forEach(keys || ownKeys(object), function (key) {\n    delete object[key];\n  });\n}\n\nfunction removeAttribute(elms, attrs) {\n  forEach(elms, function (elm) {\n    forEach(attrs, function (attr) {\n      elm && elm.removeAttribute(attr);\n    });\n  });\n}\n\nfunction setAttribute(elms, attrs, value) {\n  if (isObject(attrs)) {\n    forOwn(attrs, function (value2, name) {\n      setAttribute(elms, name, value2);\n    });\n  } else {\n    forEach(elms, function (elm) {\n      isNull(value) || value === \"\" ? removeAttribute(elm, attrs) : elm.setAttribute(attrs, String(value));\n    });\n  }\n}\n\nfunction create(tag, attrs, parent) {\n  var elm = document.createElement(tag);\n\n  if (attrs) {\n    isString(attrs) ? addClass(elm, attrs) : setAttribute(elm, attrs);\n  }\n\n  parent && append(parent, elm);\n  return elm;\n}\n\nfunction style(elm, prop, value) {\n  if (isUndefined(value)) {\n    return getComputedStyle(elm)[prop];\n  }\n\n  if (!isNull(value)) {\n    elm.style[prop] = \"\" + value;\n  }\n}\n\nfunction display(elm, display2) {\n  style(elm, \"display\", display2);\n}\n\nfunction focus(elm) {\n  elm[\"setActive\"] && elm[\"setActive\"]() || elm.focus({\n    preventScroll: true\n  });\n}\n\nfunction getAttribute(elm, attr) {\n  return elm.getAttribute(attr);\n}\n\nfunction hasClass(elm, className) {\n  return elm && elm.classList.contains(className);\n}\n\nfunction rect(target) {\n  return target.getBoundingClientRect();\n}\n\nfunction remove(nodes) {\n  forEach(nodes, function (node) {\n    if (node && node.parentNode) {\n      node.parentNode.removeChild(node);\n    }\n  });\n}\n\nfunction parseHtml(html) {\n  return child(new DOMParser().parseFromString(html, \"text/html\").body);\n}\n\nfunction prevent(e, stopPropagation) {\n  e.preventDefault();\n\n  if (stopPropagation) {\n    e.stopPropagation();\n    e.stopImmediatePropagation();\n  }\n}\n\nfunction query(parent, selector) {\n  return parent && parent.querySelector(selector);\n}\n\nfunction queryAll(parent, selector) {\n  return selector ? slice(parent.querySelectorAll(selector)) : [];\n}\n\nfunction removeClass(elm, classes) {\n  toggleClass(elm, classes, false);\n}\n\nfunction timeOf(e) {\n  return e.timeStamp;\n}\n\nfunction unit(value) {\n  return isString(value) ? value : value ? value + \"px\" : \"\";\n}\n\nvar PROJECT_CODE = \"splide\";\nvar DATA_ATTRIBUTE = \"data-\" + PROJECT_CODE;\n\nfunction assert(condition, message) {\n  if (!condition) {\n    throw new Error(\"[\" + PROJECT_CODE + \"] \" + (message || \"\"));\n  }\n}\n\nvar min = Math.min,\n    max = Math.max,\n    floor = Math.floor,\n    ceil = Math.ceil,\n    abs = Math.abs;\n\nfunction approximatelyEqual(x, y, epsilon) {\n  return abs(x - y) < epsilon;\n}\n\nfunction between(number, x, y, exclusive) {\n  var minimum = min(x, y);\n  var maximum = max(x, y);\n  return exclusive ? minimum < number && number < maximum : minimum <= number && number <= maximum;\n}\n\nfunction clamp(number, x, y) {\n  var minimum = min(x, y);\n  var maximum = max(x, y);\n  return min(max(minimum, number), maximum);\n}\n\nfunction sign(x) {\n  return +(x > 0) - +(x < 0);\n}\n\nfunction camelToKebab(string) {\n  return string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\n}\n\nfunction format(string, replacements) {\n  forEach(replacements, function (replacement) {\n    string = string.replace(\"%s\", \"\" + replacement);\n  });\n  return string;\n}\n\nfunction pad(number) {\n  return number < 10 ? \"0\" + number : \"\" + number;\n}\n\nvar ids = {};\n\nfunction uniqueId(prefix) {\n  return \"\" + prefix + pad(ids[prefix] = (ids[prefix] || 0) + 1);\n}\n\nfunction EventBinder() {\n  var listeners = [];\n\n  function bind(targets, events, callback, options) {\n    forEachEvent(targets, events, function (target, event, namespace) {\n      var isEventTarget = (\"addEventListener\" in target);\n      var remover = isEventTarget ? target.removeEventListener.bind(target, event, callback, options) : target[\"removeListener\"].bind(target, callback);\n      isEventTarget ? target.addEventListener(event, callback, options) : target[\"addListener\"](callback);\n      listeners.push([target, event, namespace, callback, remover]);\n    });\n  }\n\n  function unbind(targets, events, callback) {\n    forEachEvent(targets, events, function (target, event, namespace) {\n      listeners = listeners.filter(function (listener) {\n        if (listener[0] === target && listener[1] === event && listener[2] === namespace && (!callback || listener[3] === callback)) {\n          listener[4]();\n          return false;\n        }\n\n        return true;\n      });\n    });\n  }\n\n  function dispatch(target, type, detail) {\n    var e;\n    var bubbles = true;\n\n    if (typeof CustomEvent === \"function\") {\n      e = new CustomEvent(type, {\n        bubbles: bubbles,\n        detail: detail\n      });\n    } else {\n      e = document.createEvent(\"CustomEvent\");\n      e.initCustomEvent(type, bubbles, false, detail);\n    }\n\n    target.dispatchEvent(e);\n    return e;\n  }\n\n  function forEachEvent(targets, events, iteratee) {\n    forEach(targets, function (target) {\n      target && forEach(events, function (events2) {\n        events2.split(\" \").forEach(function (eventNS) {\n          var fragment = eventNS.split(\".\");\n          iteratee(target, fragment[0], fragment[1]);\n        });\n      });\n    });\n  }\n\n  function destroy() {\n    listeners.forEach(function (data) {\n      data[4]();\n    });\n    empty(listeners);\n  }\n\n  return {\n    bind: bind,\n    unbind: unbind,\n    dispatch: dispatch,\n    destroy: destroy\n  };\n}\n\nvar EVENT_MOUNTED = \"mounted\";\nvar EVENT_READY = \"ready\";\nvar EVENT_MOVE = \"move\";\nvar EVENT_MOVED = \"moved\";\nvar EVENT_CLICK = \"click\";\nvar EVENT_ACTIVE = \"active\";\nvar EVENT_INACTIVE = \"inactive\";\nvar EVENT_VISIBLE = \"visible\";\nvar EVENT_HIDDEN = \"hidden\";\nvar EVENT_REFRESH = \"refresh\";\nvar EVENT_UPDATED = \"updated\";\nvar EVENT_RESIZE = \"resize\";\nvar EVENT_RESIZED = \"resized\";\nvar EVENT_DRAG = \"drag\";\nvar EVENT_DRAGGING = \"dragging\";\nvar EVENT_DRAGGED = \"dragged\";\nvar EVENT_SCROLL = \"scroll\";\nvar EVENT_SCROLLED = \"scrolled\";\nvar EVENT_OVERFLOW = \"overflow\";\nvar EVENT_DESTROY = \"destroy\";\nvar EVENT_ARROWS_MOUNTED = \"arrows:mounted\";\nvar EVENT_ARROWS_UPDATED = \"arrows:updated\";\nvar EVENT_PAGINATION_MOUNTED = \"pagination:mounted\";\nvar EVENT_PAGINATION_UPDATED = \"pagination:updated\";\nvar EVENT_NAVIGATION_MOUNTED = \"navigation:mounted\";\nvar EVENT_AUTOPLAY_PLAY = \"autoplay:play\";\nvar EVENT_AUTOPLAY_PLAYING = \"autoplay:playing\";\nvar EVENT_AUTOPLAY_PAUSE = \"autoplay:pause\";\nvar EVENT_LAZYLOAD_LOADED = \"lazyload:loaded\";\nvar EVENT_SLIDE_KEYDOWN = \"sk\";\nvar EVENT_SHIFTED = \"sh\";\nvar EVENT_END_INDEX_CHANGED = \"ei\";\n\nfunction EventInterface(Splide2) {\n  var bus = Splide2 ? Splide2.event.bus : document.createDocumentFragment();\n  var binder = EventBinder();\n\n  function on(events, callback) {\n    binder.bind(bus, toArray(events).join(\" \"), function (e) {\n      callback.apply(callback, isArray(e.detail) ? e.detail : []);\n    });\n  }\n\n  function emit(event) {\n    binder.dispatch(bus, event, slice(arguments, 1));\n  }\n\n  if (Splide2) {\n    Splide2.event.on(EVENT_DESTROY, binder.destroy);\n  }\n\n  return assign(binder, {\n    bus: bus,\n    on: on,\n    off: apply(binder.unbind, bus),\n    emit: emit\n  });\n}\n\nfunction RequestInterval(interval, onInterval, onUpdate, limit) {\n  var now = Date.now;\n  var startTime;\n  var rate = 0;\n  var id;\n  var paused = true;\n  var count = 0;\n\n  function update() {\n    if (!paused) {\n      rate = interval ? min((now() - startTime) / interval, 1) : 1;\n      onUpdate && onUpdate(rate);\n\n      if (rate >= 1) {\n        onInterval();\n        startTime = now();\n\n        if (limit && ++count >= limit) {\n          return pause();\n        }\n      }\n\n      id = raf(update);\n    }\n  }\n\n  function start(resume) {\n    resume || cancel();\n    startTime = now() - (resume ? rate * interval : 0);\n    paused = false;\n    id = raf(update);\n  }\n\n  function pause() {\n    paused = true;\n  }\n\n  function rewind() {\n    startTime = now();\n    rate = 0;\n\n    if (onUpdate) {\n      onUpdate(rate);\n    }\n  }\n\n  function cancel() {\n    id && cancelAnimationFrame(id);\n    rate = 0;\n    id = 0;\n    paused = true;\n  }\n\n  function set(time) {\n    interval = time;\n  }\n\n  function isPaused() {\n    return paused;\n  }\n\n  return {\n    start: start,\n    rewind: rewind,\n    pause: pause,\n    cancel: cancel,\n    set: set,\n    isPaused: isPaused\n  };\n}\n\nfunction State(initialState) {\n  var state = initialState;\n\n  function set(value) {\n    state = value;\n  }\n\n  function is(states) {\n    return includes(toArray(states), state);\n  }\n\n  return {\n    set: set,\n    is: is\n  };\n}\n\nfunction Throttle(func, duration) {\n  var interval = RequestInterval(duration || 0, func, null, 1);\n  return function () {\n    interval.isPaused() && interval.start();\n  };\n}\n\nfunction Media(Splide2, Components2, options) {\n  var state = Splide2.state;\n  var breakpoints = options.breakpoints || {};\n  var reducedMotion = options.reducedMotion || {};\n  var binder = EventBinder();\n  var queries = [];\n\n  function setup() {\n    var isMin = options.mediaQuery === \"min\";\n    ownKeys(breakpoints).sort(function (n, m) {\n      return isMin ? +n - +m : +m - +n;\n    }).forEach(function (key) {\n      register(breakpoints[key], \"(\" + (isMin ? \"min\" : \"max\") + \"-width:\" + key + \"px)\");\n    });\n    register(reducedMotion, MEDIA_PREFERS_REDUCED_MOTION);\n    update();\n  }\n\n  function destroy(completely) {\n    if (completely) {\n      binder.destroy();\n    }\n  }\n\n  function register(options2, query) {\n    var queryList = matchMedia(query);\n    binder.bind(queryList, \"change\", update);\n    queries.push([options2, queryList]);\n  }\n\n  function update() {\n    var destroyed = state.is(DESTROYED);\n    var direction = options.direction;\n    var merged = queries.reduce(function (merged2, entry) {\n      return merge(merged2, entry[1].matches ? entry[0] : {});\n    }, {});\n    omit(options);\n    set(merged);\n\n    if (options.destroy) {\n      Splide2.destroy(options.destroy === \"completely\");\n    } else if (destroyed) {\n      destroy(true);\n      Splide2.mount();\n    } else {\n      direction !== options.direction && Splide2.refresh();\n    }\n  }\n\n  function reduce(enable) {\n    if (matchMedia(MEDIA_PREFERS_REDUCED_MOTION).matches) {\n      enable ? merge(options, reducedMotion) : omit(options, ownKeys(reducedMotion));\n    }\n  }\n\n  function set(opts, base, notify) {\n    merge(options, opts);\n    base && merge(Object.getPrototypeOf(options), opts);\n\n    if (notify || !state.is(CREATED)) {\n      Splide2.emit(EVENT_UPDATED, options);\n    }\n  }\n\n  return {\n    setup: setup,\n    destroy: destroy,\n    reduce: reduce,\n    set: set\n  };\n}\n\nvar ARROW = \"Arrow\";\nvar ARROW_LEFT = ARROW + \"Left\";\nvar ARROW_RIGHT = ARROW + \"Right\";\nvar ARROW_UP = ARROW + \"Up\";\nvar ARROW_DOWN = ARROW + \"Down\";\nvar LTR = \"ltr\";\nvar RTL = \"rtl\";\nvar TTB = \"ttb\";\nvar ORIENTATION_MAP = {\n  width: [\"height\"],\n  left: [\"top\", \"right\"],\n  right: [\"bottom\", \"left\"],\n  x: [\"y\"],\n  X: [\"Y\"],\n  Y: [\"X\"],\n  ArrowLeft: [ARROW_UP, ARROW_RIGHT],\n  ArrowRight: [ARROW_DOWN, ARROW_LEFT]\n};\n\nfunction Direction(Splide2, Components2, options) {\n  function resolve(prop, axisOnly, direction) {\n    direction = direction || options.direction;\n    var index = direction === RTL && !axisOnly ? 1 : direction === TTB ? 0 : -1;\n    return ORIENTATION_MAP[prop] && ORIENTATION_MAP[prop][index] || prop.replace(/width|left|right/i, function (match, offset) {\n      var replacement = ORIENTATION_MAP[match.toLowerCase()][index] || match;\n      return offset > 0 ? replacement.charAt(0).toUpperCase() + replacement.slice(1) : replacement;\n    });\n  }\n\n  function orient(value) {\n    return value * (options.direction === RTL ? 1 : -1);\n  }\n\n  return {\n    resolve: resolve,\n    orient: orient\n  };\n}\n\nvar ROLE = \"role\";\nvar TAB_INDEX = \"tabindex\";\nvar DISABLED = \"disabled\";\nvar ARIA_PREFIX = \"aria-\";\nvar ARIA_CONTROLS = ARIA_PREFIX + \"controls\";\nvar ARIA_CURRENT = ARIA_PREFIX + \"current\";\nvar ARIA_SELECTED = ARIA_PREFIX + \"selected\";\nvar ARIA_LABEL = ARIA_PREFIX + \"label\";\nvar ARIA_LABELLEDBY = ARIA_PREFIX + \"labelledby\";\nvar ARIA_HIDDEN = ARIA_PREFIX + \"hidden\";\nvar ARIA_ORIENTATION = ARIA_PREFIX + \"orientation\";\nvar ARIA_ROLEDESCRIPTION = ARIA_PREFIX + \"roledescription\";\nvar ARIA_LIVE = ARIA_PREFIX + \"live\";\nvar ARIA_BUSY = ARIA_PREFIX + \"busy\";\nvar ARIA_ATOMIC = ARIA_PREFIX + \"atomic\";\nvar ALL_ATTRIBUTES = [ROLE, TAB_INDEX, DISABLED, ARIA_CONTROLS, ARIA_CURRENT, ARIA_LABEL, ARIA_LABELLEDBY, ARIA_HIDDEN, ARIA_ORIENTATION, ARIA_ROLEDESCRIPTION];\nvar CLASS_PREFIX = PROJECT_CODE + \"__\";\nvar STATUS_CLASS_PREFIX = \"is-\";\nvar CLASS_ROOT = PROJECT_CODE;\nvar CLASS_TRACK = CLASS_PREFIX + \"track\";\nvar CLASS_LIST = CLASS_PREFIX + \"list\";\nvar CLASS_SLIDE = CLASS_PREFIX + \"slide\";\nvar CLASS_CLONE = CLASS_SLIDE + \"--clone\";\nvar CLASS_CONTAINER = CLASS_SLIDE + \"__container\";\nvar CLASS_ARROWS = CLASS_PREFIX + \"arrows\";\nvar CLASS_ARROW = CLASS_PREFIX + \"arrow\";\nvar CLASS_ARROW_PREV = CLASS_ARROW + \"--prev\";\nvar CLASS_ARROW_NEXT = CLASS_ARROW + \"--next\";\nvar CLASS_PAGINATION = CLASS_PREFIX + \"pagination\";\nvar CLASS_PAGINATION_PAGE = CLASS_PAGINATION + \"__page\";\nvar CLASS_PROGRESS = CLASS_PREFIX + \"progress\";\nvar CLASS_PROGRESS_BAR = CLASS_PROGRESS + \"__bar\";\nvar CLASS_TOGGLE = CLASS_PREFIX + \"toggle\";\nvar CLASS_TOGGLE_PLAY = CLASS_TOGGLE + \"__play\";\nvar CLASS_TOGGLE_PAUSE = CLASS_TOGGLE + \"__pause\";\nvar CLASS_SPINNER = CLASS_PREFIX + \"spinner\";\nvar CLASS_SR = CLASS_PREFIX + \"sr\";\nvar CLASS_INITIALIZED = STATUS_CLASS_PREFIX + \"initialized\";\nvar CLASS_ACTIVE = STATUS_CLASS_PREFIX + \"active\";\nvar CLASS_PREV = STATUS_CLASS_PREFIX + \"prev\";\nvar CLASS_NEXT = STATUS_CLASS_PREFIX + \"next\";\nvar CLASS_VISIBLE = STATUS_CLASS_PREFIX + \"visible\";\nvar CLASS_LOADING = STATUS_CLASS_PREFIX + \"loading\";\nvar CLASS_FOCUS_IN = STATUS_CLASS_PREFIX + \"focus-in\";\nvar CLASS_OVERFLOW = STATUS_CLASS_PREFIX + \"overflow\";\nvar STATUS_CLASSES = [CLASS_ACTIVE, CLASS_VISIBLE, CLASS_PREV, CLASS_NEXT, CLASS_LOADING, CLASS_FOCUS_IN, CLASS_OVERFLOW];\nvar CLASSES = {\n  slide: CLASS_SLIDE,\n  clone: CLASS_CLONE,\n  arrows: CLASS_ARROWS,\n  arrow: CLASS_ARROW,\n  prev: CLASS_ARROW_PREV,\n  next: CLASS_ARROW_NEXT,\n  pagination: CLASS_PAGINATION,\n  page: CLASS_PAGINATION_PAGE,\n  spinner: CLASS_SPINNER\n};\n\nfunction closest(from, selector) {\n  if (isFunction(from.closest)) {\n    return from.closest(selector);\n  }\n\n  var elm = from;\n\n  while (elm && elm.nodeType === 1) {\n    if (matches(elm, selector)) {\n      break;\n    }\n\n    elm = elm.parentElement;\n  }\n\n  return elm;\n}\n\nvar FRICTION = 5;\nvar LOG_INTERVAL = 200;\nvar POINTER_DOWN_EVENTS = \"touchstart mousedown\";\nvar POINTER_MOVE_EVENTS = \"touchmove mousemove\";\nvar POINTER_UP_EVENTS = \"touchend touchcancel mouseup click\";\n\nfunction Elements(Splide2, Components2, options) {\n  var _EventInterface = EventInterface(Splide2),\n      on = _EventInterface.on,\n      bind = _EventInterface.bind;\n\n  var root = Splide2.root;\n  var i18n = options.i18n;\n  var elements = {};\n  var slides = [];\n  var rootClasses = [];\n  var trackClasses = [];\n  var track;\n  var list;\n  var isUsingKey;\n\n  function setup() {\n    collect();\n    init();\n    update();\n  }\n\n  function mount() {\n    on(EVENT_REFRESH, destroy);\n    on(EVENT_REFRESH, setup);\n    on(EVENT_UPDATED, update);\n    bind(document, POINTER_DOWN_EVENTS + \" keydown\", function (e) {\n      isUsingKey = e.type === \"keydown\";\n    }, {\n      capture: true\n    });\n    bind(root, \"focusin\", function () {\n      toggleClass(root, CLASS_FOCUS_IN, !!isUsingKey);\n    });\n  }\n\n  function destroy(completely) {\n    var attrs = ALL_ATTRIBUTES.concat(\"style\");\n    empty(slides);\n    removeClass(root, rootClasses);\n    removeClass(track, trackClasses);\n    removeAttribute([track, list], attrs);\n    removeAttribute(root, completely ? attrs : [\"style\", ARIA_ROLEDESCRIPTION]);\n  }\n\n  function update() {\n    removeClass(root, rootClasses);\n    removeClass(track, trackClasses);\n    rootClasses = getClasses(CLASS_ROOT);\n    trackClasses = getClasses(CLASS_TRACK);\n    addClass(root, rootClasses);\n    addClass(track, trackClasses);\n    setAttribute(root, ARIA_LABEL, options.label);\n    setAttribute(root, ARIA_LABELLEDBY, options.labelledby);\n  }\n\n  function collect() {\n    track = find(\".\" + CLASS_TRACK);\n    list = child(track, \".\" + CLASS_LIST);\n    assert(track && list, \"A track/list element is missing.\");\n    push(slides, children(list, \".\" + CLASS_SLIDE + \":not(.\" + CLASS_CLONE + \")\"));\n    forOwn({\n      arrows: CLASS_ARROWS,\n      pagination: CLASS_PAGINATION,\n      prev: CLASS_ARROW_PREV,\n      next: CLASS_ARROW_NEXT,\n      bar: CLASS_PROGRESS_BAR,\n      toggle: CLASS_TOGGLE\n    }, function (className, key) {\n      elements[key] = find(\".\" + className);\n    });\n    assign(elements, {\n      root: root,\n      track: track,\n      list: list,\n      slides: slides\n    });\n  }\n\n  function init() {\n    var id = root.id || uniqueId(PROJECT_CODE);\n    var role = options.role;\n    root.id = id;\n    track.id = track.id || id + \"-track\";\n    list.id = list.id || id + \"-list\";\n\n    if (!getAttribute(root, ROLE) && root.tagName !== \"SECTION\" && role) {\n      setAttribute(root, ROLE, role);\n    }\n\n    setAttribute(root, ARIA_ROLEDESCRIPTION, i18n.carousel);\n    setAttribute(list, ROLE, \"presentation\");\n  }\n\n  function find(selector) {\n    var elm = query(root, selector);\n    return elm && closest(elm, \".\" + CLASS_ROOT) === root ? elm : void 0;\n  }\n\n  function getClasses(base) {\n    return [base + \"--\" + options.type, base + \"--\" + options.direction, options.drag && base + \"--draggable\", options.isNavigation && base + \"--nav\", base === CLASS_ROOT && CLASS_ACTIVE];\n  }\n\n  return assign(elements, {\n    setup: setup,\n    mount: mount,\n    destroy: destroy\n  });\n}\n\nvar SLIDE = \"slide\";\nvar LOOP = \"loop\";\nvar FADE = \"fade\";\n\nfunction Slide$1(Splide2, index, slideIndex, slide) {\n  var event = EventInterface(Splide2);\n  var on = event.on,\n      emit = event.emit,\n      bind = event.bind;\n  var Components = Splide2.Components,\n      root = Splide2.root,\n      options = Splide2.options;\n  var isNavigation = options.isNavigation,\n      updateOnMove = options.updateOnMove,\n      i18n = options.i18n,\n      pagination = options.pagination,\n      slideFocus = options.slideFocus;\n  var resolve = Components.Direction.resolve;\n  var styles = getAttribute(slide, \"style\");\n  var label = getAttribute(slide, ARIA_LABEL);\n  var isClone = slideIndex > -1;\n  var container = child(slide, \".\" + CLASS_CONTAINER);\n  var destroyed;\n\n  function mount() {\n    if (!isClone) {\n      slide.id = root.id + \"-slide\" + pad(index + 1);\n      setAttribute(slide, ROLE, pagination ? \"tabpanel\" : \"group\");\n      setAttribute(slide, ARIA_ROLEDESCRIPTION, i18n.slide);\n      setAttribute(slide, ARIA_LABEL, label || format(i18n.slideLabel, [index + 1, Splide2.length]));\n    }\n\n    listen();\n  }\n\n  function listen() {\n    bind(slide, \"click\", apply(emit, EVENT_CLICK, self));\n    bind(slide, \"keydown\", apply(emit, EVENT_SLIDE_KEYDOWN, self));\n    on([EVENT_MOVED, EVENT_SHIFTED, EVENT_SCROLLED], update);\n    on(EVENT_NAVIGATION_MOUNTED, initNavigation);\n\n    if (updateOnMove) {\n      on(EVENT_MOVE, onMove);\n    }\n  }\n\n  function destroy() {\n    destroyed = true;\n    event.destroy();\n    removeClass(slide, STATUS_CLASSES);\n    removeAttribute(slide, ALL_ATTRIBUTES);\n    setAttribute(slide, \"style\", styles);\n    setAttribute(slide, ARIA_LABEL, label || \"\");\n  }\n\n  function initNavigation() {\n    var controls = Splide2.splides.map(function (target) {\n      var Slide2 = target.splide.Components.Slides.getAt(index);\n      return Slide2 ? Slide2.slide.id : \"\";\n    }).join(\" \");\n    setAttribute(slide, ARIA_LABEL, format(i18n.slideX, (isClone ? slideIndex : index) + 1));\n    setAttribute(slide, ARIA_CONTROLS, controls);\n    setAttribute(slide, ROLE, slideFocus ? \"button\" : \"\");\n    slideFocus && removeAttribute(slide, ARIA_ROLEDESCRIPTION);\n  }\n\n  function onMove() {\n    if (!destroyed) {\n      update();\n    }\n  }\n\n  function update() {\n    if (!destroyed) {\n      var curr = Splide2.index;\n      updateActivity();\n      updateVisibility();\n      toggleClass(slide, CLASS_PREV, index === curr - 1);\n      toggleClass(slide, CLASS_NEXT, index === curr + 1);\n    }\n  }\n\n  function updateActivity() {\n    var active = isActive();\n\n    if (active !== hasClass(slide, CLASS_ACTIVE)) {\n      toggleClass(slide, CLASS_ACTIVE, active);\n      setAttribute(slide, ARIA_CURRENT, isNavigation && active || \"\");\n      emit(active ? EVENT_ACTIVE : EVENT_INACTIVE, self);\n    }\n  }\n\n  function updateVisibility() {\n    var visible = isVisible();\n    var hidden = !visible && (!isActive() || isClone);\n\n    if (!Splide2.state.is([MOVING, SCROLLING])) {\n      setAttribute(slide, ARIA_HIDDEN, hidden || \"\");\n    }\n\n    setAttribute(queryAll(slide, options.focusableNodes || \"\"), TAB_INDEX, hidden ? -1 : \"\");\n\n    if (slideFocus) {\n      setAttribute(slide, TAB_INDEX, hidden ? -1 : 0);\n    }\n\n    if (visible !== hasClass(slide, CLASS_VISIBLE)) {\n      toggleClass(slide, CLASS_VISIBLE, visible);\n      emit(visible ? EVENT_VISIBLE : EVENT_HIDDEN, self);\n    }\n\n    if (!visible && document.activeElement === slide) {\n      var Slide2 = Components.Slides.getAt(Splide2.index);\n      Slide2 && focus(Slide2.slide);\n    }\n  }\n\n  function style$1(prop, value, useContainer) {\n    style(useContainer && container || slide, prop, value);\n  }\n\n  function isActive() {\n    var curr = Splide2.index;\n    return curr === index || options.cloneStatus && curr === slideIndex;\n  }\n\n  function isVisible() {\n    if (Splide2.is(FADE)) {\n      return isActive();\n    }\n\n    var trackRect = rect(Components.Elements.track);\n    var slideRect = rect(slide);\n    var left = resolve(\"left\", true);\n    var right = resolve(\"right\", true);\n    return floor(trackRect[left]) <= ceil(slideRect[left]) && floor(slideRect[right]) <= ceil(trackRect[right]);\n  }\n\n  function isWithin(from, distance) {\n    var diff = abs(from - index);\n\n    if (!isClone && (options.rewind || Splide2.is(LOOP))) {\n      diff = min(diff, Splide2.length - diff);\n    }\n\n    return diff <= distance;\n  }\n\n  var self = {\n    index: index,\n    slideIndex: slideIndex,\n    slide: slide,\n    container: container,\n    isClone: isClone,\n    mount: mount,\n    destroy: destroy,\n    update: update,\n    style: style$1,\n    isWithin: isWithin\n  };\n  return self;\n}\n\nfunction Slides(Splide2, Components2, options) {\n  var _EventInterface2 = EventInterface(Splide2),\n      on = _EventInterface2.on,\n      emit = _EventInterface2.emit,\n      bind = _EventInterface2.bind;\n\n  var _Components2$Elements = Components2.Elements,\n      slides = _Components2$Elements.slides,\n      list = _Components2$Elements.list;\n  var Slides2 = [];\n\n  function mount() {\n    init();\n    on(EVENT_REFRESH, destroy);\n    on(EVENT_REFRESH, init);\n  }\n\n  function init() {\n    slides.forEach(function (slide, index) {\n      register(slide, index, -1);\n    });\n  }\n\n  function destroy() {\n    forEach$1(function (Slide2) {\n      Slide2.destroy();\n    });\n    empty(Slides2);\n  }\n\n  function update() {\n    forEach$1(function (Slide2) {\n      Slide2.update();\n    });\n  }\n\n  function register(slide, index, slideIndex) {\n    var object = Slide$1(Splide2, index, slideIndex, slide);\n    object.mount();\n    Slides2.push(object);\n    Slides2.sort(function (Slide1, Slide2) {\n      return Slide1.index - Slide2.index;\n    });\n  }\n\n  function get(excludeClones) {\n    return excludeClones ? filter(function (Slide2) {\n      return !Slide2.isClone;\n    }) : Slides2;\n  }\n\n  function getIn(page) {\n    var Controller = Components2.Controller;\n    var index = Controller.toIndex(page);\n    var max = Controller.hasFocus() ? 1 : options.perPage;\n    return filter(function (Slide2) {\n      return between(Slide2.index, index, index + max - 1);\n    });\n  }\n\n  function getAt(index) {\n    return filter(index)[0];\n  }\n\n  function add(items, index) {\n    forEach(items, function (slide) {\n      if (isString(slide)) {\n        slide = parseHtml(slide);\n      }\n\n      if (isHTMLElement(slide)) {\n        var ref = slides[index];\n        ref ? before(slide, ref) : append(list, slide);\n        addClass(slide, options.classes.slide);\n        observeImages(slide, apply(emit, EVENT_RESIZE));\n      }\n    });\n    emit(EVENT_REFRESH);\n  }\n\n  function remove$1(matcher) {\n    remove(filter(matcher).map(function (Slide2) {\n      return Slide2.slide;\n    }));\n    emit(EVENT_REFRESH);\n  }\n\n  function forEach$1(iteratee, excludeClones) {\n    get(excludeClones).forEach(iteratee);\n  }\n\n  function filter(matcher) {\n    return Slides2.filter(isFunction(matcher) ? matcher : function (Slide2) {\n      return isString(matcher) ? matches(Slide2.slide, matcher) : includes(toArray(matcher), Slide2.index);\n    });\n  }\n\n  function style(prop, value, useContainer) {\n    forEach$1(function (Slide2) {\n      Slide2.style(prop, value, useContainer);\n    });\n  }\n\n  function observeImages(elm, callback) {\n    var images = queryAll(elm, \"img\");\n    var length = images.length;\n\n    if (length) {\n      images.forEach(function (img) {\n        bind(img, \"load error\", function () {\n          if (! --length) {\n            callback();\n          }\n        });\n      });\n    } else {\n      callback();\n    }\n  }\n\n  function getLength(excludeClones) {\n    return excludeClones ? slides.length : Slides2.length;\n  }\n\n  function isEnough() {\n    return Slides2.length > options.perPage;\n  }\n\n  return {\n    mount: mount,\n    destroy: destroy,\n    update: update,\n    register: register,\n    get: get,\n    getIn: getIn,\n    getAt: getAt,\n    add: add,\n    remove: remove$1,\n    forEach: forEach$1,\n    filter: filter,\n    style: style,\n    getLength: getLength,\n    isEnough: isEnough\n  };\n}\n\nfunction Layout(Splide2, Components2, options) {\n  var _EventInterface3 = EventInterface(Splide2),\n      on = _EventInterface3.on,\n      bind = _EventInterface3.bind,\n      emit = _EventInterface3.emit;\n\n  var Slides = Components2.Slides;\n  var resolve = Components2.Direction.resolve;\n  var _Components2$Elements2 = Components2.Elements,\n      root = _Components2$Elements2.root,\n      track = _Components2$Elements2.track,\n      list = _Components2$Elements2.list;\n  var getAt = Slides.getAt,\n      styleSlides = Slides.style;\n  var vertical;\n  var rootRect;\n  var overflow;\n\n  function mount() {\n    init();\n    bind(window, \"resize load\", Throttle(apply(emit, EVENT_RESIZE)));\n    on([EVENT_UPDATED, EVENT_REFRESH], init);\n    on(EVENT_RESIZE, resize);\n  }\n\n  function init() {\n    vertical = options.direction === TTB;\n    style(root, \"maxWidth\", unit(options.width));\n    style(track, resolve(\"paddingLeft\"), cssPadding(false));\n    style(track, resolve(\"paddingRight\"), cssPadding(true));\n    resize(true);\n  }\n\n  function resize(force) {\n    var newRect = rect(root);\n\n    if (force || rootRect.width !== newRect.width || rootRect.height !== newRect.height) {\n      style(track, \"height\", cssTrackHeight());\n      styleSlides(resolve(\"marginRight\"), unit(options.gap));\n      styleSlides(\"width\", cssSlideWidth());\n      styleSlides(\"height\", cssSlideHeight(), true);\n      rootRect = newRect;\n      emit(EVENT_RESIZED);\n\n      if (overflow !== (overflow = isOverflow())) {\n        toggleClass(root, CLASS_OVERFLOW, overflow);\n        emit(EVENT_OVERFLOW, overflow);\n      }\n    }\n  }\n\n  function cssPadding(right) {\n    var padding = options.padding;\n    var prop = resolve(right ? \"right\" : \"left\");\n    return padding && unit(padding[prop] || (isObject(padding) ? 0 : padding)) || \"0px\";\n  }\n\n  function cssTrackHeight() {\n    var height = \"\";\n\n    if (vertical) {\n      height = cssHeight();\n      assert(height, \"height or heightRatio is missing.\");\n      height = \"calc(\" + height + \" - \" + cssPadding(false) + \" - \" + cssPadding(true) + \")\";\n    }\n\n    return height;\n  }\n\n  function cssHeight() {\n    return unit(options.height || rect(list).width * options.heightRatio);\n  }\n\n  function cssSlideWidth() {\n    return options.autoWidth ? null : unit(options.fixedWidth) || (vertical ? \"\" : cssSlideSize());\n  }\n\n  function cssSlideHeight() {\n    return unit(options.fixedHeight) || (vertical ? options.autoHeight ? null : cssSlideSize() : cssHeight());\n  }\n\n  function cssSlideSize() {\n    var gap = unit(options.gap);\n    return \"calc((100%\" + (gap && \" + \" + gap) + \")/\" + (options.perPage || 1) + (gap && \" - \" + gap) + \")\";\n  }\n\n  function listSize() {\n    return rect(list)[resolve(\"width\")];\n  }\n\n  function slideSize(index, withoutGap) {\n    var Slide = getAt(index || 0);\n    return Slide ? rect(Slide.slide)[resolve(\"width\")] + (withoutGap ? 0 : getGap()) : 0;\n  }\n\n  function totalSize(index, withoutGap) {\n    var Slide = getAt(index);\n\n    if (Slide) {\n      var right = rect(Slide.slide)[resolve(\"right\")];\n      var left = rect(list)[resolve(\"left\")];\n      return abs(right - left) + (withoutGap ? 0 : getGap());\n    }\n\n    return 0;\n  }\n\n  function sliderSize(withoutGap) {\n    return totalSize(Splide2.length - 1) - totalSize(0) + slideSize(0, withoutGap);\n  }\n\n  function getGap() {\n    var Slide = getAt(0);\n    return Slide && parseFloat(style(Slide.slide, resolve(\"marginRight\"))) || 0;\n  }\n\n  function getPadding(right) {\n    return parseFloat(style(track, resolve(\"padding\" + (right ? \"Right\" : \"Left\")))) || 0;\n  }\n\n  function isOverflow() {\n    return Splide2.is(FADE) || sliderSize(true) > listSize();\n  }\n\n  return {\n    mount: mount,\n    resize: resize,\n    listSize: listSize,\n    slideSize: slideSize,\n    sliderSize: sliderSize,\n    totalSize: totalSize,\n    getPadding: getPadding,\n    isOverflow: isOverflow\n  };\n}\n\nvar MULTIPLIER = 2;\n\nfunction Clones(Splide2, Components2, options) {\n  var event = EventInterface(Splide2);\n  var on = event.on;\n  var Elements = Components2.Elements,\n      Slides = Components2.Slides;\n  var resolve = Components2.Direction.resolve;\n  var clones = [];\n  var cloneCount;\n\n  function mount() {\n    on(EVENT_REFRESH, remount);\n    on([EVENT_UPDATED, EVENT_RESIZE], observe);\n\n    if (cloneCount = computeCloneCount()) {\n      generate(cloneCount);\n      Components2.Layout.resize(true);\n    }\n  }\n\n  function remount() {\n    destroy();\n    mount();\n  }\n\n  function destroy() {\n    remove(clones);\n    empty(clones);\n    event.destroy();\n  }\n\n  function observe() {\n    var count = computeCloneCount();\n\n    if (cloneCount !== count) {\n      if (cloneCount < count || !count) {\n        event.emit(EVENT_REFRESH);\n      }\n    }\n  }\n\n  function generate(count) {\n    var slides = Slides.get().slice();\n    var length = slides.length;\n\n    if (length) {\n      while (slides.length < count) {\n        push(slides, slides);\n      }\n\n      push(slides.slice(-count), slides.slice(0, count)).forEach(function (Slide, index) {\n        var isHead = index < count;\n        var clone = cloneDeep(Slide.slide, index);\n        isHead ? before(clone, slides[0].slide) : append(Elements.list, clone);\n        push(clones, clone);\n        Slides.register(clone, index - count + (isHead ? 0 : length), Slide.index);\n      });\n    }\n  }\n\n  function cloneDeep(elm, index) {\n    var clone = elm.cloneNode(true);\n    addClass(clone, options.classes.clone);\n    clone.id = Splide2.root.id + \"-clone\" + pad(index + 1);\n    return clone;\n  }\n\n  function computeCloneCount() {\n    var clones2 = options.clones;\n\n    if (!Splide2.is(LOOP)) {\n      clones2 = 0;\n    } else if (isUndefined(clones2)) {\n      var fixedSize = options[resolve(\"fixedWidth\")] && Components2.Layout.slideSize(0);\n      var fixedCount = fixedSize && ceil(rect(Elements.track)[resolve(\"width\")] / fixedSize);\n      clones2 = fixedCount || options[resolve(\"autoWidth\")] && Splide2.length || options.perPage * MULTIPLIER;\n    }\n\n    return clones2;\n  }\n\n  return {\n    mount: mount,\n    destroy: destroy\n  };\n}\n\nfunction Move(Splide2, Components2, options) {\n  var _EventInterface4 = EventInterface(Splide2),\n      on = _EventInterface4.on,\n      emit = _EventInterface4.emit;\n\n  var set = Splide2.state.set;\n  var _Components2$Layout = Components2.Layout,\n      slideSize = _Components2$Layout.slideSize,\n      getPadding = _Components2$Layout.getPadding,\n      totalSize = _Components2$Layout.totalSize,\n      listSize = _Components2$Layout.listSize,\n      sliderSize = _Components2$Layout.sliderSize;\n  var _Components2$Directio = Components2.Direction,\n      resolve = _Components2$Directio.resolve,\n      orient = _Components2$Directio.orient;\n  var _Components2$Elements3 = Components2.Elements,\n      list = _Components2$Elements3.list,\n      track = _Components2$Elements3.track;\n  var Transition;\n\n  function mount() {\n    Transition = Components2.Transition;\n    on([EVENT_MOUNTED, EVENT_RESIZED, EVENT_UPDATED, EVENT_REFRESH], reposition);\n  }\n\n  function reposition() {\n    if (!Components2.Controller.isBusy()) {\n      Components2.Scroll.cancel();\n      jump(Splide2.index);\n      Components2.Slides.update();\n    }\n  }\n\n  function move(dest, index, prev, callback) {\n    if (dest !== index && canShift(dest > prev)) {\n      cancel();\n      translate(shift(getPosition(), dest > prev), true);\n    }\n\n    set(MOVING);\n    emit(EVENT_MOVE, index, prev, dest);\n    Transition.start(index, function () {\n      set(IDLE);\n      emit(EVENT_MOVED, index, prev, dest);\n      callback && callback();\n    });\n  }\n\n  function jump(index) {\n    translate(toPosition(index, true));\n  }\n\n  function translate(position, preventLoop) {\n    if (!Splide2.is(FADE)) {\n      var destination = preventLoop ? position : loop(position);\n      style(list, \"transform\", \"translate\" + resolve(\"X\") + \"(\" + destination + \"px)\");\n      position !== destination && emit(EVENT_SHIFTED);\n    }\n  }\n\n  function loop(position) {\n    if (Splide2.is(LOOP)) {\n      var index = toIndex(position);\n      var exceededMax = index > Components2.Controller.getEnd();\n      var exceededMin = index < 0;\n\n      if (exceededMin || exceededMax) {\n        position = shift(position, exceededMax);\n      }\n    }\n\n    return position;\n  }\n\n  function shift(position, backwards) {\n    var excess = position - getLimit(backwards);\n    var size = sliderSize();\n    position -= orient(size * (ceil(abs(excess) / size) || 1)) * (backwards ? 1 : -1);\n    return position;\n  }\n\n  function cancel() {\n    translate(getPosition(), true);\n    Transition.cancel();\n  }\n\n  function toIndex(position) {\n    var Slides = Components2.Slides.get();\n    var index = 0;\n    var minDistance = Infinity;\n\n    for (var i = 0; i < Slides.length; i++) {\n      var slideIndex = Slides[i].index;\n      var distance = abs(toPosition(slideIndex, true) - position);\n\n      if (distance <= minDistance) {\n        minDistance = distance;\n        index = slideIndex;\n      } else {\n        break;\n      }\n    }\n\n    return index;\n  }\n\n  function toPosition(index, trimming) {\n    var position = orient(totalSize(index - 1) - offset(index));\n    return trimming ? trim(position) : position;\n  }\n\n  function getPosition() {\n    var left = resolve(\"left\");\n    return rect(list)[left] - rect(track)[left] + orient(getPadding(false));\n  }\n\n  function trim(position) {\n    if (options.trimSpace && Splide2.is(SLIDE)) {\n      position = clamp(position, 0, orient(sliderSize(true) - listSize()));\n    }\n\n    return position;\n  }\n\n  function offset(index) {\n    var focus = options.focus;\n    return focus === \"center\" ? (listSize() - slideSize(index, true)) / 2 : +focus * slideSize(index) || 0;\n  }\n\n  function getLimit(max) {\n    return toPosition(max ? Components2.Controller.getEnd() : 0, !!options.trimSpace);\n  }\n\n  function canShift(backwards) {\n    var shifted = orient(shift(getPosition(), backwards));\n    return backwards ? shifted >= 0 : shifted <= list[resolve(\"scrollWidth\")] - rect(track)[resolve(\"width\")];\n  }\n\n  function exceededLimit(max, position) {\n    position = isUndefined(position) ? getPosition() : position;\n    var exceededMin = max !== true && orient(position) < orient(getLimit(false));\n    var exceededMax = max !== false && orient(position) > orient(getLimit(true));\n    return exceededMin || exceededMax;\n  }\n\n  return {\n    mount: mount,\n    move: move,\n    jump: jump,\n    translate: translate,\n    shift: shift,\n    cancel: cancel,\n    toIndex: toIndex,\n    toPosition: toPosition,\n    getPosition: getPosition,\n    getLimit: getLimit,\n    exceededLimit: exceededLimit,\n    reposition: reposition\n  };\n}\n\nfunction Controller(Splide2, Components2, options) {\n  var _EventInterface5 = EventInterface(Splide2),\n      on = _EventInterface5.on,\n      emit = _EventInterface5.emit;\n\n  var Move = Components2.Move;\n  var getPosition = Move.getPosition,\n      getLimit = Move.getLimit,\n      toPosition = Move.toPosition;\n  var _Components2$Slides = Components2.Slides,\n      isEnough = _Components2$Slides.isEnough,\n      getLength = _Components2$Slides.getLength;\n  var omitEnd = options.omitEnd;\n  var isLoop = Splide2.is(LOOP);\n  var isSlide = Splide2.is(SLIDE);\n  var getNext = apply(getAdjacent, false);\n  var getPrev = apply(getAdjacent, true);\n  var currIndex = options.start || 0;\n  var endIndex;\n  var prevIndex = currIndex;\n  var slideCount;\n  var perMove;\n  var perPage;\n\n  function mount() {\n    init();\n    on([EVENT_UPDATED, EVENT_REFRESH, EVENT_END_INDEX_CHANGED], init);\n    on(EVENT_RESIZED, onResized);\n  }\n\n  function init() {\n    slideCount = getLength(true);\n    perMove = options.perMove;\n    perPage = options.perPage;\n    endIndex = getEnd();\n    var index = clamp(currIndex, 0, omitEnd ? endIndex : slideCount - 1);\n\n    if (index !== currIndex) {\n      currIndex = index;\n      Move.reposition();\n    }\n  }\n\n  function onResized() {\n    if (endIndex !== getEnd()) {\n      emit(EVENT_END_INDEX_CHANGED);\n    }\n  }\n\n  function go(control, allowSameIndex, callback) {\n    if (!isBusy()) {\n      var dest = parse(control);\n      var index = loop(dest);\n\n      if (index > -1 && (allowSameIndex || index !== currIndex)) {\n        setIndex(index);\n        Move.move(dest, index, prevIndex, callback);\n      }\n    }\n  }\n\n  function scroll(destination, duration, snap, callback) {\n    Components2.Scroll.scroll(destination, duration, snap, function () {\n      var index = loop(Move.toIndex(getPosition()));\n      setIndex(omitEnd ? min(index, endIndex) : index);\n      callback && callback();\n    });\n  }\n\n  function parse(control) {\n    var index = currIndex;\n\n    if (isString(control)) {\n      var _ref = control.match(/([+\\-<>])(\\d+)?/) || [],\n          indicator = _ref[1],\n          number = _ref[2];\n\n      if (indicator === \"+\" || indicator === \"-\") {\n        index = computeDestIndex(currIndex + +(\"\" + indicator + (+number || 1)), currIndex);\n      } else if (indicator === \">\") {\n        index = number ? toIndex(+number) : getNext(true);\n      } else if (indicator === \"<\") {\n        index = getPrev(true);\n      }\n    } else {\n      index = isLoop ? control : clamp(control, 0, endIndex);\n    }\n\n    return index;\n  }\n\n  function getAdjacent(prev, destination) {\n    var number = perMove || (hasFocus() ? 1 : perPage);\n    var dest = computeDestIndex(currIndex + number * (prev ? -1 : 1), currIndex, !(perMove || hasFocus()));\n\n    if (dest === -1 && isSlide) {\n      if (!approximatelyEqual(getPosition(), getLimit(!prev), 1)) {\n        return prev ? 0 : endIndex;\n      }\n    }\n\n    return destination ? dest : loop(dest);\n  }\n\n  function computeDestIndex(dest, from, snapPage) {\n    if (isEnough() || hasFocus()) {\n      var index = computeMovableDestIndex(dest);\n\n      if (index !== dest) {\n        from = dest;\n        dest = index;\n        snapPage = false;\n      }\n\n      if (dest < 0 || dest > endIndex) {\n        if (!perMove && (between(0, dest, from, true) || between(endIndex, from, dest, true))) {\n          dest = toIndex(toPage(dest));\n        } else {\n          if (isLoop) {\n            dest = snapPage ? dest < 0 ? -(slideCount % perPage || perPage) : slideCount : dest;\n          } else if (options.rewind) {\n            dest = dest < 0 ? endIndex : 0;\n          } else {\n            dest = -1;\n          }\n        }\n      } else {\n        if (snapPage && dest !== from) {\n          dest = toIndex(toPage(from) + (dest < from ? -1 : 1));\n        }\n      }\n    } else {\n      dest = -1;\n    }\n\n    return dest;\n  }\n\n  function computeMovableDestIndex(dest) {\n    if (isSlide && options.trimSpace === \"move\" && dest !== currIndex) {\n      var position = getPosition();\n\n      while (position === toPosition(dest, true) && between(dest, 0, Splide2.length - 1, !options.rewind)) {\n        dest < currIndex ? --dest : ++dest;\n      }\n    }\n\n    return dest;\n  }\n\n  function loop(index) {\n    return isLoop ? (index + slideCount) % slideCount || 0 : index;\n  }\n\n  function getEnd() {\n    var end = slideCount - (hasFocus() || isLoop && perMove ? 1 : perPage);\n\n    while (omitEnd && end-- > 0) {\n      if (toPosition(slideCount - 1, true) !== toPosition(end, true)) {\n        end++;\n        break;\n      }\n    }\n\n    return clamp(end, 0, slideCount - 1);\n  }\n\n  function toIndex(page) {\n    return clamp(hasFocus() ? page : perPage * page, 0, endIndex);\n  }\n\n  function toPage(index) {\n    return hasFocus() ? min(index, endIndex) : floor((index >= endIndex ? slideCount - 1 : index) / perPage);\n  }\n\n  function toDest(destination) {\n    var closest = Move.toIndex(destination);\n    return isSlide ? clamp(closest, 0, endIndex) : closest;\n  }\n\n  function setIndex(index) {\n    if (index !== currIndex) {\n      prevIndex = currIndex;\n      currIndex = index;\n    }\n  }\n\n  function getIndex(prev) {\n    return prev ? prevIndex : currIndex;\n  }\n\n  function hasFocus() {\n    return !isUndefined(options.focus) || options.isNavigation;\n  }\n\n  function isBusy() {\n    return Splide2.state.is([MOVING, SCROLLING]) && !!options.waitForTransition;\n  }\n\n  return {\n    mount: mount,\n    go: go,\n    scroll: scroll,\n    getNext: getNext,\n    getPrev: getPrev,\n    getAdjacent: getAdjacent,\n    getEnd: getEnd,\n    setIndex: setIndex,\n    getIndex: getIndex,\n    toIndex: toIndex,\n    toPage: toPage,\n    toDest: toDest,\n    hasFocus: hasFocus,\n    isBusy: isBusy\n  };\n}\n\nvar XML_NAME_SPACE = \"http://www.w3.org/2000/svg\";\nvar PATH = \"m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z\";\nvar SIZE = 40;\n\nfunction Arrows(Splide2, Components2, options) {\n  var event = EventInterface(Splide2);\n  var on = event.on,\n      bind = event.bind,\n      emit = event.emit;\n  var classes = options.classes,\n      i18n = options.i18n;\n  var Elements = Components2.Elements,\n      Controller = Components2.Controller;\n  var placeholder = Elements.arrows,\n      track = Elements.track;\n  var wrapper = placeholder;\n  var prev = Elements.prev;\n  var next = Elements.next;\n  var created;\n  var wrapperClasses;\n  var arrows = {};\n\n  function mount() {\n    init();\n    on(EVENT_UPDATED, remount);\n  }\n\n  function remount() {\n    destroy();\n    mount();\n  }\n\n  function init() {\n    var enabled = options.arrows;\n\n    if (enabled && !(prev && next)) {\n      createArrows();\n    }\n\n    if (prev && next) {\n      assign(arrows, {\n        prev: prev,\n        next: next\n      });\n      display(wrapper, enabled ? \"\" : \"none\");\n      addClass(wrapper, wrapperClasses = CLASS_ARROWS + \"--\" + options.direction);\n\n      if (enabled) {\n        listen();\n        update();\n        setAttribute([prev, next], ARIA_CONTROLS, track.id);\n        emit(EVENT_ARROWS_MOUNTED, prev, next);\n      }\n    }\n  }\n\n  function destroy() {\n    event.destroy();\n    removeClass(wrapper, wrapperClasses);\n\n    if (created) {\n      remove(placeholder ? [prev, next] : wrapper);\n      prev = next = null;\n    } else {\n      removeAttribute([prev, next], ALL_ATTRIBUTES);\n    }\n  }\n\n  function listen() {\n    on([EVENT_MOUNTED, EVENT_MOVED, EVENT_REFRESH, EVENT_SCROLLED, EVENT_END_INDEX_CHANGED], update);\n    bind(next, \"click\", apply(go, \">\"));\n    bind(prev, \"click\", apply(go, \"<\"));\n  }\n\n  function go(control) {\n    Controller.go(control, true);\n  }\n\n  function createArrows() {\n    wrapper = placeholder || create(\"div\", classes.arrows);\n    prev = createArrow(true);\n    next = createArrow(false);\n    created = true;\n    append(wrapper, [prev, next]);\n    !placeholder && before(wrapper, track);\n  }\n\n  function createArrow(prev2) {\n    var arrow = \"<button class=\\\"\" + classes.arrow + \" \" + (prev2 ? classes.prev : classes.next) + \"\\\" type=\\\"button\\\"><svg xmlns=\\\"\" + XML_NAME_SPACE + \"\\\" viewBox=\\\"0 0 \" + SIZE + \" \" + SIZE + \"\\\" width=\\\"\" + SIZE + \"\\\" height=\\\"\" + SIZE + \"\\\" focusable=\\\"false\\\"><path d=\\\"\" + (options.arrowPath || PATH) + \"\\\" />\";\n    return parseHtml(arrow);\n  }\n\n  function update() {\n    if (prev && next) {\n      var index = Splide2.index;\n      var prevIndex = Controller.getPrev();\n      var nextIndex = Controller.getNext();\n      var prevLabel = prevIndex > -1 && index < prevIndex ? i18n.last : i18n.prev;\n      var nextLabel = nextIndex > -1 && index > nextIndex ? i18n.first : i18n.next;\n      prev.disabled = prevIndex < 0;\n      next.disabled = nextIndex < 0;\n      setAttribute(prev, ARIA_LABEL, prevLabel);\n      setAttribute(next, ARIA_LABEL, nextLabel);\n      emit(EVENT_ARROWS_UPDATED, prev, next, prevIndex, nextIndex);\n    }\n  }\n\n  return {\n    arrows: arrows,\n    mount: mount,\n    destroy: destroy,\n    update: update\n  };\n}\n\nvar INTERVAL_DATA_ATTRIBUTE = DATA_ATTRIBUTE + \"-interval\";\n\nfunction Autoplay(Splide2, Components2, options) {\n  var _EventInterface6 = EventInterface(Splide2),\n      on = _EventInterface6.on,\n      bind = _EventInterface6.bind,\n      emit = _EventInterface6.emit;\n\n  var interval = RequestInterval(options.interval, Splide2.go.bind(Splide2, \">\"), onAnimationFrame);\n  var isPaused = interval.isPaused;\n  var Elements = Components2.Elements,\n      _Components2$Elements4 = Components2.Elements,\n      root = _Components2$Elements4.root,\n      toggle = _Components2$Elements4.toggle;\n  var autoplay = options.autoplay;\n  var hovered;\n  var focused;\n  var stopped = autoplay === \"pause\";\n\n  function mount() {\n    if (autoplay) {\n      listen();\n      toggle && setAttribute(toggle, ARIA_CONTROLS, Elements.track.id);\n      stopped || play();\n      update();\n    }\n  }\n\n  function listen() {\n    if (options.pauseOnHover) {\n      bind(root, \"mouseenter mouseleave\", function (e) {\n        hovered = e.type === \"mouseenter\";\n        autoToggle();\n      });\n    }\n\n    if (options.pauseOnFocus) {\n      bind(root, \"focusin focusout\", function (e) {\n        focused = e.type === \"focusin\";\n        autoToggle();\n      });\n    }\n\n    if (toggle) {\n      bind(toggle, \"click\", function () {\n        stopped ? play() : pause(true);\n      });\n    }\n\n    on([EVENT_MOVE, EVENT_SCROLL, EVENT_REFRESH], interval.rewind);\n    on(EVENT_MOVE, onMove);\n  }\n\n  function play() {\n    if (isPaused() && Components2.Slides.isEnough()) {\n      interval.start(!options.resetProgress);\n      focused = hovered = stopped = false;\n      update();\n      emit(EVENT_AUTOPLAY_PLAY);\n    }\n  }\n\n  function pause(stop) {\n    if (stop === void 0) {\n      stop = true;\n    }\n\n    stopped = !!stop;\n    update();\n\n    if (!isPaused()) {\n      interval.pause();\n      emit(EVENT_AUTOPLAY_PAUSE);\n    }\n  }\n\n  function autoToggle() {\n    if (!stopped) {\n      hovered || focused ? pause(false) : play();\n    }\n  }\n\n  function update() {\n    if (toggle) {\n      toggleClass(toggle, CLASS_ACTIVE, !stopped);\n      setAttribute(toggle, ARIA_LABEL, options.i18n[stopped ? \"play\" : \"pause\"]);\n    }\n  }\n\n  function onAnimationFrame(rate) {\n    var bar = Elements.bar;\n    bar && style(bar, \"width\", rate * 100 + \"%\");\n    emit(EVENT_AUTOPLAY_PLAYING, rate);\n  }\n\n  function onMove(index) {\n    var Slide = Components2.Slides.getAt(index);\n    interval.set(Slide && +getAttribute(Slide.slide, INTERVAL_DATA_ATTRIBUTE) || options.interval);\n  }\n\n  return {\n    mount: mount,\n    destroy: interval.cancel,\n    play: play,\n    pause: pause,\n    isPaused: isPaused\n  };\n}\n\nfunction Cover(Splide2, Components2, options) {\n  var _EventInterface7 = EventInterface(Splide2),\n      on = _EventInterface7.on;\n\n  function mount() {\n    if (options.cover) {\n      on(EVENT_LAZYLOAD_LOADED, apply(toggle, true));\n      on([EVENT_MOUNTED, EVENT_UPDATED, EVENT_REFRESH], apply(cover, true));\n    }\n  }\n\n  function cover(cover2) {\n    Components2.Slides.forEach(function (Slide) {\n      var img = child(Slide.container || Slide.slide, \"img\");\n\n      if (img && img.src) {\n        toggle(cover2, img, Slide);\n      }\n    });\n  }\n\n  function toggle(cover2, img, Slide) {\n    Slide.style(\"background\", cover2 ? \"center/cover no-repeat url(\\\"\" + img.src + \"\\\")\" : \"\", true);\n    display(img, cover2 ? \"none\" : \"\");\n  }\n\n  return {\n    mount: mount,\n    destroy: apply(cover, false)\n  };\n}\n\nvar BOUNCE_DIFF_THRESHOLD = 10;\nvar BOUNCE_DURATION = 600;\nvar FRICTION_FACTOR = 0.6;\nvar BASE_VELOCITY = 1.5;\nvar MIN_DURATION = 800;\n\nfunction Scroll(Splide2, Components2, options) {\n  var _EventInterface8 = EventInterface(Splide2),\n      on = _EventInterface8.on,\n      emit = _EventInterface8.emit;\n\n  var set = Splide2.state.set;\n  var Move = Components2.Move;\n  var getPosition = Move.getPosition,\n      getLimit = Move.getLimit,\n      exceededLimit = Move.exceededLimit,\n      translate = Move.translate;\n  var isSlide = Splide2.is(SLIDE);\n  var interval;\n  var callback;\n  var friction = 1;\n\n  function mount() {\n    on(EVENT_MOVE, clear);\n    on([EVENT_UPDATED, EVENT_REFRESH], cancel);\n  }\n\n  function scroll(destination, duration, snap, onScrolled, noConstrain) {\n    var from = getPosition();\n    clear();\n\n    if (snap && (!isSlide || !exceededLimit())) {\n      var size = Components2.Layout.sliderSize();\n      var offset = sign(destination) * size * floor(abs(destination) / size) || 0;\n      destination = Move.toPosition(Components2.Controller.toDest(destination % size)) + offset;\n    }\n\n    var noDistance = approximatelyEqual(from, destination, 1);\n    friction = 1;\n    duration = noDistance ? 0 : duration || max(abs(destination - from) / BASE_VELOCITY, MIN_DURATION);\n    callback = onScrolled;\n    interval = RequestInterval(duration, onEnd, apply(update, from, destination, noConstrain), 1);\n    set(SCROLLING);\n    emit(EVENT_SCROLL);\n    interval.start();\n  }\n\n  function onEnd() {\n    set(IDLE);\n    callback && callback();\n    emit(EVENT_SCROLLED);\n  }\n\n  function update(from, to, noConstrain, rate) {\n    var position = getPosition();\n    var target = from + (to - from) * easing(rate);\n    var diff = (target - position) * friction;\n    translate(position + diff);\n\n    if (isSlide && !noConstrain && exceededLimit()) {\n      friction *= FRICTION_FACTOR;\n\n      if (abs(diff) < BOUNCE_DIFF_THRESHOLD) {\n        scroll(getLimit(exceededLimit(true)), BOUNCE_DURATION, false, callback, true);\n      }\n    }\n  }\n\n  function clear() {\n    if (interval) {\n      interval.cancel();\n    }\n  }\n\n  function cancel() {\n    if (interval && !interval.isPaused()) {\n      clear();\n      onEnd();\n    }\n  }\n\n  function easing(t) {\n    var easingFunc = options.easingFunc;\n    return easingFunc ? easingFunc(t) : 1 - Math.pow(1 - t, 4);\n  }\n\n  return {\n    mount: mount,\n    destroy: clear,\n    scroll: scroll,\n    cancel: cancel\n  };\n}\n\nvar SCROLL_LISTENER_OPTIONS = {\n  passive: false,\n  capture: true\n};\n\nfunction Drag(Splide2, Components2, options) {\n  var _EventInterface9 = EventInterface(Splide2),\n      on = _EventInterface9.on,\n      emit = _EventInterface9.emit,\n      bind = _EventInterface9.bind,\n      unbind = _EventInterface9.unbind;\n\n  var state = Splide2.state;\n  var Move = Components2.Move,\n      Scroll = Components2.Scroll,\n      Controller = Components2.Controller,\n      track = Components2.Elements.track,\n      reduce = Components2.Media.reduce;\n  var _Components2$Directio2 = Components2.Direction,\n      resolve = _Components2$Directio2.resolve,\n      orient = _Components2$Directio2.orient;\n  var getPosition = Move.getPosition,\n      exceededLimit = Move.exceededLimit;\n  var basePosition;\n  var baseEvent;\n  var prevBaseEvent;\n  var isFree;\n  var dragging;\n  var exceeded = false;\n  var clickPrevented;\n  var disabled;\n  var target;\n\n  function mount() {\n    bind(track, POINTER_MOVE_EVENTS, noop, SCROLL_LISTENER_OPTIONS);\n    bind(track, POINTER_UP_EVENTS, noop, SCROLL_LISTENER_OPTIONS);\n    bind(track, POINTER_DOWN_EVENTS, onPointerDown, SCROLL_LISTENER_OPTIONS);\n    bind(track, \"click\", onClick, {\n      capture: true\n    });\n    bind(track, \"dragstart\", prevent);\n    on([EVENT_MOUNTED, EVENT_UPDATED], init);\n  }\n\n  function init() {\n    var drag = options.drag;\n    disable(!drag);\n    isFree = drag === \"free\";\n  }\n\n  function onPointerDown(e) {\n    clickPrevented = false;\n\n    if (!disabled) {\n      var isTouch = isTouchEvent(e);\n\n      if (isDraggable(e.target) && (isTouch || !e.button)) {\n        if (!Controller.isBusy()) {\n          target = isTouch ? track : window;\n          dragging = state.is([MOVING, SCROLLING]);\n          prevBaseEvent = null;\n          bind(target, POINTER_MOVE_EVENTS, onPointerMove, SCROLL_LISTENER_OPTIONS);\n          bind(target, POINTER_UP_EVENTS, onPointerUp, SCROLL_LISTENER_OPTIONS);\n          Move.cancel();\n          Scroll.cancel();\n          save(e);\n        } else {\n          prevent(e, true);\n        }\n      }\n    }\n  }\n\n  function onPointerMove(e) {\n    if (!state.is(DRAGGING)) {\n      state.set(DRAGGING);\n      emit(EVENT_DRAG);\n    }\n\n    if (e.cancelable) {\n      if (dragging) {\n        Move.translate(basePosition + constrain(diffCoord(e)));\n        var expired = diffTime(e) > LOG_INTERVAL;\n        var hasExceeded = exceeded !== (exceeded = exceededLimit());\n\n        if (expired || hasExceeded) {\n          save(e);\n        }\n\n        clickPrevented = true;\n        emit(EVENT_DRAGGING);\n        prevent(e);\n      } else if (isSliderDirection(e)) {\n        dragging = shouldStart(e);\n        prevent(e);\n      }\n    }\n  }\n\n  function onPointerUp(e) {\n    if (state.is(DRAGGING)) {\n      state.set(IDLE);\n      emit(EVENT_DRAGGED);\n    }\n\n    if (dragging) {\n      move(e);\n      prevent(e);\n    }\n\n    unbind(target, POINTER_MOVE_EVENTS, onPointerMove);\n    unbind(target, POINTER_UP_EVENTS, onPointerUp);\n    dragging = false;\n  }\n\n  function onClick(e) {\n    if (!disabled && clickPrevented) {\n      prevent(e, true);\n    }\n  }\n\n  function save(e) {\n    prevBaseEvent = baseEvent;\n    baseEvent = e;\n    basePosition = getPosition();\n  }\n\n  function move(e) {\n    var velocity = computeVelocity(e);\n    var destination = computeDestination(velocity);\n    var rewind = options.rewind && options.rewindByDrag;\n    reduce(false);\n\n    if (isFree) {\n      Controller.scroll(destination, 0, options.snap);\n    } else if (Splide2.is(FADE)) {\n      Controller.go(orient(sign(velocity)) < 0 ? rewind ? \"<\" : \"-\" : rewind ? \">\" : \"+\");\n    } else if (Splide2.is(SLIDE) && exceeded && rewind) {\n      Controller.go(exceededLimit(true) ? \">\" : \"<\");\n    } else {\n      Controller.go(Controller.toDest(destination), true);\n    }\n\n    reduce(true);\n  }\n\n  function shouldStart(e) {\n    var thresholds = options.dragMinThreshold;\n    var isObj = isObject(thresholds);\n    var mouse = isObj && thresholds.mouse || 0;\n    var touch = (isObj ? thresholds.touch : +thresholds) || 10;\n    return abs(diffCoord(e)) > (isTouchEvent(e) ? touch : mouse);\n  }\n\n  function isSliderDirection(e) {\n    return abs(diffCoord(e)) > abs(diffCoord(e, true));\n  }\n\n  function computeVelocity(e) {\n    if (Splide2.is(LOOP) || !exceeded) {\n      var time = diffTime(e);\n\n      if (time && time < LOG_INTERVAL) {\n        return diffCoord(e) / time;\n      }\n    }\n\n    return 0;\n  }\n\n  function computeDestination(velocity) {\n    return getPosition() + sign(velocity) * min(abs(velocity) * (options.flickPower || 600), isFree ? Infinity : Components2.Layout.listSize() * (options.flickMaxPages || 1));\n  }\n\n  function diffCoord(e, orthogonal) {\n    return coordOf(e, orthogonal) - coordOf(getBaseEvent(e), orthogonal);\n  }\n\n  function diffTime(e) {\n    return timeOf(e) - timeOf(getBaseEvent(e));\n  }\n\n  function getBaseEvent(e) {\n    return baseEvent === e && prevBaseEvent || baseEvent;\n  }\n\n  function coordOf(e, orthogonal) {\n    return (isTouchEvent(e) ? e.changedTouches[0] : e)[\"page\" + resolve(orthogonal ? \"Y\" : \"X\")];\n  }\n\n  function constrain(diff) {\n    return diff / (exceeded && Splide2.is(SLIDE) ? FRICTION : 1);\n  }\n\n  function isDraggable(target2) {\n    var noDrag = options.noDrag;\n    return !matches(target2, \".\" + CLASS_PAGINATION_PAGE + \", .\" + CLASS_ARROW) && (!noDrag || !matches(target2, noDrag));\n  }\n\n  function isTouchEvent(e) {\n    return typeof TouchEvent !== \"undefined\" && e instanceof TouchEvent;\n  }\n\n  function isDragging() {\n    return dragging;\n  }\n\n  function disable(value) {\n    disabled = value;\n  }\n\n  return {\n    mount: mount,\n    disable: disable,\n    isDragging: isDragging\n  };\n}\n\nvar NORMALIZATION_MAP = {\n  Spacebar: \" \",\n  Right: ARROW_RIGHT,\n  Left: ARROW_LEFT,\n  Up: ARROW_UP,\n  Down: ARROW_DOWN\n};\n\nfunction normalizeKey(key) {\n  key = isString(key) ? key : key.key;\n  return NORMALIZATION_MAP[key] || key;\n}\n\nvar KEYBOARD_EVENT = \"keydown\";\n\nfunction Keyboard(Splide2, Components2, options) {\n  var _EventInterface10 = EventInterface(Splide2),\n      on = _EventInterface10.on,\n      bind = _EventInterface10.bind,\n      unbind = _EventInterface10.unbind;\n\n  var root = Splide2.root;\n  var resolve = Components2.Direction.resolve;\n  var target;\n  var disabled;\n\n  function mount() {\n    init();\n    on(EVENT_UPDATED, destroy);\n    on(EVENT_UPDATED, init);\n    on(EVENT_MOVE, onMove);\n  }\n\n  function init() {\n    var keyboard = options.keyboard;\n\n    if (keyboard) {\n      target = keyboard === \"global\" ? window : root;\n      bind(target, KEYBOARD_EVENT, onKeydown);\n    }\n  }\n\n  function destroy() {\n    unbind(target, KEYBOARD_EVENT);\n  }\n\n  function disable(value) {\n    disabled = value;\n  }\n\n  function onMove() {\n    var _disabled = disabled;\n    disabled = true;\n    nextTick(function () {\n      disabled = _disabled;\n    });\n  }\n\n  function onKeydown(e) {\n    if (!disabled) {\n      var key = normalizeKey(e);\n\n      if (key === resolve(ARROW_LEFT)) {\n        Splide2.go(\"<\");\n      } else if (key === resolve(ARROW_RIGHT)) {\n        Splide2.go(\">\");\n      }\n    }\n  }\n\n  return {\n    mount: mount,\n    destroy: destroy,\n    disable: disable\n  };\n}\n\nvar SRC_DATA_ATTRIBUTE = DATA_ATTRIBUTE + \"-lazy\";\nvar SRCSET_DATA_ATTRIBUTE = SRC_DATA_ATTRIBUTE + \"-srcset\";\nvar IMAGE_SELECTOR = \"[\" + SRC_DATA_ATTRIBUTE + \"], [\" + SRCSET_DATA_ATTRIBUTE + \"]\";\n\nfunction LazyLoad(Splide2, Components2, options) {\n  var _EventInterface11 = EventInterface(Splide2),\n      on = _EventInterface11.on,\n      off = _EventInterface11.off,\n      bind = _EventInterface11.bind,\n      emit = _EventInterface11.emit;\n\n  var isSequential = options.lazyLoad === \"sequential\";\n  var events = [EVENT_MOVED, EVENT_SCROLLED];\n  var entries = [];\n\n  function mount() {\n    if (options.lazyLoad) {\n      init();\n      on(EVENT_REFRESH, init);\n    }\n  }\n\n  function init() {\n    empty(entries);\n    register();\n\n    if (isSequential) {\n      loadNext();\n    } else {\n      off(events);\n      on(events, check);\n      check();\n    }\n  }\n\n  function register() {\n    Components2.Slides.forEach(function (Slide) {\n      queryAll(Slide.slide, IMAGE_SELECTOR).forEach(function (img) {\n        var src = getAttribute(img, SRC_DATA_ATTRIBUTE);\n        var srcset = getAttribute(img, SRCSET_DATA_ATTRIBUTE);\n\n        if (src !== img.src || srcset !== img.srcset) {\n          var className = options.classes.spinner;\n          var parent = img.parentElement;\n          var spinner = child(parent, \".\" + className) || create(\"span\", className, parent);\n          entries.push([img, Slide, spinner]);\n          img.src || display(img, \"none\");\n        }\n      });\n    });\n  }\n\n  function check() {\n    entries = entries.filter(function (data) {\n      var distance = options.perPage * ((options.preloadPages || 1) + 1) - 1;\n      return data[1].isWithin(Splide2.index, distance) ? load(data) : true;\n    });\n    entries.length || off(events);\n  }\n\n  function load(data) {\n    var img = data[0];\n    addClass(data[1].slide, CLASS_LOADING);\n    bind(img, \"load error\", apply(onLoad, data));\n    setAttribute(img, \"src\", getAttribute(img, SRC_DATA_ATTRIBUTE));\n    setAttribute(img, \"srcset\", getAttribute(img, SRCSET_DATA_ATTRIBUTE));\n    removeAttribute(img, SRC_DATA_ATTRIBUTE);\n    removeAttribute(img, SRCSET_DATA_ATTRIBUTE);\n  }\n\n  function onLoad(data, e) {\n    var img = data[0],\n        Slide = data[1];\n    removeClass(Slide.slide, CLASS_LOADING);\n\n    if (e.type !== \"error\") {\n      remove(data[2]);\n      display(img, \"\");\n      emit(EVENT_LAZYLOAD_LOADED, img, Slide);\n      emit(EVENT_RESIZE);\n    }\n\n    isSequential && loadNext();\n  }\n\n  function loadNext() {\n    entries.length && load(entries.shift());\n  }\n\n  return {\n    mount: mount,\n    destroy: apply(empty, entries),\n    check: check\n  };\n}\n\nfunction Pagination(Splide2, Components2, options) {\n  var event = EventInterface(Splide2);\n  var on = event.on,\n      emit = event.emit,\n      bind = event.bind;\n  var Slides = Components2.Slides,\n      Elements = Components2.Elements,\n      Controller = Components2.Controller;\n  var hasFocus = Controller.hasFocus,\n      getIndex = Controller.getIndex,\n      go = Controller.go;\n  var resolve = Components2.Direction.resolve;\n  var placeholder = Elements.pagination;\n  var items = [];\n  var list;\n  var paginationClasses;\n\n  function mount() {\n    destroy();\n    on([EVENT_UPDATED, EVENT_REFRESH, EVENT_END_INDEX_CHANGED], mount);\n    var enabled = options.pagination;\n    placeholder && display(placeholder, enabled ? \"\" : \"none\");\n\n    if (enabled) {\n      on([EVENT_MOVE, EVENT_SCROLL, EVENT_SCROLLED], update);\n      createPagination();\n      update();\n      emit(EVENT_PAGINATION_MOUNTED, {\n        list: list,\n        items: items\n      }, getAt(Splide2.index));\n    }\n  }\n\n  function destroy() {\n    if (list) {\n      remove(placeholder ? slice(list.children) : list);\n      removeClass(list, paginationClasses);\n      empty(items);\n      list = null;\n    }\n\n    event.destroy();\n  }\n\n  function createPagination() {\n    var length = Splide2.length;\n    var classes = options.classes,\n        i18n = options.i18n,\n        perPage = options.perPage;\n    var max = hasFocus() ? Controller.getEnd() + 1 : ceil(length / perPage);\n    list = placeholder || create(\"ul\", classes.pagination, Elements.track.parentElement);\n    addClass(list, paginationClasses = CLASS_PAGINATION + \"--\" + getDirection());\n    setAttribute(list, ROLE, \"tablist\");\n    setAttribute(list, ARIA_LABEL, i18n.select);\n    setAttribute(list, ARIA_ORIENTATION, getDirection() === TTB ? \"vertical\" : \"\");\n\n    for (var i = 0; i < max; i++) {\n      var li = create(\"li\", null, list);\n      var button = create(\"button\", {\n        class: classes.page,\n        type: \"button\"\n      }, li);\n      var controls = Slides.getIn(i).map(function (Slide) {\n        return Slide.slide.id;\n      });\n      var text = !hasFocus() && perPage > 1 ? i18n.pageX : i18n.slideX;\n      bind(button, \"click\", apply(onClick, i));\n\n      if (options.paginationKeyboard) {\n        bind(button, \"keydown\", apply(onKeydown, i));\n      }\n\n      setAttribute(li, ROLE, \"presentation\");\n      setAttribute(button, ROLE, \"tab\");\n      setAttribute(button, ARIA_CONTROLS, controls.join(\" \"));\n      setAttribute(button, ARIA_LABEL, format(text, i + 1));\n      setAttribute(button, TAB_INDEX, -1);\n      items.push({\n        li: li,\n        button: button,\n        page: i\n      });\n    }\n  }\n\n  function onClick(page) {\n    go(\">\" + page, true);\n  }\n\n  function onKeydown(page, e) {\n    var length = items.length;\n    var key = normalizeKey(e);\n    var dir = getDirection();\n    var nextPage = -1;\n\n    if (key === resolve(ARROW_RIGHT, false, dir)) {\n      nextPage = ++page % length;\n    } else if (key === resolve(ARROW_LEFT, false, dir)) {\n      nextPage = (--page + length) % length;\n    } else if (key === \"Home\") {\n      nextPage = 0;\n    } else if (key === \"End\") {\n      nextPage = length - 1;\n    }\n\n    var item = items[nextPage];\n\n    if (item) {\n      focus(item.button);\n      go(\">\" + nextPage);\n      prevent(e, true);\n    }\n  }\n\n  function getDirection() {\n    return options.paginationDirection || options.direction;\n  }\n\n  function getAt(index) {\n    return items[Controller.toPage(index)];\n  }\n\n  function update() {\n    var prev = getAt(getIndex(true));\n    var curr = getAt(getIndex());\n\n    if (prev) {\n      var button = prev.button;\n      removeClass(button, CLASS_ACTIVE);\n      removeAttribute(button, ARIA_SELECTED);\n      setAttribute(button, TAB_INDEX, -1);\n    }\n\n    if (curr) {\n      var _button = curr.button;\n      addClass(_button, CLASS_ACTIVE);\n      setAttribute(_button, ARIA_SELECTED, true);\n      setAttribute(_button, TAB_INDEX, \"\");\n    }\n\n    emit(EVENT_PAGINATION_UPDATED, {\n      list: list,\n      items: items\n    }, prev, curr);\n  }\n\n  return {\n    items: items,\n    mount: mount,\n    destroy: destroy,\n    getAt: getAt,\n    update: update\n  };\n}\n\nvar TRIGGER_KEYS = [\" \", \"Enter\"];\n\nfunction Sync(Splide2, Components2, options) {\n  var isNavigation = options.isNavigation,\n      slideFocus = options.slideFocus;\n  var events = [];\n\n  function mount() {\n    Splide2.splides.forEach(function (target) {\n      if (!target.isParent) {\n        sync(Splide2, target.splide);\n        sync(target.splide, Splide2);\n      }\n    });\n\n    if (isNavigation) {\n      navigate();\n    }\n  }\n\n  function destroy() {\n    events.forEach(function (event) {\n      event.destroy();\n    });\n    empty(events);\n  }\n\n  function remount() {\n    destroy();\n    mount();\n  }\n\n  function sync(splide, target) {\n    var event = EventInterface(splide);\n    event.on(EVENT_MOVE, function (index, prev, dest) {\n      target.go(target.is(LOOP) ? dest : index);\n    });\n    events.push(event);\n  }\n\n  function navigate() {\n    var event = EventInterface(Splide2);\n    var on = event.on;\n    on(EVENT_CLICK, onClick);\n    on(EVENT_SLIDE_KEYDOWN, onKeydown);\n    on([EVENT_MOUNTED, EVENT_UPDATED], update);\n    events.push(event);\n    event.emit(EVENT_NAVIGATION_MOUNTED, Splide2.splides);\n  }\n\n  function update() {\n    setAttribute(Components2.Elements.list, ARIA_ORIENTATION, options.direction === TTB ? \"vertical\" : \"\");\n  }\n\n  function onClick(Slide) {\n    Splide2.go(Slide.index);\n  }\n\n  function onKeydown(Slide, e) {\n    if (includes(TRIGGER_KEYS, normalizeKey(e))) {\n      onClick(Slide);\n      prevent(e);\n    }\n  }\n\n  return {\n    setup: apply(Components2.Media.set, {\n      slideFocus: isUndefined(slideFocus) ? isNavigation : slideFocus\n    }, true),\n    mount: mount,\n    destroy: destroy,\n    remount: remount\n  };\n}\n\nfunction Wheel(Splide2, Components2, options) {\n  var _EventInterface12 = EventInterface(Splide2),\n      bind = _EventInterface12.bind;\n\n  var lastTime = 0;\n\n  function mount() {\n    if (options.wheel) {\n      bind(Components2.Elements.track, \"wheel\", onWheel, SCROLL_LISTENER_OPTIONS);\n    }\n  }\n\n  function onWheel(e) {\n    if (e.cancelable) {\n      var deltaY = e.deltaY;\n      var backwards = deltaY < 0;\n      var timeStamp = timeOf(e);\n\n      var _min = options.wheelMinThreshold || 0;\n\n      var sleep = options.wheelSleep || 0;\n\n      if (abs(deltaY) > _min && timeStamp - lastTime > sleep) {\n        Splide2.go(backwards ? \"<\" : \">\");\n        lastTime = timeStamp;\n      }\n\n      shouldPrevent(backwards) && prevent(e);\n    }\n  }\n\n  function shouldPrevent(backwards) {\n    return !options.releaseWheel || Splide2.state.is(MOVING) || Components2.Controller.getAdjacent(backwards) !== -1;\n  }\n\n  return {\n    mount: mount\n  };\n}\n\nvar SR_REMOVAL_DELAY = 90;\n\nfunction Live(Splide2, Components2, options) {\n  var _EventInterface13 = EventInterface(Splide2),\n      on = _EventInterface13.on;\n\n  var track = Components2.Elements.track;\n  var enabled = options.live && !options.isNavigation;\n  var sr = create(\"span\", CLASS_SR);\n  var interval = RequestInterval(SR_REMOVAL_DELAY, apply(toggle, false));\n\n  function mount() {\n    if (enabled) {\n      disable(!Components2.Autoplay.isPaused());\n      setAttribute(track, ARIA_ATOMIC, true);\n      sr.textContent = \"\\u2026\";\n      on(EVENT_AUTOPLAY_PLAY, apply(disable, true));\n      on(EVENT_AUTOPLAY_PAUSE, apply(disable, false));\n      on([EVENT_MOVED, EVENT_SCROLLED], apply(toggle, true));\n    }\n  }\n\n  function toggle(active) {\n    setAttribute(track, ARIA_BUSY, active);\n\n    if (active) {\n      append(track, sr);\n      interval.start();\n    } else {\n      remove(sr);\n      interval.cancel();\n    }\n  }\n\n  function destroy() {\n    removeAttribute(track, [ARIA_LIVE, ARIA_ATOMIC, ARIA_BUSY]);\n    remove(sr);\n  }\n\n  function disable(disabled) {\n    if (enabled) {\n      setAttribute(track, ARIA_LIVE, disabled ? \"off\" : \"polite\");\n    }\n  }\n\n  return {\n    mount: mount,\n    disable: disable,\n    destroy: destroy\n  };\n}\n\nvar ComponentConstructors = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Media: Media,\n  Direction: Direction,\n  Elements: Elements,\n  Slides: Slides,\n  Layout: Layout,\n  Clones: Clones,\n  Move: Move,\n  Controller: Controller,\n  Arrows: Arrows,\n  Autoplay: Autoplay,\n  Cover: Cover,\n  Scroll: Scroll,\n  Drag: Drag,\n  Keyboard: Keyboard,\n  LazyLoad: LazyLoad,\n  Pagination: Pagination,\n  Sync: Sync,\n  Wheel: Wheel,\n  Live: Live\n});\nvar I18N = {\n  prev: \"Previous slide\",\n  next: \"Next slide\",\n  first: \"Go to first slide\",\n  last: \"Go to last slide\",\n  slideX: \"Go to slide %s\",\n  pageX: \"Go to page %s\",\n  play: \"Start autoplay\",\n  pause: \"Pause autoplay\",\n  carousel: \"carousel\",\n  slide: \"slide\",\n  select: \"Select a slide to show\",\n  slideLabel: \"%s of %s\"\n};\nvar DEFAULTS = {\n  type: \"slide\",\n  role: \"region\",\n  speed: 400,\n  perPage: 1,\n  cloneStatus: true,\n  arrows: true,\n  pagination: true,\n  paginationKeyboard: true,\n  interval: 5e3,\n  pauseOnHover: true,\n  pauseOnFocus: true,\n  resetProgress: true,\n  easing: \"cubic-bezier(0.25, 1, 0.5, 1)\",\n  drag: true,\n  direction: \"ltr\",\n  trimSpace: true,\n  focusableNodes: \"a, button, textarea, input, select, iframe\",\n  live: true,\n  classes: CLASSES,\n  i18n: I18N,\n  reducedMotion: {\n    speed: 0,\n    rewindSpeed: 0,\n    autoplay: \"pause\"\n  }\n};\n\nfunction Fade(Splide2, Components2, options) {\n  var Slides = Components2.Slides;\n\n  function mount() {\n    EventInterface(Splide2).on([EVENT_MOUNTED, EVENT_REFRESH], init);\n  }\n\n  function init() {\n    Slides.forEach(function (Slide) {\n      Slide.style(\"transform\", \"translateX(-\" + 100 * Slide.index + \"%)\");\n    });\n  }\n\n  function start(index, done) {\n    Slides.style(\"transition\", \"opacity \" + options.speed + \"ms \" + options.easing);\n    nextTick(done);\n  }\n\n  return {\n    mount: mount,\n    start: start,\n    cancel: noop\n  };\n}\n\nfunction Slide(Splide2, Components2, options) {\n  var Move = Components2.Move,\n      Controller = Components2.Controller,\n      Scroll = Components2.Scroll;\n  var list = Components2.Elements.list;\n  var transition = apply(style, list, \"transition\");\n  var endCallback;\n\n  function mount() {\n    EventInterface(Splide2).bind(list, \"transitionend\", function (e) {\n      if (e.target === list && endCallback) {\n        cancel();\n        endCallback();\n      }\n    });\n  }\n\n  function start(index, done) {\n    var destination = Move.toPosition(index, true);\n    var position = Move.getPosition();\n    var speed = getSpeed(index);\n\n    if (abs(destination - position) >= 1 && speed >= 1) {\n      if (options.useScroll) {\n        Scroll.scroll(destination, speed, false, done);\n      } else {\n        transition(\"transform \" + speed + \"ms \" + options.easing);\n        Move.translate(destination, true);\n        endCallback = done;\n      }\n    } else {\n      Move.jump(index);\n      done();\n    }\n  }\n\n  function cancel() {\n    transition(\"\");\n    Scroll.cancel();\n  }\n\n  function getSpeed(index) {\n    var rewindSpeed = options.rewindSpeed;\n\n    if (Splide2.is(SLIDE) && rewindSpeed) {\n      var prev = Controller.getIndex(true);\n      var end = Controller.getEnd();\n\n      if (prev === 0 && index >= end || prev >= end && index === 0) {\n        return rewindSpeed;\n      }\n    }\n\n    return options.speed;\n  }\n\n  return {\n    mount: mount,\n    start: start,\n    cancel: cancel\n  };\n}\n\nvar _Splide = /*#__PURE__*/function () {\n  function _Splide(target, options) {\n    this.event = EventInterface();\n    this.Components = {};\n    this.state = State(CREATED);\n    this.splides = [];\n    this._o = {};\n    this._E = {};\n    var root = isString(target) ? query(document, target) : target;\n    assert(root, root + \" is invalid.\");\n    this.root = root;\n    options = merge({\n      label: getAttribute(root, ARIA_LABEL) || \"\",\n      labelledby: getAttribute(root, ARIA_LABELLEDBY) || \"\"\n    }, DEFAULTS, _Splide.defaults, options || {});\n\n    try {\n      merge(options, JSON.parse(getAttribute(root, DATA_ATTRIBUTE)));\n    } catch (e) {\n      assert(false, \"Invalid JSON\");\n    }\n\n    this._o = Object.create(merge({}, options));\n  }\n\n  var _proto = _Splide.prototype;\n\n  _proto.mount = function mount(Extensions, Transition) {\n    var _this = this;\n\n    var state = this.state,\n        Components2 = this.Components;\n    assert(state.is([CREATED, DESTROYED]), \"Already mounted!\");\n    state.set(CREATED);\n    this._C = Components2;\n    this._T = Transition || this._T || (this.is(FADE) ? Fade : Slide);\n    this._E = Extensions || this._E;\n    var Constructors = assign({}, ComponentConstructors, this._E, {\n      Transition: this._T\n    });\n    forOwn(Constructors, function (Component, key) {\n      var component = Component(_this, Components2, _this._o);\n      Components2[key] = component;\n      component.setup && component.setup();\n    });\n    forOwn(Components2, function (component) {\n      component.mount && component.mount();\n    });\n    this.emit(EVENT_MOUNTED);\n    addClass(this.root, CLASS_INITIALIZED);\n    state.set(IDLE);\n    this.emit(EVENT_READY);\n    return this;\n  };\n\n  _proto.sync = function sync(splide) {\n    this.splides.push({\n      splide: splide\n    });\n    splide.splides.push({\n      splide: this,\n      isParent: true\n    });\n\n    if (this.state.is(IDLE)) {\n      this._C.Sync.remount();\n\n      splide.Components.Sync.remount();\n    }\n\n    return this;\n  };\n\n  _proto.go = function go(control) {\n    this._C.Controller.go(control);\n\n    return this;\n  };\n\n  _proto.on = function on(events, callback) {\n    this.event.on(events, callback);\n    return this;\n  };\n\n  _proto.off = function off(events) {\n    this.event.off(events);\n    return this;\n  };\n\n  _proto.emit = function emit(event) {\n    var _this$event;\n\n    (_this$event = this.event).emit.apply(_this$event, [event].concat(slice(arguments, 1)));\n\n    return this;\n  };\n\n  _proto.add = function add(slides, index) {\n    this._C.Slides.add(slides, index);\n\n    return this;\n  };\n\n  _proto.remove = function remove(matcher) {\n    this._C.Slides.remove(matcher);\n\n    return this;\n  };\n\n  _proto.is = function is(type) {\n    return this._o.type === type;\n  };\n\n  _proto.refresh = function refresh() {\n    this.emit(EVENT_REFRESH);\n    return this;\n  };\n\n  _proto.destroy = function destroy(completely) {\n    if (completely === void 0) {\n      completely = true;\n    }\n\n    var event = this.event,\n        state = this.state;\n\n    if (state.is(CREATED)) {\n      EventInterface(this).on(EVENT_READY, this.destroy.bind(this, completely));\n    } else {\n      forOwn(this._C, function (component) {\n        component.destroy && component.destroy(completely);\n      }, true);\n      event.emit(EVENT_DESTROY);\n      event.destroy();\n      completely && empty(this.splides);\n      state.set(DESTROYED);\n    }\n\n    return this;\n  };\n\n  _createClass(_Splide, [{\n    key: \"options\",\n    get: function get() {\n      return this._o;\n    },\n    set: function set(options) {\n      this._C.Media.set(options, true, true);\n    }\n  }, {\n    key: \"length\",\n    get: function get() {\n      return this._C.Slides.getLength(true);\n    }\n  }, {\n    key: \"index\",\n    get: function get() {\n      return this._C.Controller.getIndex();\n    }\n  }]);\n\n  return _Splide;\n}();\n\nvar Splide = _Splide;\nSplide.defaults = {};\nSplide.STATES = STATES;\nvar CLASS_RENDERED = \"is-rendered\";\nvar RENDERER_DEFAULT_CONFIG = {\n  listTag: \"ul\",\n  slideTag: \"li\"\n};\n\nvar Style = /*#__PURE__*/function () {\n  function Style(id, options) {\n    this.styles = {};\n    this.id = id;\n    this.options = options;\n  }\n\n  var _proto2 = Style.prototype;\n\n  _proto2.rule = function rule(selector, prop, value, breakpoint) {\n    breakpoint = breakpoint || \"default\";\n    var selectors = this.styles[breakpoint] = this.styles[breakpoint] || {};\n    var styles = selectors[selector] = selectors[selector] || {};\n    styles[prop] = value;\n  };\n\n  _proto2.build = function build() {\n    var _this2 = this;\n\n    var css = \"\";\n\n    if (this.styles.default) {\n      css += this.buildSelectors(this.styles.default);\n    }\n\n    Object.keys(this.styles).sort(function (n, m) {\n      return _this2.options.mediaQuery === \"min\" ? +n - +m : +m - +n;\n    }).forEach(function (breakpoint) {\n      if (breakpoint !== \"default\") {\n        css += \"@media screen and (max-width: \" + breakpoint + \"px) {\";\n        css += _this2.buildSelectors(_this2.styles[breakpoint]);\n        css += \"}\";\n      }\n    });\n    return css;\n  };\n\n  _proto2.buildSelectors = function buildSelectors(selectors) {\n    var _this3 = this;\n\n    var css = \"\";\n    forOwn(selectors, function (styles, selector) {\n      selector = (\"#\" + _this3.id + \" \" + selector).trim();\n      css += selector + \" {\";\n      forOwn(styles, function (value, prop) {\n        if (value || value === 0) {\n          css += prop + \": \" + value + \";\";\n        }\n      });\n      css += \"}\";\n    });\n    return css;\n  };\n\n  return Style;\n}();\n\nvar SplideRenderer = /*#__PURE__*/function () {\n  function SplideRenderer(contents, options, config, defaults) {\n    this.slides = [];\n    this.options = {};\n    this.breakpoints = [];\n    merge(DEFAULTS, defaults || {});\n    merge(merge(this.options, DEFAULTS), options || {});\n    this.contents = contents;\n    this.config = assign({}, RENDERER_DEFAULT_CONFIG, config || {});\n    this.id = this.config.id || uniqueId(\"splide\");\n    this.Style = new Style(this.id, this.options);\n    this.Direction = Direction(null, null, this.options);\n    assert(this.contents.length, \"Provide at least 1 content.\");\n    this.init();\n  }\n\n  SplideRenderer.clean = function clean(splide) {\n    var _EventInterface14 = EventInterface(splide),\n        on = _EventInterface14.on;\n\n    var root = splide.root;\n    var clones = queryAll(root, \".\" + CLASS_CLONE);\n    on(EVENT_MOUNTED, function () {\n      remove(child(root, \"style\"));\n    });\n    remove(clones);\n  };\n\n  var _proto3 = SplideRenderer.prototype;\n\n  _proto3.init = function init() {\n    this.parseBreakpoints();\n    this.initSlides();\n    this.registerRootStyles();\n    this.registerTrackStyles();\n    this.registerSlideStyles();\n    this.registerListStyles();\n  };\n\n  _proto3.initSlides = function initSlides() {\n    var _this4 = this;\n\n    push(this.slides, this.contents.map(function (content, index) {\n      content = isString(content) ? {\n        html: content\n      } : content;\n      content.styles = content.styles || {};\n      content.attrs = content.attrs || {};\n\n      _this4.cover(content);\n\n      var classes = _this4.options.classes.slide + \" \" + (index === 0 ? CLASS_ACTIVE : \"\");\n      assign(content.attrs, {\n        class: (classes + \" \" + (content.attrs.class || \"\")).trim(),\n        style: _this4.buildStyles(content.styles)\n      });\n      return content;\n    }));\n\n    if (this.isLoop()) {\n      this.generateClones(this.slides);\n    }\n  };\n\n  _proto3.registerRootStyles = function registerRootStyles() {\n    var _this5 = this;\n\n    this.breakpoints.forEach(function (_ref2) {\n      var width = _ref2[0],\n          options = _ref2[1];\n\n      _this5.Style.rule(\" \", \"max-width\", unit(options.width), width);\n    });\n  };\n\n  _proto3.registerTrackStyles = function registerTrackStyles() {\n    var _this6 = this;\n\n    var Style2 = this.Style;\n    var selector = \".\" + CLASS_TRACK;\n    this.breakpoints.forEach(function (_ref3) {\n      var width = _ref3[0],\n          options = _ref3[1];\n      Style2.rule(selector, _this6.resolve(\"paddingLeft\"), _this6.cssPadding(options, false), width);\n      Style2.rule(selector, _this6.resolve(\"paddingRight\"), _this6.cssPadding(options, true), width);\n      Style2.rule(selector, \"height\", _this6.cssTrackHeight(options), width);\n    });\n  };\n\n  _proto3.registerListStyles = function registerListStyles() {\n    var _this7 = this;\n\n    var Style2 = this.Style;\n    var selector = \".\" + CLASS_LIST;\n    this.breakpoints.forEach(function (_ref4) {\n      var width = _ref4[0],\n          options = _ref4[1];\n      Style2.rule(selector, \"transform\", _this7.buildTranslate(options), width);\n\n      if (!_this7.cssSlideHeight(options)) {\n        Style2.rule(selector, \"aspect-ratio\", _this7.cssAspectRatio(options), width);\n      }\n    });\n  };\n\n  _proto3.registerSlideStyles = function registerSlideStyles() {\n    var _this8 = this;\n\n    var Style2 = this.Style;\n    var selector = \".\" + CLASS_SLIDE;\n    this.breakpoints.forEach(function (_ref5) {\n      var width = _ref5[0],\n          options = _ref5[1];\n      Style2.rule(selector, \"width\", _this8.cssSlideWidth(options), width);\n      Style2.rule(selector, \"height\", _this8.cssSlideHeight(options) || \"100%\", width);\n      Style2.rule(selector, _this8.resolve(\"marginRight\"), unit(options.gap) || \"0px\", width);\n      Style2.rule(selector + \" > img\", \"display\", options.cover ? \"none\" : \"inline\", width);\n    });\n  };\n\n  _proto3.buildTranslate = function buildTranslate(options) {\n    var _this$Direction = this.Direction,\n        resolve = _this$Direction.resolve,\n        orient = _this$Direction.orient;\n    var values = [];\n    values.push(this.cssOffsetClones(options));\n    values.push(this.cssOffsetGaps(options));\n\n    if (this.isCenter(options)) {\n      values.push(this.buildCssValue(orient(-50), \"%\"));\n      values.push.apply(values, this.cssOffsetCenter(options));\n    }\n\n    return values.filter(Boolean).map(function (value) {\n      return \"translate\" + resolve(\"X\") + \"(\" + value + \")\";\n    }).join(\" \");\n  };\n\n  _proto3.cssOffsetClones = function cssOffsetClones(options) {\n    var _this$Direction2 = this.Direction,\n        resolve = _this$Direction2.resolve,\n        orient = _this$Direction2.orient;\n    var cloneCount = this.getCloneCount();\n\n    if (this.isFixedWidth(options)) {\n      var _this$parseCssValue = this.parseCssValue(options[resolve(\"fixedWidth\")]),\n          value = _this$parseCssValue.value,\n          unit2 = _this$parseCssValue.unit;\n\n      return this.buildCssValue(orient(value) * cloneCount, unit2);\n    }\n\n    var percent = 100 * cloneCount / options.perPage;\n    return orient(percent) + \"%\";\n  };\n\n  _proto3.cssOffsetCenter = function cssOffsetCenter(options) {\n    var _this$Direction3 = this.Direction,\n        resolve = _this$Direction3.resolve,\n        orient = _this$Direction3.orient;\n\n    if (this.isFixedWidth(options)) {\n      var _this$parseCssValue2 = this.parseCssValue(options[resolve(\"fixedWidth\")]),\n          value = _this$parseCssValue2.value,\n          unit2 = _this$parseCssValue2.unit;\n\n      return [this.buildCssValue(orient(value / 2), unit2)];\n    }\n\n    var values = [];\n    var perPage = options.perPage,\n        gap = options.gap;\n    values.push(orient(50 / perPage) + \"%\");\n\n    if (gap) {\n      var _this$parseCssValue3 = this.parseCssValue(gap),\n          _value = _this$parseCssValue3.value,\n          _unit = _this$parseCssValue3.unit;\n\n      var gapOffset = (_value / perPage - _value) / 2;\n      values.push(this.buildCssValue(orient(gapOffset), _unit));\n    }\n\n    return values;\n  };\n\n  _proto3.cssOffsetGaps = function cssOffsetGaps(options) {\n    var cloneCount = this.getCloneCount();\n\n    if (cloneCount && options.gap) {\n      var orient = this.Direction.orient;\n\n      var _this$parseCssValue4 = this.parseCssValue(options.gap),\n          value = _this$parseCssValue4.value,\n          unit2 = _this$parseCssValue4.unit;\n\n      if (this.isFixedWidth(options)) {\n        return this.buildCssValue(orient(value * cloneCount), unit2);\n      }\n\n      var perPage = options.perPage;\n      var gaps = cloneCount / perPage;\n      return this.buildCssValue(orient(gaps * value), unit2);\n    }\n\n    return \"\";\n  };\n\n  _proto3.resolve = function resolve(prop) {\n    return camelToKebab(this.Direction.resolve(prop));\n  };\n\n  _proto3.cssPadding = function cssPadding(options, right) {\n    var padding = options.padding;\n    var prop = this.Direction.resolve(right ? \"right\" : \"left\", true);\n    return padding && unit(padding[prop] || (isObject(padding) ? 0 : padding)) || \"0px\";\n  };\n\n  _proto3.cssTrackHeight = function cssTrackHeight(options) {\n    var height = \"\";\n\n    if (this.isVertical()) {\n      height = this.cssHeight(options);\n      assert(height, '\"height\" is missing.');\n      height = \"calc(\" + height + \" - \" + this.cssPadding(options, false) + \" - \" + this.cssPadding(options, true) + \")\";\n    }\n\n    return height;\n  };\n\n  _proto3.cssHeight = function cssHeight(options) {\n    return unit(options.height);\n  };\n\n  _proto3.cssSlideWidth = function cssSlideWidth(options) {\n    return options.autoWidth ? \"\" : unit(options.fixedWidth) || (this.isVertical() ? \"\" : this.cssSlideSize(options));\n  };\n\n  _proto3.cssSlideHeight = function cssSlideHeight(options) {\n    return unit(options.fixedHeight) || (this.isVertical() ? options.autoHeight ? \"\" : this.cssSlideSize(options) : this.cssHeight(options));\n  };\n\n  _proto3.cssSlideSize = function cssSlideSize(options) {\n    var gap = unit(options.gap);\n    return \"calc((100%\" + (gap && \" + \" + gap) + \")/\" + (options.perPage || 1) + (gap && \" - \" + gap) + \")\";\n  };\n\n  _proto3.cssAspectRatio = function cssAspectRatio(options) {\n    var heightRatio = options.heightRatio;\n    return heightRatio ? \"\" + 1 / heightRatio : \"\";\n  };\n\n  _proto3.buildCssValue = function buildCssValue(value, unit2) {\n    return \"\" + value + unit2;\n  };\n\n  _proto3.parseCssValue = function parseCssValue(value) {\n    if (isString(value)) {\n      var number = parseFloat(value) || 0;\n      var unit2 = value.replace(/\\d*(\\.\\d*)?/, \"\") || \"px\";\n      return {\n        value: number,\n        unit: unit2\n      };\n    }\n\n    return {\n      value: value,\n      unit: \"px\"\n    };\n  };\n\n  _proto3.parseBreakpoints = function parseBreakpoints() {\n    var _this9 = this;\n\n    var breakpoints = this.options.breakpoints;\n    this.breakpoints.push([\"default\", this.options]);\n\n    if (breakpoints) {\n      forOwn(breakpoints, function (options, width) {\n        _this9.breakpoints.push([width, merge(merge({}, _this9.options), options)]);\n      });\n    }\n  };\n\n  _proto3.isFixedWidth = function isFixedWidth(options) {\n    return !!options[this.Direction.resolve(\"fixedWidth\")];\n  };\n\n  _proto3.isLoop = function isLoop() {\n    return this.options.type === LOOP;\n  };\n\n  _proto3.isCenter = function isCenter(options) {\n    if (options.focus === \"center\") {\n      if (this.isLoop()) {\n        return true;\n      }\n\n      if (this.options.type === SLIDE) {\n        return !this.options.trimSpace;\n      }\n    }\n\n    return false;\n  };\n\n  _proto3.isVertical = function isVertical() {\n    return this.options.direction === TTB;\n  };\n\n  _proto3.buildClasses = function buildClasses() {\n    var options = this.options;\n    return [CLASS_ROOT, CLASS_ROOT + \"--\" + options.type, CLASS_ROOT + \"--\" + options.direction, options.drag && CLASS_ROOT + \"--draggable\", options.isNavigation && CLASS_ROOT + \"--nav\", CLASS_ACTIVE, !this.config.hidden && CLASS_RENDERED].filter(Boolean).join(\" \");\n  };\n\n  _proto3.buildAttrs = function buildAttrs(attrs) {\n    var attr = \"\";\n    forOwn(attrs, function (value, key) {\n      attr += value ? \" \" + camelToKebab(key) + \"=\\\"\" + value + \"\\\"\" : \"\";\n    });\n    return attr.trim();\n  };\n\n  _proto3.buildStyles = function buildStyles(styles) {\n    var style = \"\";\n    forOwn(styles, function (value, key) {\n      style += \" \" + camelToKebab(key) + \":\" + value + \";\";\n    });\n    return style.trim();\n  };\n\n  _proto3.renderSlides = function renderSlides() {\n    var _this10 = this;\n\n    var tag = this.config.slideTag;\n    return this.slides.map(function (content) {\n      return \"<\" + tag + \" \" + _this10.buildAttrs(content.attrs) + \">\" + (content.html || \"\") + \"</\" + tag + \">\";\n    }).join(\"\");\n  };\n\n  _proto3.cover = function cover(content) {\n    var styles = content.styles,\n        _content$html = content.html,\n        html = _content$html === void 0 ? \"\" : _content$html;\n\n    if (this.options.cover && !this.options.lazyLoad) {\n      var src = html.match(/<img.*?src\\s*=\\s*(['\"])(.+?)\\1.*?>/);\n\n      if (src && src[2]) {\n        styles.background = \"center/cover no-repeat url('\" + src[2] + \"')\";\n      }\n    }\n  };\n\n  _proto3.generateClones = function generateClones(contents) {\n    var classes = this.options.classes;\n    var count = this.getCloneCount();\n    var slides = contents.slice();\n\n    while (slides.length < count) {\n      push(slides, slides);\n    }\n\n    push(slides.slice(-count).reverse(), slides.slice(0, count)).forEach(function (content, index) {\n      var attrs = assign({}, content.attrs, {\n        class: content.attrs.class + \" \" + classes.clone\n      });\n      var clone = assign({}, content, {\n        attrs: attrs\n      });\n      index < count ? contents.unshift(clone) : contents.push(clone);\n    });\n  };\n\n  _proto3.getCloneCount = function getCloneCount() {\n    if (this.isLoop()) {\n      var options = this.options;\n\n      if (options.clones) {\n        return options.clones;\n      }\n\n      var perPage = max.apply(void 0, this.breakpoints.map(function (_ref6) {\n        var options2 = _ref6[1];\n        return options2.perPage;\n      }));\n      return perPage * ((options.flickMaxPages || 1) + 1);\n    }\n\n    return 0;\n  };\n\n  _proto3.renderArrows = function renderArrows() {\n    var html = \"\";\n    html += \"<div class=\\\"\" + this.options.classes.arrows + \"\\\">\";\n    html += this.renderArrow(true);\n    html += this.renderArrow(false);\n    html += \"</div>\";\n    return html;\n  };\n\n  _proto3.renderArrow = function renderArrow(prev) {\n    var _this$options = this.options,\n        classes = _this$options.classes,\n        i18n = _this$options.i18n;\n    var attrs = {\n      class: classes.arrow + \" \" + (prev ? classes.prev : classes.next),\n      type: \"button\",\n      ariaLabel: prev ? i18n.prev : i18n.next\n    };\n    return \"<button \" + this.buildAttrs(attrs) + \"><svg xmlns=\\\"\" + XML_NAME_SPACE + \"\\\" viewBox=\\\"0 0 \" + SIZE + \" \" + SIZE + \"\\\" width=\\\"\" + SIZE + \"\\\" height=\\\"\" + SIZE + \"\\\"><path d=\\\"\" + (this.options.arrowPath || PATH) + \"\\\" /></svg></button>\";\n  };\n\n  _proto3.html = function html() {\n    var _this$config = this.config,\n        rootClass = _this$config.rootClass,\n        listTag = _this$config.listTag,\n        arrows = _this$config.arrows,\n        beforeTrack = _this$config.beforeTrack,\n        afterTrack = _this$config.afterTrack,\n        slider = _this$config.slider,\n        beforeSlider = _this$config.beforeSlider,\n        afterSlider = _this$config.afterSlider;\n    var html = \"\";\n    html += \"<div id=\\\"\" + this.id + \"\\\" class=\\\"\" + this.buildClasses() + \" \" + (rootClass || \"\") + \"\\\">\";\n    html += \"<style>\" + this.Style.build() + \"</style>\";\n\n    if (slider) {\n      html += beforeSlider || \"\";\n      html += \"<div class=\\\"splide__slider\\\">\";\n    }\n\n    html += beforeTrack || \"\";\n\n    if (arrows) {\n      html += this.renderArrows();\n    }\n\n    html += \"<div class=\\\"splide__track\\\">\";\n    html += \"<\" + listTag + \" class=\\\"splide__list\\\">\";\n    html += this.renderSlides();\n    html += \"</\" + listTag + \">\";\n    html += \"</div>\";\n    html += afterTrack || \"\";\n\n    if (slider) {\n      html += \"</div>\";\n      html += afterSlider || \"\";\n    }\n\n    html += \"</div>\";\n    return html;\n  };\n\n  return SplideRenderer;\n}();\n\nexport { CLASSES, CLASS_ACTIVE, CLASS_ARROW, CLASS_ARROWS, CLASS_ARROW_NEXT, CLASS_ARROW_PREV, CLASS_CLONE, CLASS_CONTAINER, CLASS_FOCUS_IN, CLASS_INITIALIZED, CLASS_LIST, CLASS_LOADING, CLASS_NEXT, CLASS_OVERFLOW, CLASS_PAGINATION, CLASS_PAGINATION_PAGE, CLASS_PREV, CLASS_PROGRESS, CLASS_PROGRESS_BAR, CLASS_ROOT, CLASS_SLIDE, CLASS_SPINNER, CLASS_SR, CLASS_TOGGLE, CLASS_TOGGLE_PAUSE, CLASS_TOGGLE_PLAY, CLASS_TRACK, CLASS_VISIBLE, DEFAULTS, EVENT_ACTIVE, EVENT_ARROWS_MOUNTED, EVENT_ARROWS_UPDATED, EVENT_AUTOPLAY_PAUSE, EVENT_AUTOPLAY_PLAY, EVENT_AUTOPLAY_PLAYING, EVENT_CLICK, EVENT_DESTROY, EVENT_DRAG, EVENT_DRAGGED, EVENT_DRAGGING, EVENT_END_INDEX_CHANGED, EVENT_HIDDEN, EVENT_INACTIVE, EVENT_LAZYLOAD_LOADED, EVENT_MOUNTED, EVENT_MOVE, EVENT_MOVED, EVENT_NAVIGATION_MOUNTED, EVENT_OVERFLOW, EVENT_PAGINATION_MOUNTED, EVENT_PAGINATION_UPDATED, EVENT_READY, EVENT_REFRESH, EVENT_RESIZE, EVENT_RESIZED, EVENT_SCROLL, EVENT_SCROLLED, EVENT_SHIFTED, EVENT_SLIDE_KEYDOWN, EVENT_UPDATED, EVENT_VISIBLE, EventBinder, EventInterface, FADE, LOOP, LTR, RTL, RequestInterval, SLIDE, STATUS_CLASSES, Splide, SplideRenderer, State, TTB, Throttle, Splide as default };\n", "import { Splide } from '@splidejs/splide';\nimport { defaultConfig } from '../../../common/config';\nimport { TIMING } from '../../../common/config/constants';\nimport type {\n  ISplideLifecycleManager,\n  SplideInstance,\n  SplideFullConfig\n} from '../../../common/config/types';\n\n/**\n * Splide lifecycle manager\n * Handles Splide instance creation, destruction, and lifecycle management\n */\nexport class SplideLifecycleManager implements ISplideLifecycleManager {\n  private splideInstance: SplideInstance | undefined;\n  private isDestroyed = false;\n\n  /**\n   * Initialize Splide instance with the provided configuration\n   */\n  initialize(config: SplideFullConfig): SplideInstance | undefined {\n    try {\n      // Destroy existing instance if any\n      this.destroy();\n\n      // Create new Splide instance\n      this.splideInstance = new Splide(`.${defaultConfig.slider.dom.splideClass}`, config) as SplideInstance;\n      this.splideInstance.mount();\n      this.isDestroyed = false;\n\n      // Add event listeners to verify arrow functionality\n      if (this.splideInstance && defaultConfig.env === 'development') {\n        this.addDebugEventListeners();\n      }\n\n      return this.splideInstance;\n    } catch {\n      // Silently handle initialization errors\n      return;\n    }\n  }\n\n  /**\n   * Destroy the current Splide instance\n   */\n  destroy(): void {\n    if (this.splideInstance && !this.isDestroyed) {\n      try {\n        this.splideInstance.destroy();\n      } catch {\n        // Silently handle destruction errors\n      } finally {\n        delete this.splideInstance;\n        this.isDestroyed = true;\n      }\n    }\n  }\n\n  /**\n   * Check if Splide instance is initialized and not destroyed\n   */\n  isInitialized(): boolean {\n    return this.splideInstance !== null && !this.isDestroyed;\n  }\n\n  /**\n   * Get the current Splide instance\n   */\n  getInstance(): SplideInstance | undefined {\n    if (this.isInitialized()) {\n      return this.splideInstance;\n    }\n    return;\n  }\n\n  /**\n   * Reinitialize Splide with new configuration\n   */\n  reinitialize(config: SplideFullConfig): SplideInstance | null {\n    this.destroy();\n    return this.initialize(config);\n  }\n\n  /**\n   * Check if the Splide container exists in the DOM\n   */\n  isContainerAvailable(): boolean {\n    try {\n      const container = document.querySelector(`.${defaultConfig.slider.dom.splideClass}`);\n      return container !== null;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * Initialize with delay to wait for DOM readiness\n   */\n  initializeWithDelay(config: SplideFullConfig, delay = TIMING.CHECK_INTERVAL): void {\n    setTimeout(() => {\n      if (this.isContainerAvailable()) {\n        this.initialize(config);\n      }\n    }, delay);\n  }\n\n  /**\n   * Get Splide instance status information\n   */\n  getStatus(): {\n    isInitialized: boolean;\n    isDestroyed: boolean;\n    hasInstance: boolean;\n    containerAvailable: boolean;\n  } {\n    return {\n      isInitialized: this.isInitialized(),\n      isDestroyed: this.isDestroyed,\n      hasInstance: this.splideInstance !== null,\n      containerAvailable: this.isContainerAvailable()\n    };\n  }\n\n  /**\n   * Add debug event listeners to track arrow functionality\n   */\n  private addDebugEventListeners(): void {\n    if (!this.splideInstance) {\n      return;\n    }\n\n    // Listen for slide changes to verify navigation\n    this.splideInstance.on('moved', (newIndex: number, prevIndex: number) => {\n      // Debug info available in development mode\n      if (defaultConfig.env === 'development') {\n        // Track slide movement for debugging\n        let direction = 'forward';\n        if (newIndex <= prevIndex) {\n          direction = 'backward';\n        }\n        // Store debug info without console output\n        (globalThis as unknown as { __splideDebug: unknown }).__splideDebug = {\n          lastMove: { from: prevIndex, to: newIndex, direction },\n          timestamp: Date.now()\n        };\n      }\n    });\n  }\n}\n", "import * as DOMUtils from '../utils/dom-utils';\nimport { defaultConfig } from '../../common/config';\nimport { ARRAY_CONSTANTS, SLIDESHOW_CONSTANTS } from '../../common/config/constants';\nimport { SplideConfigManager } from './splide/splide-config-manager';\nimport { SplideDOMBuilder } from './splide/splide-dom-builder';\nimport { SlideDataManager } from './splide/slide-data-manager';\nimport { SplideLifecycleManager } from './splide/splide-lifecycle-manager';\nimport type { FlarumVnode, ProcessedSlideData } from '../../common/config/types';\n\n/**\n * Slideshow manager for header advertisements\n * Coordinates between different modules to manage the complete slideshow lifecycle\n */\nexport class SlideshowManager {\n    private readonly configManager: SplideConfigManager;\n    private readonly domBuilder: SplideDOMBuilder;\n    private readonly dataManager: SlideDataManager;\n    private readonly lifecycleManager: SplideLifecycleManager;\n    private readonly checkTime = defaultConfig.slider.checkTime;\n\n    constructor() {\n        this.configManager = new SplideConfigManager();\n        this.domBuilder = new SplideDOMBuilder();\n        this.dataManager = new SlideDataManager();\n        this.lifecycleManager = new SplideLifecycleManager();\n    }\n\n    /**\n     * Initialize and attach slideshow to the DOM\n     */\n    attachAdvertiseHeader(_vdom: FlarumVnode): void {\n        try {\n            this.destroy(); // Clean up any existing instance\n\n            // Fetch slide data\n            const slideData = this.dataManager.fetchSlideData();\n            if (slideData.length === ARRAY_CONSTANTS.EMPTY_LENGTH) {\n                return; // No slides to display\n            }\n\n            // Build DOM structure\n            const container = this.domBuilder.createContainer();\n            const splide = this.domBuilder.createSplideElement(container);\n            const list = this.domBuilder.createSplideTrack(splide);\n\n            // Populate slides\n            const slideCount = this.populateSlides(list, slideData);\n            this.domBuilder.createPagination(splide);\n            // Remove manual arrow creation - let Splide.js auto-generate arrows with proper SVG content\n            // this.domBuilder.createNavigation(splide);\n\n            // Append to DOM\n            this.domBuilder.appendToDOM(container);\n\n            // Initialize Splide after DOM attachment\n            setTimeout(() => {\n                this.initializeSplide(slideCount);\n            }, this.checkTime);\n        } catch {\n            // Silently handle slideshow creation errors\n        }\n    }\n\n    /**\n     * Populate slides with processed slide data\n     */\n    private populateSlides(wrapper: HTMLElement, slideData: ProcessedSlideData[]): number {\n        let slideCount = ARRAY_CONSTANTS.EMPTY_LENGTH;\n\n        for (const slide of slideData) {\n            if (slide.isValid) {\n                const slideElement = this.domBuilder.createSlide(slide.imageSrc, slide.imageLink);\n                DOMUtils.appendChild(wrapper, slideElement);\n                slideCount += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT;\n            }\n        }\n\n        return slideCount;\n    }\n\n\n\n    /**\n     * Initialize Splide with calculated configuration\n     */\n    private initializeSplide(slideCount: number): void {\n        try {\n            const transitionTime = this.dataManager.getTransitionTime();\n            const configResult = this.configManager.calculateConfiguration(slideCount, transitionTime);\n\n            // Validate configuration\n            const validationResult = this.configManager.validateConfiguration(configResult.finalConfig);\n            if (!validationResult.isValid) {\n                return;\n            }\n\n            // Initialize Splide instance\n            this.lifecycleManager.initialize(configResult.finalConfig);\n        } catch {\n            // Silently handle Splide initialization errors\n        }\n    }\n\n    /**\n     * Destroy slideshow instance\n     */\n    destroy(): void {\n        this.lifecycleManager.destroy();\n        this.domBuilder.cleanup();\n    }\n\n}\n", "import { ERROR_HANDLING } from '../../common/config/constants';\nimport type { ErrorLogEntry } from '../../common/config/types';\n\n/**\n * Error handling utility for the Header Advertisement extension\n */\nexport class ErrorHandler {\n    private static instance: ErrorHandler;\n    private errorLog: ErrorLogEntry[] = [];\n    private isInitialized = false;\n\n    private constructor() {\n        // Private constructor for singleton pattern\n    }\n\n    /**\n     * Get singleton instance\n     */\n    public static getInstance(): ErrorHandler {\n        if (!ErrorHandler.instance) {\n            ErrorHandler.instance = new ErrorHandler();\n        }\n        return ErrorHandler.instance;\n    }\n\n    /**\n     * Initialize error handler\n     */\n    public initialize(): boolean {\n        try {\n            if (this.isInitialized) {\n                return true;\n            }\n\n            // Set up global error handling\n            this.setupGlobalErrorHandling();\n            this.isInitialized = true;\n            return true;\n        } catch {\n            return false;\n        }\n    }\n\n    /**\n     * Handle synchronous errors\n     */\n    public handleSync<TResult>(fn: () => TResult, context: string): TResult | false {\n        try {\n            return fn();\n        } catch (error) {\n            this.logError(error as Error, context);\n            return false;\n        }\n    }\n\n    /**\n     * Handle asynchronous errors\n     */\n    public handleAsync<TResult>(fn: () => Promise<TResult>, context: string): Promise<TResult | false> {\n        return fn().catch((error) => {\n            this.logError(error as Error, context);\n            return false;\n        });\n    }\n\n    /**\n     * Log error with context\n     */\n    private logError(error: Error, context: string): void {\n        try {\n            const entry: ErrorLogEntry = {\n                timestamp: new Date(),\n                error,\n                context,\n            };\n\n            this.errorLog.push(entry);\n\n            // Keep log size manageable\n            if (this.errorLog.length > ERROR_HANDLING.MAX_ERROR_LOG_ENTRIES) {\n                this.errorLog.shift();\n            }\n\n            // Log to console in development\n            if (process.env.NODE_ENV === 'development') {\n                // Development logging would go here\n                // console.warn(`[HeaderAdvertisement] Error in ${context}:`, error);\n            }\n        } catch {\n            // Silently handle logging errors\n        }\n    }\n\n    /**\n     * Set up global error handling\n     */\n    private setupGlobalErrorHandling(): void {\n        try {\n            // Handle unhandled promise rejections\n            globalThis.addEventListener('unhandledrejection', (event) => {\n                this.logError(\n                    new Error(String(event.reason)),\n                    'Unhandled Promise Rejection'\n                );\n            });\n        } catch {\n            // Silently handle setup errors\n        }\n    }\n\n    /**\n     * Get error log (for debugging)\n     */\n    public getErrorLog(): ErrorLogEntry[] {\n        return [...this.errorLog];\n    }\n\n    /**\n     * Clear error log\n     */\n    public clearErrorLog(): void {\n        this.errorLog = [];\n    }\n}\n", "import app from 'flarum/forum/app';\nimport { defaultConfig } from '../../common/config';\n\n/**\n * Configuration manager for the Header Advertisement extension\n */\nexport class ConfigManager {\n    private static instance: ConfigManager;\n\n    private constructor() {\n        // Private constructor for singleton pattern\n    }\n\n    /**\n     * Get singleton instance\n     */\n    public static getInstance(): ConfigManager {\n        if (!ConfigManager.instance) {\n            ConfigManager.instance = new ConfigManager();\n        }\n        return ConfigManager.instance;\n    }\n\n    /**\n     * Check if current page is tags page\n     */\n    public isTagsPage(): boolean {\n        try {\n            const currentRoute = app.current.get('routeName');\n            return currentRoute === 'tags';\n        } catch {\n            // Fallback: check URL\n            try {\n                return globalThis.location.pathname.includes('/tags');\n            } catch {\n                return false;\n            }\n        }\n    }\n\n    /**\n     * Get extension configuration\n     */\n    public getConfig(): typeof defaultConfig {\n        return defaultConfig;\n    }\n\n    /**\n     * Check if slideshow is properly configured\n     */\n    public isSlideshowConfigured(): boolean {\n        try {\n            const FIRST_SLIDE_INDEX = 1;\n            const SLIDE_INCREMENT = 1;\n            // Check if at least one slide is configured\n            for (let slideIndex = FIRST_SLIDE_INDEX; slideIndex <= defaultConfig.slider.maxSlides; slideIndex += SLIDE_INCREMENT) {\n                const image = app.forum.attribute(`wusong8899-header-advertisement.Image${slideIndex}`);\n                if (image) {\n                    return true;\n                }\n            }\n            return false;\n        } catch {\n            return false;\n        }\n    }\n}\n", "import { extend } from 'flarum/common/extend';\nimport app from 'flarum/forum/app';\nimport HeaderPrimary from 'flarum/forum/components/HeaderPrimary';\n\nimport { SlideshowManager } from './components/slideshow-manager';\nimport { UIManager } from './components/ui-manager';\nimport { ErrorHandler } from './utils/error-handler';\nimport { ConfigManager } from './utils/config-manager';\nimport { isMobileDevice } from './utils/mobile-detection';\nimport { defaultConfig } from '../common/config';\n\n/**\n * Main extension initializer for Header Advertisement\n */\napp.initializers.add(defaultConfig.app.extensionId, () => {\n    const errorHandler = ErrorHandler.getInstance();\n    const configManager = ConfigManager.getInstance();\n\n    // Initialize error handling\n    if (!errorHandler.initialize()) {\n        return;\n    }\n\n    const slideshowManager = new SlideshowManager();\n    const uiManager = new UIManager();\n\n    extend(HeaderPrimary.prototype, 'view', function headerPrimaryViewExtension(vnode: unknown) {\n        errorHandler.handleSync(() => {\n            if (configManager.isTagsPage()) {\n                initializeExtension(vnode, slideshowManager, uiManager);\n            }\n        }, 'HeaderPrimary view extension');\n    });\n});\n\n/**\n * Initialize extension components\n */\nconst initializeExtension = (\n    vnode: unknown,\n    slideshowManager: SlideshowManager,\n    _uiManager: UIManager\n): void => {\n    try {\n        const configManager = ConfigManager.getInstance();\n\n        // Setup slideshow (only if configured)\n        if (configManager.isSlideshowConfigured()) {\n            try {\n                slideshowManager.attachAdvertiseHeader(vnode);\n            } catch {\n                // Slideshow setup failed, but continue with other features\n            }\n        }\n\n        // UI Manager is available for future use if needed\n\n        // Add header icon for non-logged users on mobile devices only\n        if (!app.session.user && isMobileDevice()) {\n            addHeaderIcon();\n        }\n\n    } catch {\n        // Silently handle initialization errors\n    }\n}\n\n\n\n/**\n * Add header icon for branding (mobile only)\n */\nconst addHeaderIcon = (): void => {\n    let headerIconContainer = document.getElementById(defaultConfig.ui.headerIconId);\n\n    if (headerIconContainer === null) {\n        // Get header icon URL from settings, fallback to default config\n        const headerIconUrl = app.forum.attribute('wusong8899-header-advertisement.HeaderIconUrl') || defaultConfig.ui.headerIconUrl;\n\n        headerIconContainer = document.createElement(\"div\");\n        headerIconContainer.id = defaultConfig.ui.headerIconId;\n        headerIconContainer.className = 'HeaderIcon-container mobile-only';\n        headerIconContainer.innerHTML = `<img src=\"${headerIconUrl}\" alt=\"Header Icon\" class=\"HeaderIcon-image\" />`;\n\n        // Find the navigation container (.App-backControl) and insert the icon as the last child\n        const navigationContainer = document.querySelector(\"#app-navigation .Navigation.ButtonGroup.App-backControl\");\n        if (navigationContainer) {\n            // Insert as the last child of the navigation container (to the right of the drawer button)\n            navigationContainer.appendChild(headerIconContainer);\n        }\n    }\n}\n"], "names": ["querySelector", "selector", "querySelectorAll", "createElement", "tagName", "options", "innerHTML", "element", "key", "value", "append<PERSON><PERSON><PERSON>", "parent", "child", "prepend<PERSON>hild", "removeElement", "MOBILE_DETECTION", "ERROR_HANDLING", "SLIDESHOW_CONSTANTS", "ARRAY_CONSTANTS", "TIMING", "DOM_ELEMENTS", "CSS_CLASSES", "EXTENSION_CONFIG", "defaultConfig", "SPLIDE_CONFIG_CONSTANTS", "SplideConfigManager", "slideCount", "transitionTime", "enableLoop", "responsiveConfig", "finalConfig", "config", "errors", "width", "breakpointConfig", "widthNum", "breakpoints", "splideType", "autoplayConfig", "isMobileDevice", "userAgent", "SplideDOMBuilder", "container", "DOMUtils.createElement", "className", "splide", "DOMUtils.appendChild", "track", "list", "imageSrc", "imageLink", "slide", "clickHandler", "pagination", "_splide", "contentContainer", "DOMUtils.querySelector", "DOMUtils.prependChild", "DOMUtils.removeElement", "existingContainer", "navElements", "DOMUtils.querySelectorAll", "SlideDataManager", "rawData", "slideIndex", "data", "parsedTime", "url", "forum", "app", "attrFn", "_defineProperties", "target", "props", "i", "descriptor", "_createClass", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "MEDIA_PREFERS_REDUCED_MOTION", "CREATED", "MOUNTED", "IDLE", "MOVING", "SCROLLING", "DRAGGING", "DESTROYED", "STATES", "empty", "array", "slice", "arrayLike", "start", "end", "apply", "func", "nextTick", "noop", "raf", "typeOf", "type", "subject", "isObject", "isNull", "isArray", "isFunction", "isString", "isUndefined", "isHTMLElement", "toArray", "for<PERSON>ach", "values", "iteratee", "includes", "push", "items", "toggleClass", "elm", "classes", "add", "name", "addClass", "append", "children", "before", "nodes", "ref", "node", "matches", "children2", "ownKeys", "forOwn", "object", "right", "assign", "source", "merge", "omit", "keys", "removeAttribute", "elms", "attrs", "attr", "setAttribute", "value2", "create", "tag", "style", "prop", "display", "display2", "focus", "getAttribute", "hasClass", "rect", "remove", "parseHtml", "html", "prevent", "e", "stopPropagation", "query", "queryAll", "removeClass", "timeOf", "unit", "PROJECT_CODE", "DATA_ATTRIBUTE", "assert", "condition", "message", "min", "max", "floor", "ceil", "abs", "approximatelyEqual", "x", "y", "epsilon", "between", "number", "exclusive", "minimum", "maximum", "clamp", "sign", "format", "string", "replacements", "replacement", "pad", "ids", "uniqueId", "prefix", "EventBinder", "listeners", "bind", "targets", "events", "callback", "forEachEvent", "event", "namespace", "isEventTarget", "remover", "unbind", "listener", "dispatch", "detail", "bubbles", "events2", "eventNS", "fragment", "destroy", "EVENT_MOUNTED", "EVENT_READY", "EVENT_MOVE", "EVENT_MOVED", "EVENT_CLICK", "EVENT_ACTIVE", "EVENT_INACTIVE", "EVENT_VISIBLE", "EVENT_HIDDEN", "EVENT_REFRESH", "EVENT_UPDATED", "EVENT_RESIZE", "EVENT_RESIZED", "EVENT_DRAG", "EVENT_DRAGGING", "EVENT_DRAGGED", "EVENT_SCROLL", "EVENT_SCROLLED", "EVENT_OVERFLOW", "EVENT_DESTROY", "EVENT_ARROWS_MOUNTED", "EVENT_ARROWS_UPDATED", "EVENT_PAGINATION_MOUNTED", "EVENT_PAGINATION_UPDATED", "EVENT_NAVIGATION_MOUNTED", "EVENT_AUTOPLAY_PLAY", "EVENT_AUTOPLAY_PLAYING", "EVENT_AUTOPLAY_PAUSE", "EVENT_LAZYLOAD_LOADED", "EVENT_SLIDE_KEYDOWN", "EVENT_SHIFTED", "EVENT_END_INDEX_CHANGED", "EventInterface", "Splide2", "bus", "binder", "on", "emit", "RequestInterval", "interval", "onInterval", "onUpdate", "limit", "now", "startTime", "rate", "id", "paused", "count", "update", "pause", "resume", "cancel", "rewind", "set", "time", "isPaused", "State", "initialState", "state", "is", "states", "<PERSON>hrottle", "duration", "Media", "Components2", "reducedMotion", "queries", "setup", "isMin", "n", "m", "register", "completely", "options2", "queryList", "destroyed", "direction", "merged", "merged2", "entry", "reduce", "enable", "opts", "base", "notify", "ARROW", "ARROW_LEFT", "ARROW_RIGHT", "ARROW_UP", "ARROW_DOWN", "RTL", "TTB", "ORIENTATION_MAP", "Direction", "resolve", "axisOnly", "index", "match", "offset", "orient", "ROLE", "TAB_INDEX", "DISABLED", "ARIA_PREFIX", "ARIA_CONTROLS", "ARIA_CURRENT", "ARIA_SELECTED", "ARIA_LABEL", "ARIA_LABELLEDBY", "ARIA_HIDDEN", "ARIA_ORIENTATION", "ARIA_ROLEDESCRIPTION", "ARIA_LIVE", "ARIA_BUSY", "ARIA_ATOMIC", "ALL_ATTRIBUTES", "CLASS_PREFIX", "STATUS_CLASS_PREFIX", "CLASS_ROOT", "CLASS_TRACK", "CLASS_LIST", "CLASS_SLIDE", "CLASS_CLONE", "CLASS_CONTAINER", "CLASS_ARROWS", "CLASS_ARROW", "CLASS_ARROW_PREV", "CLASS_ARROW_NEXT", "CLASS_PAGINATION", "CLASS_PAGINATION_PAGE", "CLASS_PROGRESS", "CLASS_PROGRESS_BAR", "CLASS_TOGGLE", "CLASS_SPINNER", "CLASS_SR", "CLASS_INITIALIZED", "CLASS_ACTIVE", "CLASS_PREV", "CLASS_NEXT", "CLASS_VISIBLE", "CLASS_LOADING", "CLASS_FOCUS_IN", "CLASS_OVERFLOW", "STATUS_CLASSES", "CLASSES", "closest", "from", "FRICTION", "LOG_INTERVAL", "POINTER_DOWN_EVENTS", "POINTER_MOVE_EVENTS", "POINTER_UP_EVENTS", "Elements", "_EventInterface", "root", "i18n", "elements", "slides", "rootClasses", "trackClasses", "isUsingKey", "collect", "init", "mount", "getClasses", "find", "role", "SLIDE", "LOOP", "FADE", "Slide$1", "Components", "isNavigation", "updateOnMove", "slideFocus", "styles", "label", "isClone", "listen", "self", "initNavigation", "onMove", "controls", "Slide2", "curr", "updateActivity", "updateVisibility", "active", "isActive", "visible", "isVisible", "hidden", "style$1", "useContainer", "trackRect", "slideRect", "left", "<PERSON><PERSON><PERSON><PERSON>", "distance", "diff", "Slides", "_EventInterface2", "_Components2$Elements", "Slides2", "forEach$1", "Slide1", "get", "excludeClones", "filter", "getIn", "page", "Controller", "getAt", "observeImages", "remove$1", "matcher", "images", "length", "img", "<PERSON><PERSON><PERSON><PERSON>", "isEnough", "Layout", "_EventInterface3", "_Components2$Elements2", "styleSlides", "vertical", "rootRect", "overflow", "resize", "cssPadding", "force", "newRect", "cssTrackHeight", "cssSlideWidth", "cssSlideHeight", "isOverflow", "padding", "height", "cssHeight", "cssSlideSize", "gap", "listSize", "slideSize", "withoutGap", "Slide", "getGap", "totalSize", "sliderSize", "getPadding", "MULTIPLIER", "<PERSON><PERSON><PERSON>", "clones", "cloneCount", "remount", "observe", "computeCloneCount", "generate", "isHead", "clone", "cloneDeep", "clones2", "fixedSize", "fixedCount", "Move", "_EventInterface4", "_Components2$Layout", "_Components2$Directio", "_Components2$Elements3", "Transition", "reposition", "jump", "move", "dest", "prev", "canShift", "translate", "shift", "getPosition", "toPosition", "position", "preventLoop", "destination", "loop", "toIndex", "exceededMax", "exceededMin", "backwards", "excess", "getLimit", "size", "minDistance", "trimming", "trim", "shifted", "exceededLimit", "_EventInterface5", "_Components2$Slides", "omitEnd", "isLoop", "isSlide", "getNext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getPrev", "currIndex", "endIndex", "prevIndex", "perMove", "perPage", "onResized", "getEnd", "go", "control", "allowSameIndex", "isBusy", "parse", "setIndex", "scroll", "snap", "_ref", "indicator", "computeDestIndex", "hasFocus", "snapPage", "computeMovableDestIndex", "toPage", "toDest", "getIndex", "XML_NAME_SPACE", "PATH", "SIZE", "Arrows", "placeholder", "wrapper", "next", "created", "wrapperClasses", "arrows", "enabled", "createArrows", "createArrow", "prev2", "arrow", "nextIndex", "prevLabel", "next<PERSON><PERSON><PERSON>", "INTERVAL_DATA_ATTRIBUTE", "Autoplay", "_EventInterface6", "onAnimationFrame", "_Components2$Elements4", "toggle", "autoplay", "hovered", "focused", "stopped", "play", "autoToggle", "stop", "bar", "Cover", "_EventInterface7", "cover", "cover2", "BOUNCE_DIFF_THRESHOLD", "BOUNCE_DURATION", "FRICTION_FACTOR", "BASE_VELOCITY", "MIN_DURATION", "<PERSON><PERSON>", "_EventInterface8", "friction", "clear", "onScrolled", "noConstrain", "noDistance", "onEnd", "to", "easing", "t", "easingFunc", "SCROLL_LISTENER_OPTIONS", "Drag", "_EventInterface9", "_Components2$Directio2", "basePosition", "baseEvent", "prevBaseEvent", "isFree", "dragging", "exceeded", "clickPrevented", "disabled", "onPointerDown", "onClick", "drag", "disable", "is<PERSON><PERSON>ch", "isTouchEvent", "isDraggable", "onPointerMove", "onPointerUp", "save", "constrain", "diffCoord", "expired", "diffTime", "hasExceeded", "isSliderDirection", "shouldStart", "velocity", "computeVelocity", "computeDestination", "thresholds", "isObj", "mouse", "touch", "orthogonal", "coordOf", "getBaseEvent", "target2", "noDrag", "isDragging", "NORMALIZATION_MAP", "normalizeKey", "KEYBOARD_EVENT", "Keyboard", "_EventInterface10", "keyboard", "onKeydown", "_disabled", "SRC_DATA_ATTRIBUTE", "SRCSET_DATA_ATTRIBUTE", "IMAGE_SELECTOR", "LazyLoad", "_EventInterface11", "off", "isSequential", "entries", "loadNext", "check", "src", "srcset", "spinner", "load", "onLoad", "Pagination", "paginationClasses", "createPagination", "getDirection", "li", "button", "text", "dir", "nextPage", "item", "_button", "TRIGGER_KEYS", "Sync", "sync", "navigate", "Wheel", "_EventInterface12", "lastTime", "onWheel", "deltaY", "timeStamp", "_min", "sleep", "shouldPrevent", "SR_REMOVAL_DELAY", "Live", "_EventInterface13", "sr", "ComponentConstructors", "I18N", "DEFAULTS", "Fade", "done", "transition", "endCallback", "speed", "getSpeed", "rewindSpeed", "_Splide", "_proto", "Extensions", "_this", "Constructors", "Component", "component", "_this$event", "Splide", "SplideLifecycleManager", "delay", "newIndex", "SlideshowManager", "_vdom", "slideData", "slideElement", "config<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fn", "context", "error", "ConfigManager", "<PERSON><PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "slideshowManager", "extend", "HeaderPrimary", "vnode", "initializeExtension", "_uiManager", "addHeaderIcon", "headerIconContainer", "headerIconUrl", "navigationContainer"], "mappings": "iCASO,MAAMA,GAAiBC,GAAsC,CAChE,GAAI,CACA,OAAO,SAAS,cAAcA,CAAQ,GAAK,EAC/C,MAAQ,CACJ,MAAO,EACX,CACJ,EAKaC,GAAoBD,GAA0C,CACvE,GAAI,CACA,OAAO,SAAS,iBAAiBA,CAAQ,CAC7C,MAAQ,CACJ,OAAO,SAAS,iBAAiB,EAAE,CACvC,CACJ,EAgBaE,GAAgB,CACzBC,EACAC,EAA6B,CAAA,EAC7BC,EAAY,KACE,CACd,GAAI,CACA,MAAMC,EAAU,SAAS,cAAcH,CAAO,EAG9C,SAAW,CAACI,EAAKC,CAAK,IAAK,OAAO,QAAQJ,CAAO,EACzCG,IAAQ,YACRD,EAAQ,UAAY,OAAOE,CAAK,EACzBD,IAAQ,KACfD,EAAQ,GAAK,OAAOE,CAAK,EAEzBF,EAAQ,aAAaC,EAAK,OAAOC,CAAK,CAAC,EAI/C,OAAIH,IACAC,EAAQ,UAAYD,GAGjBC,CACX,MAAQ,CACJ,OAAO,SAAS,cAAc,KAAK,CACvC,CACJ,EAKaG,GAAc,CAACC,EAAiBC,IAAyB,CAClE,GAAI,CACAD,EAAO,YAAYC,CAAK,CAC5B,MAAQ,CAER,CACJ,EAKaC,GAAe,CAACF,EAAiBC,IAAyB,CACnE,GAAI,CACAD,EAAO,QAAQC,CAAK,CACxB,MAAQ,CAER,CACJ,EAKaE,GAAiBP,GAA2B,CACrD,GAAI,CACAA,EAAQ,OAAA,CACZ,MAAQ,CAER,CACJ,ECjGaQ,GAAmB,CAC9B,wBAAyB,EACzB,yBAA0B,CAC5B,EAGaC,GAAiB,CAC5B,sBAAuB,GACvB,kBAAmB,IACnB,iBAAkB,EAClB,iBAAkB,GAClB,oBAAqB,IACrB,oBAAqB,IACrB,sBAAuB,EACvB,sBAAuB,EACzB,EAuBaC,GAAsB,CACjC,gBAAiB,EACjB,oBAAqB,CAEvB,EAGaC,GAAkB,CAC7B,aAAc,EACd,YAAa,EACb,gBAAiB,GACjB,iBAAkB,EAClB,iBAAkB,EACpB,EAGaC,GAAS,CACpB,eAAgB,GAChB,oBAAqB,IACrB,wBAAyB,GAC3B,EAGaC,GAAe,CAC1B,uBAAwB,oBACxB,eAAgB,yBAClB,EAGaC,GAAc,CAQzB,UAAW,UACb,EAeaC,GAAmB,CAC9B,GAAI,kCACJ,mBAAoB,kCACpB,WAAY,GACZ,gBAAiB,0CACnB,EC7FaC,EAA4B,CACvC,IAAM,aACN,IAAK,CACH,YAAaD,GAAiB,GAC9B,kBAAmBA,GAAiB,kBAAA,EAEtC,OAAQ,CACN,UAAWA,GAAiB,WAC5B,sBAAuBH,GAAO,wBAC9B,UAAWA,GAAO,eAClB,kBAAmBA,GAAO,oBAC1B,IAAK,CACH,YAAaC,GAAa,uBAC1B,YAAaC,GAAY,SAAA,EAE3B,OAAQ,CACN,IAAK,OACL,KAAM,OACN,MAAO,SACP,QAAS,EACT,WAAY,GACZ,OAAQ,EAAA,CACV,EAEF,GAAI,CACF,aAAcD,GAAa,eAC3B,cAAeE,GAAiB,eAAA,CAEpC,ECrBME,GAA0B,CAC9B,oBAAqB,EAErB,gBAAiB,EACjB,gBAAiB,EACjB,iBAAkB,CACpB,EAMO,MAAMC,EAAoD,CAI/D,uBAAuBC,EAAoBC,EAAiD,CAC1F,MAAMC,EAAa,KAAK,iBAAiBF,CAAU,EAC7CG,EAAmB,KAAK,0BAAA,EACxBC,EAAc,KAAK,iBAAiBH,EAAgBC,CAAU,EAEpE,MAAO,CACL,WAAAA,EACA,iBAAAC,EACA,YAAAC,CAAA,CAEJ,CAKA,sBAAsBC,EAA4C,CAChE,MAAMC,EAAmB,CAAA,EAiBzB,GAdID,EAAO,UAAY,OAAOA,EAAO,UAAa,WAC/C,OAAOA,EAAO,SAAS,UAAa,UAAYA,EAAO,SAAS,UAAYb,GAAgB,eAC7Fc,EAAO,KAAK,yCAAyC,GAGnD,OAAOD,EAAO,KAAQ,UAAY,CAACA,EAAO,IAAI,MAAM,SAAS,IAC/DC,EAAO,KAAK,qDAAqD,GAG/D,OAAOD,EAAO,SAAY,UAAYA,EAAO,SAAWb,GAAgB,eAC1Ec,EAAO,KAAK,+BAA+B,EAIzCD,EAAO,aAAe,OAAOA,EAAO,aAAgB,SACtD,SAAW,CAACE,EAAOC,CAAgB,IAAK,OAAO,QAAQH,EAAO,WAAW,EAAG,CAC1E,MAAMI,EAAW,OAAOF,CAAK,GACzB,OAAO,MAAME,CAAQ,GAAKA,GAAYjB,GAAgB,eACxDc,EAAO,KAAK,6BAA6BC,CAAK,EAAE,GAE9C,OAAOC,EAAiB,SAAY,UAAYA,EAAiB,SAAWhB,GAAgB,eAC9Fc,EAAO,KAAK,kCAAkCC,CAAK,EAAE,GAEnD,OAAOC,EAAiB,KAAQ,UAAY,CAACA,EAAiB,IAAI,MAAM,SAAS,IACnFF,EAAO,KAAK,8BAA8BC,CAAK,EAAE,CAErD,CAGF,MAAO,CACL,QAASD,EAAO,SAAWd,GAAgB,aAC3C,OAAAc,CAAA,CAEJ,CAKQ,iBAAiBN,EAA6B,CAGpD,OAAOA,GAAcF,GAAwB,mBAC/C,CAKQ,2BAA8C,CACpD,MAAO,CACL,OAAQ,CACN,QAASA,GAAwB,gBACjC,IAAK,MAAA,EAEP,OAAQ,CACN,QAASA,GAAwB,gBACjC,IAAK,MAAA,EAEP,QAAS,CACP,QAASA,GAAwB,iBACjC,IAAKD,EAAc,OAAO,OAAO,GAAA,CACnC,CAEJ,CAKQ,iBACNI,EACAC,EACkB,CAClB,MAAMQ,EAAiC,CACrC,IAAK,CAAE,QAASZ,GAAwB,gBAAiB,IAAK,MAAA,EAC9D,IAAK,CAAE,QAASA,GAAwB,gBAAiB,IAAK,MAAA,EAC9D,KAAM,CAAE,QAASA,GAAwB,iBAAkB,IAAKD,EAAc,OAAO,OAAO,GAAA,CAAI,EAGlG,IAAIc,EAA+B,QAC/BT,IACFS,EAAa,QAGf,IAAIC,EAAsE,GAC1E,OAAIX,EAAiBT,GAAgB,eACnCoB,EAAiB,CACf,SAAUX,EACV,aAAc,EAAA,GAIX,CACL,KAAMU,EACN,SAAUC,EACV,IAAKf,EAAc,OAAO,OAAO,IACjC,MAAOA,EAAc,OAAO,OAAO,MACnC,QAASA,EAAc,OAAO,OAAO,QACrC,YAAAa,EACA,WAAYb,EAAc,OAAO,OAAO,WACxC,OAAQA,EAAc,OAAO,OAAO,OACpC,MAAO,IAEP,UAAW,MACX,UAAW,gEACX,KAAM,CACJ,KAAM,iBACN,KAAM,aACN,MAAO,oBACP,KAAM,mBACN,OAAQ,iBACR,MAAO,gBACP,KAAM,iBACN,MAAO,gBAAA,CACT,CAEJ,CACF,CC5JO,MAAMgB,GAAiB,IAAe,CACzC,GAAI,CACA,KAAM,CAAE,UAAAC,GAAc,UAKtB,OAJwBA,EAAU,UAC9BzB,GAAiB,wBACjBA,GAAiB,wBAAA,IAEM,MAC/B,MAAQ,CACJ,MAAO,EACX,CACJ,ECXO,MAAM0B,EAA+C,CAM1D,iBAA+B,CAC7B,KAAK,yBAAA,EAEL,MAAMC,EAAYC,GAAuB,MAAO,CAC9C,GAAIpB,EAAc,OAAO,IAAI,YAC7B,UAAW,iCAAA,CACZ,EAED,YAAK,UAAYmB,EACVA,CACT,CAKA,oBAAoBA,EAAqC,CACvD,IAAIE,EAAY,UAAUrB,EAAc,OAAO,IAAI,WAAW,oBAG1DgB,OACFK,GAAa,qBAGf,MAAMC,EAASF,GAAuB,MAAO,CAC3C,UAAAC,CAAA,CACD,EAEDE,OAAAA,GAAqBJ,EAAWG,CAAM,EAC/BA,CACT,CAKA,kBAAkBA,EAAkC,CAClD,MAAME,EAAQJ,GAAuB,MAAO,CAC1C,UAAW,qCAAA,CACZ,EAEKK,EAAOL,GAAuB,KAAM,CACxC,UAAW,mCAAA,CACZ,EAEDG,OAAAA,GAAqBC,EAAOC,CAAI,EAChCF,GAAqBD,EAAQE,CAAK,EAC3BC,CACT,CAKA,YAAYC,EAAkBC,EAAgC,CAC5D,MAAMC,EAAQR,GAAuB,KAAM,CACzC,UAAW,qCAAA,CACZ,EAED,IAAIS,EAAe,GACnB,OAAIF,IACFE,EAAe,yBAAyBF,CAAS,KAInDC,EAAM,UAAY,iBAAiBC,CAAY,UAAUH,CAAQ,oCAE1DE,CACT,CAKA,iBAAiBN,EAA2B,CAC1C,MAAMQ,EAAaV,GAAuB,KAAM,CAC9C,UAAW,oBAAA,CACZ,EACDG,GAAqBD,EAAQQ,CAAU,CACzC,CAOA,iBAAiBC,EAA4B,CAc7C,CAKA,YAAYZ,EAA8B,CACxC,MAAMa,EAAmBC,GAAuB,qBAAqB,EACjED,GACFE,GAAsBF,EAAkBb,CAAS,CAErD,CAKA,SAAgB,CACV,KAAK,YACPgB,GAAuB,KAAK,SAAS,EACrC,OAAO,KAAK,UAEhB,CAKA,cAAwC,CACtC,OAAO,KAAK,SACd,CAKQ,0BAAiC,CACvC,MAAMC,EAAoBH,GAAuB,IAAIjC,EAAc,OAAO,IAAI,WAAW,EAAE,EACvFoC,GACFD,GAAuBC,CAAiB,EAG1C,MAAMC,EAAcC,GAA0B,WAAW,EACzD,UAAWtD,KAAWqD,EACpBF,GAAuBnD,CAAO,CAElC,CACF,CC5IO,MAAMuD,EAA8C,CAApD,aAAA,CACL,KAAiB,UAAYvC,EAAc,OAAO,SAAA,CAKlD,gBAAuC,CACrC,MAAMwC,EAA0B,CAAA,EAGhC,QAASC,EAAa/C,GAAoB,oBAAqB+C,GAAc,KAAK,UAAWA,GAAc/C,GAAoB,gBAAiB,CAC9I,MAAMgC,EAAW,KAAK,kBAAkB,wCAAwCe,CAAU,EAAE,EACtFd,EAAY,KAAK,kBAAkB,uCAAuCc,CAAU,EAAE,EAExFf,GACFc,EAAQ,KAAK,CACX,SAAU,OAAOd,CAAQ,EACzB,UAAW,OAAOC,GAAa,EAAE,EACjC,WAAAc,CAAA,CACD,CAEL,CAGA,OAAO,KAAK,iBAAiBD,CAAO,CACtC,CAKA,kBAAkBE,EAAwC,CACxD,MAAMjC,EAAmB,CAAA,EAEzB,GAAI,CAAC,MAAM,QAAQiC,CAAI,EACrB,OAAAjC,EAAO,KAAK,6BAA6B,EAClC,CAAE,QAAS,GAAO,OAAAA,CAAA,EAG3B,GAAIiC,EAAK,SAAW/C,GAAgB,aAClC,OAAAc,EAAO,KAAK,wBAAwB,EAC7B,CAAE,QAAS,GAAO,OAAAA,CAAA,EAG3B,UAAWmB,KAASc,GACd,CAACd,EAAM,UAAY,OAAOA,EAAM,UAAa,WAC/CnB,EAAO,KAAK,kCAAkCmB,EAAM,UAAU,EAAE,EAG9DA,EAAM,WAAa,OAAOA,EAAM,WAAc,UAChDnB,EAAO,KAAK,gCAAgCmB,EAAM,UAAU,EAAE,GAG5D,OAAOA,EAAM,YAAe,UAAYA,EAAM,WAAalC,GAAoB,sBACjFe,EAAO,KAAK,wBAAwBmB,EAAM,UAAU,EAAE,EAIpDA,EAAM,UAAY,CAAC,KAAK,WAAWA,EAAM,QAAQ,GACnDnB,EAAO,KAAK,sCAAsCmB,EAAM,UAAU,EAAE,EAIlEA,EAAM,WAAa,CAAC,KAAK,WAAWA,EAAM,SAAS,GACrDnB,EAAO,KAAK,qCAAqCmB,EAAM,UAAU,EAAE,EAIvE,MAAO,CACL,QAASnB,EAAO,SAAWd,GAAgB,aAC3C,OAAAc,CAAA,CAEJ,CAKA,mBAA4B,CAC1B,MAAML,EAAiB,KAAK,kBAAkB,gDAAgD,EAC9F,GAAIA,EAAgB,CAClB,MAAMuC,EAAa,OAAO,SAAS,OAAOvC,CAAc,EAAG,EAAE,EAC7D,GAAI,CAAC,OAAO,MAAMuC,CAAU,GAAKA,EAAahD,GAAgB,aAC5D,OAAOgD,CAEX,CACA,OAAO3C,EAAc,OAAO,qBAC9B,CAKQ,iBAAiBwC,EAA+C,CACtE,OAAOA,EAAQ,IAAKZ,IAAW,CAC7B,SAAUA,EAAM,SAChB,UAAWA,EAAM,UACjB,WAAYA,EAAM,WAClB,QAAS,KAAK,aAAaA,CAAK,CAAA,EAChC,EAAE,OAAQA,GAAUA,EAAM,OAAO,CACrC,CAKQ,aAAaA,EAA8B,CACjD,MAAO,GACLA,EAAM,UACN,OAAOA,EAAM,UAAa,UAC1B,KAAK,WAAWA,EAAM,QAAQ,GAC9B,OAAOA,EAAM,YAAe,UAC5BA,EAAM,WAAajC,GAAgB,aAEvC,CAKQ,WAAWiD,EAAsB,CACvC,GAAI,CAEF,MAAO,EADW,IAAI,IAAIA,CAAG,CAE/B,MAAQ,CAEN,OAAOA,EAAI,WAAW,GAAG,GAAKA,EAAI,WAAW,IAAI,GAAKA,EAAI,WAAW,OAAO,CAC9E,CACF,CAKQ,kBAAkB3D,EAAsB,CAC9C,GAAI,CACF,MAAM4D,EAAQC,IAAOA,GAAI,MACnBC,EAASF,GAASA,EAAM,UAC9B,OAAI,OAAOE,GAAW,WACbA,EAAO,KAAKF,EAAO5D,CAAG,EAE/B,MACF,MAAQ,CACN,MACF,CACF,CACF,CC1JA,SAAS+D,GAAkBC,EAAQC,EAAO,CAAE,QAASC,EAAI,EAAGA,EAAID,EAAM,OAAQC,IAAK,CAAE,IAAIC,EAAaF,EAAMC,CAAC,EAAGC,EAAW,WAAaA,EAAW,YAAc,GAAOA,EAAW,aAAe,GAAU,UAAWA,IAAYA,EAAW,SAAW,IAAM,OAAO,eAAeH,EAAQG,EAAW,IAAKA,CAAU,CAAG,CAAE,CAE5T,SAASC,GAAaC,EAAaC,EAAYC,EAAa,CAAE,OAAID,GAAYP,GAAkBM,EAAY,UAAWC,CAAU,EAAiE,OAAO,eAAeD,EAAa,YAAa,CAAE,SAAU,GAAO,EAAUA,CAAa,CAE5R;AAAA;AAAA;AAAA;AAAA;AAAA,GAMA,IAAIG,GAA+B,mCAC/BC,GAAU,EACVC,GAAU,EACVC,GAAO,EACPC,GAAS,EACTC,GAAY,EACZC,GAAW,EACXC,GAAY,EACZC,GAAS,CACX,QAASP,GACT,QAASC,GACT,KAAMC,GACN,OAAQC,GACR,UAAWC,GACX,SAAUC,GACV,UAAWC,EACb,EAEA,SAASE,GAAMC,EAAO,CACpBA,EAAM,OAAS,CACjB,CAEA,SAASC,GAAMC,EAAWC,EAAOC,EAAK,CACpC,OAAO,MAAM,UAAU,MAAM,KAAKF,EAAWC,EAAOC,CAAG,CACzD,CAEA,SAASC,EAAMC,EAAM,CACnB,OAAOA,EAAK,KAAK,MAAMA,EAAM,CAAC,IAAI,EAAE,OAAOL,GAAM,UAAW,CAAC,CAAC,CAAC,CACjE,CAEA,IAAIM,GAAW,WAEXC,GAAO,UAAgB,CAAC,EAE5B,SAASC,GAAIH,EAAM,CACjB,OAAO,sBAAsBA,CAAI,CACnC,CAEA,SAASI,GAAOC,EAAMC,EAAS,CAC7B,OAAO,OAAOA,IAAYD,CAC5B,CAEA,SAASE,GAASD,EAAS,CACzB,MAAO,CAACE,GAAOF,CAAO,GAAKF,GAAO,SAAUE,CAAO,CACrD,CAEA,IAAIG,GAAU,MAAM,QAChBC,GAAaX,EAAMK,GAAQ,UAAU,EACrCO,GAAWZ,EAAMK,GAAQ,QAAQ,EACjCQ,GAAcb,EAAMK,GAAQ,WAAW,EAE3C,SAASI,GAAOF,EAAS,CACvB,OAAOA,IAAY,IACrB,CAEA,SAASO,GAAcP,EAAS,CAC9B,GAAI,CACF,OAAOA,aAAoBA,EAAQ,cAAc,aAAe,QAAQ,WAC1E,MAAY,CACV,MAAO,EACT,CACF,CAEA,SAASQ,GAAQrG,EAAO,CACtB,OAAOgG,GAAQhG,CAAK,EAAIA,EAAQ,CAACA,CAAK,CACxC,CAEA,SAASsG,GAAQC,EAAQC,EAAU,CACjCH,GAAQE,CAAM,EAAE,QAAQC,CAAQ,CAClC,CAEA,SAASC,GAASxB,EAAOjF,EAAO,CAC9B,OAAOiF,EAAM,QAAQjF,CAAK,EAAI,EAChC,CAEA,SAAS0G,GAAKzB,EAAO0B,EAAO,CAC1B,OAAA1B,EAAM,KAAK,MAAMA,EAAOoB,GAAQM,CAAK,CAAC,EAC/B1B,CACT,CAEA,SAAS2B,GAAYC,EAAKC,EAASC,EAAK,CAClCF,GACFP,GAAQQ,EAAS,SAAUE,EAAM,CAC3BA,GACFH,EAAI,UAAUE,EAAM,MAAQ,QAAQ,EAAEC,CAAI,CAE9C,CAAC,CAEL,CAEA,SAASC,GAASJ,EAAKC,EAAS,CAC9BF,GAAYC,EAAKX,GAASY,CAAO,EAAIA,EAAQ,MAAM,GAAG,EAAIA,EAAS,EAAI,CACzE,CAEA,SAASI,GAAOhH,EAAQiH,EAAU,CAChCb,GAAQa,EAAUjH,EAAO,YAAY,KAAKA,CAAM,CAAC,CACnD,CAEA,SAASkH,GAAOC,EAAOC,EAAK,CAC1BhB,GAAQe,EAAO,SAAUE,EAAM,CAC7B,IAAIrH,GAAUoH,GAAOC,GAAM,WAEvBrH,GACFA,EAAO,aAAaqH,EAAMD,CAAG,CAEjC,CAAC,CACH,CAEA,SAASE,GAAQX,EAAKrH,EAAU,CAC9B,OAAO4G,GAAcS,CAAG,IAAMA,EAAI,mBAAwBA,EAAI,SAAS,KAAKA,EAAKrH,CAAQ,CAC3F,CAEA,SAAS2H,GAASjH,EAAQV,EAAU,CAClC,IAAIiI,EAAYvH,EAASgF,GAAMhF,EAAO,QAAQ,EAAI,CAAA,EAClD,OAAOV,EAAWiI,EAAU,OAAO,SAAUtH,EAAO,CAClD,OAAOqH,GAAQrH,EAAOX,CAAQ,CAChC,CAAC,EAAIiI,CACP,CAEA,SAAStH,GAAMD,EAAQV,EAAU,CAC/B,OAAOA,EAAW2H,GAASjH,EAAQV,CAAQ,EAAE,CAAC,EAAIU,EAAO,iBAC3D,CAEA,IAAIwH,GAAU,OAAO,KAErB,SAASC,GAAOC,EAAQpB,EAAUqB,EAAO,CACvC,OAAID,IACDC,EAAQH,GAAQE,CAAM,EAAE,QAAO,EAAKF,GAAQE,CAAM,GAAG,QAAQ,SAAU7H,EAAK,CAC3EA,IAAQ,aAAeyG,EAASoB,EAAO7H,CAAG,EAAGA,CAAG,CAClD,CAAC,EAGI6H,CACT,CAEA,SAASE,GAAOF,EAAQ,CACtB,OAAA1C,GAAM,UAAW,CAAC,EAAE,QAAQ,SAAU6C,EAAQ,CAC5CJ,GAAOI,EAAQ,SAAU/H,EAAOD,EAAK,CACnC6H,EAAO7H,CAAG,EAAIgI,EAAOhI,CAAG,CAC1B,CAAC,CACH,CAAC,EACM6H,CACT,CAEA,SAASI,GAAMJ,EAAQ,CACrB,OAAA1C,GAAM,UAAW,CAAC,EAAE,QAAQ,SAAU6C,EAAQ,CAC5CJ,GAAOI,EAAQ,SAAU/H,EAAOD,EAAK,CAC/BiG,GAAQhG,CAAK,EACf4H,EAAO7H,CAAG,EAAIC,EAAM,MAAK,EAChB8F,GAAS9F,CAAK,EACvB4H,EAAO7H,CAAG,EAAIiI,GAAM,CAAA,EAAIlC,GAAS8B,EAAO7H,CAAG,CAAC,EAAI6H,EAAO7H,CAAG,EAAI,CAAA,EAAIC,CAAK,EAEvE4H,EAAO7H,CAAG,EAAIC,CAElB,CAAC,CACH,CAAC,EACM4H,CACT,CAEA,SAASK,GAAKL,EAAQM,EAAM,CAC1B5B,GAAQ4B,GAAQR,GAAQE,CAAM,EAAG,SAAU7H,EAAK,CAC9C,OAAO6H,EAAO7H,CAAG,CACnB,CAAC,CACH,CAEA,SAASoI,GAAgBC,EAAMC,EAAO,CACpC/B,GAAQ8B,EAAM,SAAUvB,EAAK,CAC3BP,GAAQ+B,EAAO,SAAUC,EAAM,CAC7BzB,GAAOA,EAAI,gBAAgByB,CAAI,CACjC,CAAC,CACH,CAAC,CACH,CAEA,SAASC,EAAaH,EAAMC,EAAOrI,EAAO,CACpC8F,GAASuC,CAAK,EAChBV,GAAOU,EAAO,SAAUG,EAAQxB,EAAM,CACpCuB,EAAaH,EAAMpB,EAAMwB,CAAM,CACjC,CAAC,EAEDlC,GAAQ8B,EAAM,SAAUvB,EAAK,CAC3Bd,GAAO/F,CAAK,GAAKA,IAAU,GAAKmI,GAAgBtB,EAAKwB,CAAK,EAAIxB,EAAI,aAAawB,EAAO,OAAOrI,CAAK,CAAC,CACrG,CAAC,CAEL,CAEA,SAASyI,GAAOC,EAAKL,EAAOnI,EAAQ,CAClC,IAAI2G,EAAM,SAAS,cAAc6B,CAAG,EAEpC,OAAIL,IACFnC,GAASmC,CAAK,EAAIpB,GAASJ,EAAKwB,CAAK,EAAIE,EAAa1B,EAAKwB,CAAK,GAGlEnI,GAAUgH,GAAOhH,EAAQ2G,CAAG,EACrBA,CACT,CAEA,SAAS8B,GAAM9B,EAAK+B,EAAM5I,EAAO,CAC/B,GAAImG,GAAYnG,CAAK,EACnB,OAAO,iBAAiB6G,CAAG,EAAE+B,CAAI,EAG9B7C,GAAO/F,CAAK,IACf6G,EAAI,MAAM+B,CAAI,EAAI,GAAK5I,EAE3B,CAEA,SAAS6I,GAAQhC,EAAKiC,EAAU,CAC9BH,GAAM9B,EAAK,UAAWiC,CAAQ,CAChC,CAEA,SAASC,GAAMlC,EAAK,CAClBA,EAAI,WAAgBA,EAAI,UAAY,GAAMA,EAAI,MAAM,CAClD,cAAe,EACnB,CAAG,CACH,CAEA,SAASmC,GAAanC,EAAKyB,EAAM,CAC/B,OAAOzB,EAAI,aAAayB,CAAI,CAC9B,CAEA,SAASW,GAASpC,EAAK1E,EAAW,CAChC,OAAO0E,GAAOA,EAAI,UAAU,SAAS1E,CAAS,CAChD,CAEA,SAAS+G,GAAKnF,EAAQ,CACpB,OAAOA,EAAO,sBAAqB,CACrC,CAEA,SAASoF,GAAO9B,EAAO,CACrBf,GAAQe,EAAO,SAAUE,EAAM,CACzBA,GAAQA,EAAK,YACfA,EAAK,WAAW,YAAYA,CAAI,CAEpC,CAAC,CACH,CAEA,SAAS6B,GAAUC,EAAM,CACvB,OAAOlJ,GAAM,IAAI,UAAS,EAAG,gBAAgBkJ,EAAM,WAAW,EAAE,IAAI,CACtE,CAEA,SAASC,GAAQC,EAAGC,EAAiB,CACnCD,EAAE,eAAc,EAEZC,IACFD,EAAE,gBAAe,EACjBA,EAAE,yBAAwB,EAE9B,CAEA,SAASE,GAAMvJ,EAAQV,EAAU,CAC/B,OAAOU,GAAUA,EAAO,cAAcV,CAAQ,CAChD,CAEA,SAASkK,GAASxJ,EAAQV,EAAU,CAClC,OAAOA,EAAW0F,GAAMhF,EAAO,iBAAiBV,CAAQ,CAAC,EAAI,CAAA,CAC/D,CAEA,SAASmK,GAAY9C,EAAKC,EAAS,CACjCF,GAAYC,EAAKC,EAAS,EAAK,CACjC,CAEA,SAAS8C,GAAOL,EAAG,CACjB,OAAOA,EAAE,SACX,CAEA,SAASM,GAAK7J,EAAO,CACnB,OAAOkG,GAASlG,CAAK,EAAIA,EAAQA,EAAQA,EAAQ,KAAO,EAC1D,CAEA,IAAI8J,GAAe,SACfC,GAAiB,QAAUD,GAE/B,SAASE,GAAOC,EAAWC,EAAS,CAClC,GAAI,CAACD,EACH,MAAM,IAAI,MAAM,IAAMH,GAAe,MAAQI,GAAW,GAAG,CAE/D,CAEA,IAAIC,GAAM,KAAK,IACXC,GAAM,KAAK,IACXC,GAAQ,KAAK,MACbC,GAAO,KAAK,KACZC,EAAM,KAAK,IAEf,SAASC,GAAmBC,EAAGC,EAAGC,EAAS,CACzC,OAAOJ,EAAIE,EAAIC,CAAC,EAAIC,CACtB,CAEA,SAASC,GAAQC,EAAQJ,EAAGC,EAAGI,EAAW,CACxC,IAAIC,EAAUZ,GAAIM,EAAGC,CAAC,EAClBM,EAAUZ,GAAIK,EAAGC,CAAC,EACtB,OAAOI,EAAYC,EAAUF,GAAUA,EAASG,EAAUD,GAAWF,GAAUA,GAAUG,CAC3F,CAEA,SAASC,GAAMJ,EAAQJ,EAAGC,EAAG,CAC3B,IAAIK,EAAUZ,GAAIM,EAAGC,CAAC,EAClBM,EAAUZ,GAAIK,EAAGC,CAAC,EACtB,OAAOP,GAAIC,GAAIW,EAASF,CAAM,EAAGG,CAAO,CAC1C,CAEA,SAASE,GAAKT,EAAG,CACf,MAAO,EAAEA,EAAI,GAAK,EAAEA,EAAI,EAC1B,CAMA,SAASU,GAAOC,EAAQC,EAAc,CACpC,OAAA/E,GAAQ+E,EAAc,SAAUC,EAAa,CAC3CF,EAASA,EAAO,QAAQ,KAAM,GAAKE,CAAW,CAChD,CAAC,EACMF,CACT,CAEA,SAASG,GAAIV,EAAQ,CACnB,OAAOA,EAAS,GAAK,IAAMA,EAAS,GAAKA,CAC3C,CAEA,IAAIW,GAAM,CAAA,EAEV,SAASC,GAASC,EAAQ,CACxB,MAAO,GAAKA,EAASH,GAAIC,GAAIE,CAAM,GAAKF,GAAIE,CAAM,GAAK,GAAK,CAAC,CAC/D,CAEA,SAASC,IAAc,CACrB,IAAIC,EAAY,CAAA,EAEhB,SAASC,EAAKC,EAASC,EAAQC,EAAUpM,EAAS,CAChDqM,EAAaH,EAASC,EAAQ,SAAUhI,EAAQmI,EAAOC,EAAW,CAChE,IAAIC,EAAiB,qBAAsBrI,EACvCsI,EAAUD,EAAgBrI,EAAO,oBAAoB,KAAKA,EAAQmI,EAAOF,EAAUpM,CAAO,EAAImE,EAAO,eAAkB,KAAKA,EAAQiI,CAAQ,EAChJI,EAAgBrI,EAAO,iBAAiBmI,EAAOF,EAAUpM,CAAO,EAAImE,EAAO,YAAeiI,CAAQ,EAClGJ,EAAU,KAAK,CAAC7H,EAAQmI,EAAOC,EAAWH,EAAUK,CAAO,CAAC,CAC9D,CAAC,CACH,CAEA,SAASC,EAAOR,EAASC,EAAQC,EAAU,CACzCC,EAAaH,EAASC,EAAQ,SAAUhI,EAAQmI,EAAOC,EAAW,CAChEP,EAAYA,EAAU,OAAO,SAAUW,EAAU,CAC/C,OAAIA,EAAS,CAAC,IAAMxI,GAAUwI,EAAS,CAAC,IAAML,GAASK,EAAS,CAAC,IAAMJ,IAAc,CAACH,GAAYO,EAAS,CAAC,IAAMP,IAChHO,EAAS,CAAC,EAAC,EACJ,IAGF,EACT,CAAC,CACH,CAAC,CACH,CAEA,SAASC,EAASzI,EAAQ6B,EAAM6G,EAAQ,CACtC,IAAIlD,EACAmD,EAAU,GAEd,OAAI,OAAO,aAAgB,WACzBnD,EAAI,IAAI,YAAY3D,EAAM,CACxB,QAAS8G,EACT,OAAQD,CAChB,CAAO,GAEDlD,EAAI,SAAS,YAAY,aAAa,EACtCA,EAAE,gBAAgB3D,EAAM8G,EAAS,GAAOD,CAAM,GAGhD1I,EAAO,cAAcwF,CAAC,EACfA,CACT,CAEA,SAAS0C,EAAaH,EAASC,EAAQvF,EAAU,CAC/CF,GAAQwF,EAAS,SAAU/H,EAAQ,CACjCA,GAAUuC,GAAQyF,EAAQ,SAAUY,EAAS,CAC3CA,EAAQ,MAAM,GAAG,EAAE,QAAQ,SAAUC,EAAS,CAC5C,IAAIC,EAAWD,EAAQ,MAAM,GAAG,EAChCpG,EAASzC,EAAQ8I,EAAS,CAAC,EAAGA,EAAS,CAAC,CAAC,CAC3C,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAEA,SAASC,GAAU,CACjBlB,EAAU,QAAQ,SAAUpI,EAAM,CAChCA,EAAK,CAAC,EAAC,CACT,CAAC,EACDwB,GAAM4G,CAAS,CACjB,CAEA,MAAO,CACL,KAAMC,EACN,OAAQS,EACR,SAAUE,EACV,QAASM,CACb,CACA,CAEA,IAAIC,GAAgB,UAChBC,GAAc,QACdC,GAAa,OACbC,GAAc,QACdC,GAAc,QACdC,GAAe,SACfC,GAAiB,WACjBC,GAAgB,UAChBC,GAAe,SACfC,EAAgB,UAChBC,EAAgB,UAChBC,GAAe,SACfC,GAAgB,UAChBC,GAAa,OACbC,GAAiB,WACjBC,GAAgB,UAChBC,GAAe,SACfC,GAAiB,WACjBC,GAAiB,WACjBC,GAAgB,UAChBC,GAAuB,iBACvBC,GAAuB,iBACvBC,GAA2B,qBAC3BC,GAA2B,qBAC3BC,GAA2B,qBAC3BC,GAAsB,gBACtBC,GAAyB,mBACzBC,GAAuB,iBACvBC,GAAwB,kBACxBC,GAAsB,KACtBC,GAAgB,KAChBC,GAA0B,KAE9B,SAASC,EAAeC,EAAS,CAC/B,IAAIC,EAAMD,EAAUA,EAAQ,MAAM,IAAM,SAAS,uBAAsB,EACnEE,EAASvD,GAAW,EAExB,SAASwD,EAAGpD,EAAQC,EAAU,CAC5BkD,EAAO,KAAKD,EAAK5I,GAAQ0F,CAAM,EAAE,KAAK,GAAG,EAAG,SAAUxC,EAAG,CACvDyC,EAAS,MAAMA,EAAUhG,GAAQuD,EAAE,MAAM,EAAIA,EAAE,OAAS,EAAE,CAC5D,CAAC,CACH,CAEA,SAAS6F,EAAKlD,EAAO,CACnBgD,EAAO,SAASD,EAAK/C,EAAOhH,GAAM,UAAW,CAAC,CAAC,CACjD,CAEA,OAAI8J,GACFA,EAAQ,MAAM,GAAGd,GAAegB,EAAO,OAAO,EAGzCpH,GAAOoH,EAAQ,CACpB,IAAKD,EACL,GAAIE,EACJ,IAAK7J,EAAM4J,EAAO,OAAQD,CAAG,EAC7B,KAAMG,CACV,CAAG,CACH,CAEA,SAASC,GAAgBC,EAAUC,EAAYC,EAAUC,EAAO,CAC9D,IAAIC,EAAM,KAAK,IACXC,EACAC,EAAO,EACPC,EACAC,EAAS,GACTC,EAAQ,EAEZ,SAASC,GAAS,CAChB,GAAI,CAACF,EAAQ,CAIX,GAHAF,EAAON,EAAWnF,IAAKuF,EAAG,EAAKC,GAAaL,EAAU,CAAC,EAAI,EAC3DE,GAAYA,EAASI,CAAI,EAErBA,GAAQ,IACVL,EAAU,EACVI,EAAYD,EAAG,EAEXD,GAAS,EAAEM,GAASN,GACtB,OAAOQ,EAAK,EAIhBJ,EAAKnK,GAAIsK,CAAM,CACjB,CACF,CAEA,SAAS5K,EAAM8K,EAAQ,CACrBA,GAAUC,EAAM,EAChBR,EAAYD,EAAG,GAAMQ,EAASN,EAAON,EAAW,GAChDQ,EAAS,GACTD,EAAKnK,GAAIsK,CAAM,CACjB,CAEA,SAASC,GAAQ,CACfH,EAAS,EACX,CAEA,SAASM,GAAS,CAChBT,EAAYD,EAAG,EACfE,EAAO,EAEHJ,GACFA,EAASI,CAAI,CAEjB,CAEA,SAASO,GAAS,CAChBN,GAAM,qBAAqBA,CAAE,EAC7BD,EAAO,EACPC,EAAK,EACLC,EAAS,EACX,CAEA,SAASO,EAAIC,EAAM,CACjBhB,EAAWgB,CACb,CAEA,SAASC,GAAW,CAClB,OAAOT,CACT,CAEA,MAAO,CACL,MAAO1K,EACP,OAAQgL,EACR,MAAOH,EACP,OAAQE,EACR,IAAKE,EACL,SAAUE,CACd,CACA,CAEA,SAASC,GAAMC,EAAc,CAC3B,IAAIC,EAAQD,EAEZ,SAASJ,EAAIrQ,EAAO,CAClB0Q,EAAQ1Q,CACV,CAEA,SAAS2Q,EAAGC,EAAQ,CAClB,OAAOnK,GAASJ,GAAQuK,CAAM,EAAGF,CAAK,CACxC,CAEA,MAAO,CACL,IAAKL,EACL,GAAIM,CACR,CACA,CAEA,SAASE,GAAStL,EAAMuL,EAAU,CAChC,IAAIxB,EAAWD,GAA4B,EAAG9J,EAAM,KAAM,CAAC,EAC3D,OAAO,UAAY,CACjB+J,EAAS,SAAQ,GAAMA,EAAS,MAAK,CACvC,CACF,CAEA,SAASyB,GAAM/B,EAASgC,EAAapR,EAAS,CAC5C,IAAI8Q,EAAQ1B,EAAQ,MAChBrN,EAAc/B,EAAQ,aAAe,CAAA,EACrCqR,EAAgBrR,EAAQ,eAAiB,CAAA,EACzCsP,EAASvD,GAAW,EACpBuF,EAAU,CAAA,EAEd,SAASC,GAAQ,CACf,IAAIC,EAAQxR,EAAQ,aAAe,MACnC8H,GAAQ/F,CAAW,EAAE,KAAK,SAAU0P,EAAGC,EAAG,CACxC,OAAOF,EAAQ,CAACC,EAAI,CAACC,EAAI,CAACA,EAAI,CAACD,CACjC,CAAC,EAAE,QAAQ,SAAUtR,EAAK,CACxBwR,EAAS5P,EAAY5B,CAAG,EAAG,KAAOqR,EAAQ,MAAQ,OAAS,UAAYrR,EAAM,KAAK,CACpF,CAAC,EACDwR,EAASN,EAAe1M,EAA4B,EACpDyL,EAAM,CACR,CAEA,SAASlD,EAAQ0E,EAAY,CACvBA,GACFtC,EAAO,QAAO,CAElB,CAEA,SAASqC,EAASE,EAAUhI,EAAO,CACjC,IAAIiI,EAAY,WAAWjI,CAAK,EAChCyF,EAAO,KAAKwC,EAAW,SAAU1B,CAAM,EACvCkB,EAAQ,KAAK,CAACO,EAAUC,CAAS,CAAC,CACpC,CAEA,SAAS1B,GAAS,CAChB,IAAI2B,EAAYjB,EAAM,GAAG5L,EAAS,EAC9B8M,EAAYhS,EAAQ,UACpBiS,EAASX,EAAQ,OAAO,SAAUY,EAASC,EAAO,CACpD,OAAO/J,GAAM8J,EAASC,EAAM,CAAC,EAAE,QAAUA,EAAM,CAAC,EAAI,EAAE,CACxD,EAAG,CAAA,CAAE,EACL9J,GAAKrI,CAAO,EACZyQ,EAAIwB,CAAM,EAENjS,EAAQ,QACVoP,EAAQ,QAAQpP,EAAQ,UAAY,YAAY,EACvC+R,GACT7E,EAAQ,EAAI,EACZkC,EAAQ,MAAK,GAEb4C,IAAchS,EAAQ,WAAaoP,EAAQ,QAAO,CAEtD,CAEA,SAASgD,EAAOC,EAAQ,CAClB,WAAW1N,EAA4B,EAAE,UAC3C0N,EAASjK,GAAMpI,EAASqR,CAAa,EAAIhJ,GAAKrI,EAAS8H,GAAQuJ,CAAa,CAAC,EAEjF,CAEA,SAASZ,EAAI6B,EAAMC,EAAMC,EAAQ,CAC/BpK,GAAMpI,EAASsS,CAAI,EACnBC,GAAQnK,GAAM,OAAO,eAAepI,CAAO,EAAGsS,CAAI,GAE9CE,GAAU,CAAC1B,EAAM,GAAGlM,EAAO,IAC7BwK,EAAQ,KAAKvB,EAAe7N,CAAO,CAEvC,CAEA,MAAO,CACL,MAAOuR,EACP,QAASrE,EACT,OAAQkF,EACR,IAAK3B,CACT,CACA,CAEA,IAAIgC,GAAQ,QACRC,GAAaD,GAAQ,OACrBE,GAAcF,GAAQ,QACtBG,GAAWH,GAAQ,KACnBI,GAAaJ,GAAQ,OAErBK,GAAM,MACNC,GAAM,MACNC,GAAkB,CACpB,MAAO,CAAC,QAAQ,EAChB,KAAM,CAAC,MAAO,OAAO,EACrB,MAAO,CAAC,SAAU,MAAM,EACxB,EAAG,CAAC,GAAG,EACP,EAAG,CAAC,GAAG,EACP,EAAG,CAAC,GAAG,EACP,UAAW,CAACJ,GAAUD,EAAW,EACjC,WAAY,CAACE,GAAYH,EAAU,CACrC,EAEA,SAASO,GAAU7D,EAASgC,EAAapR,EAAS,CAChD,SAASkT,EAAQlK,EAAMmK,EAAUnB,EAAW,CAC1CA,EAAYA,GAAahS,EAAQ,UACjC,IAAIoT,EAAQpB,IAAcc,IAAO,CAACK,EAAW,EAAInB,IAAce,GAAM,EAAI,GACzE,OAAOC,GAAgBhK,CAAI,GAAKgK,GAAgBhK,CAAI,EAAEoK,CAAK,GAAKpK,EAAK,QAAQ,oBAAqB,SAAUqK,EAAOC,EAAQ,CACzH,IAAI5H,EAAcsH,GAAgBK,EAAM,YAAW,CAAE,EAAED,CAAK,GAAKC,EACjE,OAAOC,EAAS,EAAI5H,EAAY,OAAO,CAAC,EAAE,YAAW,EAAKA,EAAY,MAAM,CAAC,EAAIA,CACnF,CAAC,CACH,CAEA,SAAS6H,EAAOnT,EAAO,CACrB,OAAOA,GAASJ,EAAQ,YAAc8S,GAAM,EAAI,GAClD,CAEA,MAAO,CACL,QAASI,EACT,OAAQK,CACZ,CACA,CAEA,IAAIC,GAAO,OACPC,GAAY,WACZC,GAAW,WACXC,GAAc,QACdC,GAAgBD,GAAc,WAC9BE,GAAeF,GAAc,UAC7BG,GAAgBH,GAAc,WAC9BI,GAAaJ,GAAc,QAC3BK,GAAkBL,GAAc,aAChCM,GAAcN,GAAc,SAC5BO,GAAmBP,GAAc,cACjCQ,GAAuBR,GAAc,kBACrCS,GAAYT,GAAc,OAC1BU,GAAYV,GAAc,OAC1BW,GAAcX,GAAc,SAC5BY,GAAiB,CAACf,GAAMC,GAAWC,GAAUE,GAAeC,GAAcE,GAAYC,GAAiBC,GAAaC,GAAkBC,EAAoB,EAC1JK,GAAetK,GAAe,KAC9BuK,GAAsB,MACtBC,GAAaxK,GACbyK,GAAcH,GAAe,QAC7BI,GAAaJ,GAAe,OAC5BK,GAAcL,GAAe,QAC7BM,GAAcD,GAAc,UAC5BE,GAAkBF,GAAc,cAChCG,GAAeR,GAAe,SAC9BS,GAAcT,GAAe,QAC7BU,GAAmBD,GAAc,SACjCE,GAAmBF,GAAc,SACjCG,GAAmBZ,GAAe,aAClCa,GAAwBD,GAAmB,SAC3CE,GAAiBd,GAAe,WAChCe,GAAqBD,GAAiB,QACtCE,GAAehB,GAAe,SAG9BiB,GAAgBjB,GAAe,UAC/BkB,GAAWlB,GAAe,KAC1BmB,GAAoBlB,GAAsB,cAC1CmB,GAAenB,GAAsB,SACrCoB,GAAapB,GAAsB,OACnCqB,GAAarB,GAAsB,OACnCsB,GAAgBtB,GAAsB,UACtCuB,GAAgBvB,GAAsB,UACtCwB,GAAiBxB,GAAsB,WACvCyB,GAAiBzB,GAAsB,WACvC0B,GAAiB,CAACP,GAAcG,GAAeF,GAAYC,GAAYE,GAAeC,GAAgBC,EAAc,EACpHE,GAAU,CACZ,MAAOvB,GACP,MAAOC,GACP,OAAQE,GACR,MAAOC,GACP,KAAMC,GACN,KAAMC,GACN,WAAYC,GACZ,KAAMC,GACN,QAASI,EACX,EAEA,SAASY,GAAQC,EAAM1W,EAAU,CAC/B,GAAIyG,GAAWiQ,EAAK,OAAO,EACzB,OAAOA,EAAK,QAAQ1W,CAAQ,EAK9B,QAFIqH,EAAMqP,EAEHrP,GAAOA,EAAI,WAAa,GACzB,CAAAW,GAAQX,EAAKrH,CAAQ,GAIzBqH,EAAMA,EAAI,cAGZ,OAAOA,CACT,CAEA,IAAIsP,GAAW,EACXC,GAAe,IACfC,GAAsB,uBACtBC,GAAsB,sBACtBC,GAAoB,qCAExB,SAASC,GAASxH,EAASgC,EAAapR,EAAS,CAC/C,IAAI6W,EAAkB1H,EAAeC,CAAO,EACxCG,EAAKsH,EAAgB,GACrB5K,EAAO4K,EAAgB,KAEvBC,EAAO1H,EAAQ,KACf2H,EAAO/W,EAAQ,KACfgX,EAAW,CAAA,EACXC,EAAS,CAAA,EACTC,EAAc,CAAA,EACdC,EAAe,CAAA,EACfzU,EACAC,EACAyU,EAEJ,SAAS7F,GAAQ,CACf8F,EAAO,EACPC,EAAI,EACJlH,EAAM,CACR,CAEA,SAASmH,GAAQ,CACfhI,EAAG3B,EAAeV,CAAO,EACzBqC,EAAG3B,EAAe2D,CAAK,EACvBhC,EAAG1B,EAAeuC,CAAM,EACxBnE,EAAK,SAAUwK,GAAsB,WAAY,SAAU9M,EAAG,CAC5DyN,EAAazN,EAAE,OAAS,SAC1B,EAAG,CACD,QAAS,EACf,CAAK,EACDsC,EAAK6K,EAAM,UAAW,UAAY,CAChC9P,GAAY8P,EAAMb,GAAgB,CAAC,CAACmB,CAAU,CAChD,CAAC,CACH,CAEA,SAASlK,EAAQ0E,EAAY,CAC3B,IAAInJ,EAAQ8L,GAAe,OAAO,OAAO,EACzCnP,GAAM6R,CAAM,EACZlN,GAAY+M,EAAMI,CAAW,EAC7BnN,GAAYrH,EAAOyU,CAAY,EAC/B5O,GAAgB,CAAC7F,EAAOC,CAAI,EAAG8F,CAAK,EACpCF,GAAgBuO,EAAMlF,EAAanJ,EAAQ,CAAC,QAAS0L,EAAoB,CAAC,CAC5E,CAEA,SAAS/D,GAAS,CAChBrG,GAAY+M,EAAMI,CAAW,EAC7BnN,GAAYrH,EAAOyU,CAAY,EAC/BD,EAAcM,EAAW9C,EAAU,EACnCyC,EAAeK,EAAW7C,EAAW,EACrCtN,GAASyP,EAAMI,CAAW,EAC1B7P,GAAS3E,EAAOyU,CAAY,EAC5BxO,EAAamO,EAAM/C,GAAY/T,EAAQ,KAAK,EAC5C2I,EAAamO,EAAM9C,GAAiBhU,EAAQ,UAAU,CACxD,CAEA,SAASqX,GAAU,CACjB3U,EAAQ+U,EAAK,IAAM9C,EAAW,EAC9BhS,EAAOpC,GAAMmC,EAAO,IAAMkS,EAAU,EACpCxK,GAAO1H,GAASC,EAAM,kCAAkC,EACxDmE,GAAKmQ,EAAQ1P,GAAS5E,EAAM,IAAMkS,GAAc,SAAWC,GAAc,GAAG,CAAC,EAC7E/M,GAAO,CACL,OAAQiN,GACR,WAAYI,GACZ,KAAMF,GACN,KAAMC,GACN,IAAKI,GACL,OAAQC,EACd,EAAO,SAAUjT,EAAWpC,EAAK,CAC3B6W,EAAS7W,CAAG,EAAIsX,EAAK,IAAMlV,CAAS,CACtC,CAAC,EACD2F,GAAO8O,EAAU,CACf,KAAMF,EACN,MAAOpU,EACP,KAAMC,EACN,OAAQsU,CACd,CAAK,CACH,CAEA,SAASK,GAAO,CACd,IAAIrH,EAAK6G,EAAK,IAAMjL,GAAS3B,EAAY,EACrCwN,EAAO1X,EAAQ,KACnB8W,EAAK,GAAK7G,EACVvN,EAAM,GAAKA,EAAM,IAAMuN,EAAK,SAC5BtN,EAAK,GAAKA,EAAK,IAAMsN,EAAK,QAEtB,CAAC7G,GAAa0N,EAAMtD,EAAI,GAAKsD,EAAK,UAAY,WAAaY,GAC7D/O,EAAamO,EAAMtD,GAAMkE,CAAI,EAG/B/O,EAAamO,EAAM3C,GAAsB4C,EAAK,QAAQ,EACtDpO,EAAahG,EAAM6Q,GAAM,cAAc,CACzC,CAEA,SAASiE,EAAK7X,EAAU,CACtB,IAAIqH,EAAM4C,GAAMiN,EAAMlX,CAAQ,EAC9B,OAAOqH,GAAOoP,GAAQpP,EAAK,IAAMyN,EAAU,IAAMoC,EAAO7P,EAAM,MAChE,CAEA,SAASuQ,EAAWjF,EAAM,CACxB,MAAO,CAACA,EAAO,KAAOvS,EAAQ,KAAMuS,EAAO,KAAOvS,EAAQ,UAAWA,EAAQ,MAAQuS,EAAO,cAAevS,EAAQ,cAAgBuS,EAAO,QAASA,IAASmC,IAAckB,EAAY,CACxL,CAEA,OAAO1N,GAAO8O,EAAU,CACtB,MAAOzF,EACP,MAAOgG,EACP,QAASrK,CACb,CAAG,CACH,CAEA,IAAIyK,GAAQ,QACRC,GAAO,OACPC,GAAO,OAEX,SAASC,GAAQ1I,EAASgE,EAAOzP,EAAYb,EAAO,CAClD,IAAIwJ,EAAQ6C,EAAeC,CAAO,EAC9BG,EAAKjD,EAAM,GACXkD,EAAOlD,EAAM,KACbL,EAAOK,EAAM,KACbyL,EAAa3I,EAAQ,WACrB0H,EAAO1H,EAAQ,KACfpP,EAAUoP,EAAQ,QAClB4I,EAAehY,EAAQ,aACvBiY,EAAejY,EAAQ,aACvB+W,EAAO/W,EAAQ,KACfgD,EAAahD,EAAQ,WACrBkY,EAAalY,EAAQ,WACrBkT,EAAU6E,EAAW,UAAU,QAC/BI,EAAS/O,GAAatG,EAAO,OAAO,EACpCsV,EAAQhP,GAAatG,EAAOiR,EAAU,EACtCsE,EAAU1U,EAAa,GACvBtB,EAAY9B,GAAMuC,EAAO,IAAMiS,EAAe,EAC9ChD,EAEJ,SAASwF,GAAQ,CACVc,IACHvV,EAAM,GAAKgU,EAAK,GAAK,SAAWnL,GAAIyH,EAAQ,CAAC,EAC7CzK,EAAa7F,EAAO0Q,GAAMxQ,EAAa,WAAa,OAAO,EAC3D2F,EAAa7F,EAAOqR,GAAsB4C,EAAK,KAAK,EACpDpO,EAAa7F,EAAOiR,GAAYqE,GAAS7M,GAAOwL,EAAK,WAAY,CAAC3D,EAAQ,EAAGhE,EAAQ,MAAM,CAAC,CAAC,GAG/FkJ,EAAM,CACR,CAEA,SAASA,GAAS,CAChBrM,EAAKnJ,EAAO,QAAS4C,EAAM8J,EAAMjC,GAAagL,CAAI,CAAC,EACnDtM,EAAKnJ,EAAO,UAAW4C,EAAM8J,EAAMR,GAAqBuJ,CAAI,CAAC,EAC7DhJ,EAAG,CAACjC,GAAa2B,GAAeb,EAAc,EAAGgC,CAAM,EACvDb,EAAGZ,GAA0B6J,CAAc,EAEvCP,GACF1I,EAAGlC,GAAYoL,CAAM,CAEzB,CAEA,SAASvL,GAAU,CACjB6E,EAAY,GACZzF,EAAM,QAAO,EACbvC,GAAYjH,EAAOqT,EAAc,EACjC5N,GAAgBzF,EAAOyR,EAAc,EACrC5L,EAAa7F,EAAO,QAASqV,CAAM,EACnCxP,EAAa7F,EAAOiR,GAAYqE,GAAS,EAAE,CAC7C,CAEA,SAASI,GAAiB,CACxB,IAAIE,EAAWtJ,EAAQ,QAAQ,IAAI,SAAUjL,EAAQ,CACnD,IAAIwU,EAASxU,EAAO,OAAO,WAAW,OAAO,MAAMiP,CAAK,EACxD,OAAOuF,EAASA,EAAO,MAAM,GAAK,EACpC,CAAC,EAAE,KAAK,GAAG,EACXhQ,EAAa7F,EAAOiR,GAAYxI,GAAOwL,EAAK,QAASsB,EAAU1U,EAAayP,GAAS,CAAC,CAAC,EACvFzK,EAAa7F,EAAO8Q,GAAe8E,CAAQ,EAC3C/P,EAAa7F,EAAO0Q,GAAM0E,EAAa,SAAW,EAAE,EACpDA,GAAc3P,GAAgBzF,EAAOqR,EAAoB,CAC3D,CAEA,SAASsE,GAAS,CACX1G,GACH3B,EAAM,CAEV,CAEA,SAASA,GAAS,CAChB,GAAI,CAAC2B,EAAW,CACd,IAAI6G,EAAOxJ,EAAQ,MACnByJ,EAAc,EACdC,EAAgB,EAChB9R,GAAYlE,EAAO+S,GAAYzC,IAAUwF,EAAO,CAAC,EACjD5R,GAAYlE,EAAOgT,GAAY1C,IAAUwF,EAAO,CAAC,CACnD,CACF,CAEA,SAASC,GAAiB,CACxB,IAAIE,EAASC,EAAQ,EAEjBD,IAAW1P,GAASvG,EAAO8S,EAAY,IACzC5O,GAAYlE,EAAO8S,GAAcmD,CAAM,EACvCpQ,EAAa7F,EAAO+Q,GAAcmE,GAAgBe,GAAU,EAAE,EAC9DvJ,EAAKuJ,EAASvL,GAAeC,GAAgB8K,CAAI,EAErD,CAEA,SAASO,GAAmB,CAC1B,IAAIG,EAAUC,EAAS,EACnBC,EAAS,CAACF,IAAY,CAACD,EAAQ,GAAMX,GAiBzC,GAfKjJ,EAAQ,MAAM,GAAG,CAACrK,GAAQC,EAAS,CAAC,GACvC2D,EAAa7F,EAAOmR,GAAakF,GAAU,EAAE,EAG/CxQ,EAAamB,GAAShH,EAAO9C,EAAQ,gBAAkB,EAAE,EAAGyT,GAAW0F,EAAS,GAAK,EAAE,EAEnFjB,GACFvP,EAAa7F,EAAO2Q,GAAW0F,EAAS,GAAK,CAAC,EAG5CF,IAAY5P,GAASvG,EAAOiT,EAAa,IAC3C/O,GAAYlE,EAAOiT,GAAekD,CAAO,EACzCzJ,EAAKyJ,EAAUvL,GAAgBC,GAAc4K,CAAI,GAG/C,CAACU,GAAW,SAAS,gBAAkBnW,EAAO,CAChD,IAAI6V,EAASZ,EAAW,OAAO,MAAM3I,EAAQ,KAAK,EAClDuJ,GAAUxP,GAAMwP,EAAO,KAAK,CAC9B,CACF,CAEA,SAASS,EAAQpQ,EAAM5I,EAAOiZ,EAAc,CAC1CtQ,GAAMsQ,GAAgBhX,GAAaS,EAAOkG,EAAM5I,CAAK,CACvD,CAEA,SAAS4Y,GAAW,CAClB,IAAIJ,EAAOxJ,EAAQ,MACnB,OAAOwJ,IAASxF,GAASpT,EAAQ,aAAe4Y,IAASjV,CAC3D,CAEA,SAASuV,GAAY,CACnB,GAAI9J,EAAQ,GAAGyI,EAAI,EACjB,OAAOmB,EAAQ,EAGjB,IAAIM,EAAYhQ,GAAKyO,EAAW,SAAS,KAAK,EAC1CwB,EAAYjQ,GAAKxG,CAAK,EACtB0W,EAAOtG,EAAQ,OAAQ,EAAI,EAC3BjL,EAAQiL,EAAQ,QAAS,EAAI,EACjC,OAAOzI,GAAM6O,EAAUE,CAAI,CAAC,GAAK9O,GAAK6O,EAAUC,CAAI,CAAC,GAAK/O,GAAM8O,EAAUtR,CAAK,CAAC,GAAKyC,GAAK4O,EAAUrR,CAAK,CAAC,CAC5G,CAEA,SAASwR,EAASnD,EAAMoD,EAAU,CAChC,IAAIC,EAAOhP,EAAI2L,EAAOlD,CAAK,EAE3B,MAAI,CAACiF,IAAYrY,EAAQ,QAAUoP,EAAQ,GAAGwI,EAAI,KAChD+B,EAAOpP,GAAIoP,EAAMvK,EAAQ,OAASuK,CAAI,GAGjCA,GAAQD,CACjB,CAEA,IAAInB,EAAO,CACT,MAAOnF,EACP,WAAYzP,EACZ,MAAOb,EACP,UAAWT,EACX,QAASgW,EACT,MAAOd,EACP,QAASrK,EACT,OAAQkD,EACR,MAAOgJ,EACP,SAAUK,CACd,EACE,OAAOlB,CACT,CAEA,SAASqB,GAAOxK,EAASgC,EAAapR,EAAS,CAC7C,IAAI6Z,EAAmB1K,EAAeC,CAAO,EACzCG,EAAKsK,EAAiB,GACtBrK,EAAOqK,EAAiB,KACxB5N,EAAO4N,EAAiB,KAExBC,EAAwB1I,EAAY,SACpC6F,EAAS6C,EAAsB,OAC/BnX,EAAOmX,EAAsB,KAC7BC,EAAU,CAAA,EAEd,SAASxC,GAAQ,CACfD,EAAI,EACJ/H,EAAG3B,EAAeV,CAAO,EACzBqC,EAAG3B,EAAe0J,CAAI,CACxB,CAEA,SAASA,GAAO,CACdL,EAAO,QAAQ,SAAUnU,EAAOsQ,EAAO,CACrCzB,EAAS7O,EAAOsQ,EAAO,EAAE,CAC3B,CAAC,CACH,CAEA,SAASlG,GAAU,CACjB8M,EAAU,SAAUrB,EAAQ,CAC1BA,EAAO,QAAO,CAChB,CAAC,EACDvT,GAAM2U,CAAO,CACf,CAEA,SAAS3J,GAAS,CAChB4J,EAAU,SAAUrB,EAAQ,CAC1BA,EAAO,OAAM,CACf,CAAC,CACH,CAEA,SAAShH,EAAS7O,EAAOsQ,EAAOzP,EAAY,CAC1C,IAAIqE,EAAS8P,GAAQ1I,EAASgE,EAAOzP,EAAYb,CAAK,EACtDkF,EAAO,MAAK,EACZ+R,EAAQ,KAAK/R,CAAM,EACnB+R,EAAQ,KAAK,SAAUE,EAAQtB,EAAQ,CACrC,OAAOsB,EAAO,MAAQtB,EAAO,KAC/B,CAAC,CACH,CAEA,SAASuB,EAAIC,EAAe,CAC1B,OAAOA,EAAgBC,EAAO,SAAUzB,EAAQ,CAC9C,MAAO,CAACA,EAAO,OACjB,CAAC,EAAIoB,CACP,CAEA,SAASM,EAAMC,EAAM,CACnB,IAAIC,EAAanJ,EAAY,WACzBgC,EAAQmH,EAAW,QAAQD,CAAI,EAC/B9P,EAAM+P,EAAW,SAAQ,EAAK,EAAIva,EAAQ,QAC9C,OAAOoa,EAAO,SAAUzB,EAAQ,CAC9B,OAAO3N,GAAQ2N,EAAO,MAAOvF,EAAOA,EAAQ5I,EAAM,CAAC,CACrD,CAAC,CACH,CAEA,SAASgQ,EAAMpH,EAAO,CACpB,OAAOgH,EAAOhH,CAAK,EAAE,CAAC,CACxB,CAEA,SAASjM,EAAIJ,EAAOqM,EAAO,CACzB1M,GAAQK,EAAO,SAAUjE,EAAO,CAK9B,GAJIwD,GAASxD,CAAK,IAChBA,EAAQ0G,GAAU1G,CAAK,GAGrB0D,GAAc1D,CAAK,EAAG,CACxB,IAAI4E,EAAMuP,EAAO7D,CAAK,EACtB1L,EAAMF,GAAO1E,EAAO4E,CAAG,EAAIJ,GAAO3E,EAAMG,CAAK,EAC7CuE,GAASvE,EAAO9C,EAAQ,QAAQ,KAAK,EACrCya,EAAc3X,EAAO4C,EAAM8J,EAAM1B,EAAY,CAAC,CAChD,CACF,CAAC,EACD0B,EAAK5B,CAAa,CACpB,CAEA,SAAS8M,EAASC,EAAS,CACzBpR,GAAO6Q,EAAOO,CAAO,EAAE,IAAI,SAAUhC,EAAQ,CAC3C,OAAOA,EAAO,KAChB,CAAC,CAAC,EACFnJ,EAAK5B,CAAa,CACpB,CAEA,SAASoM,EAAUpT,EAAUuT,EAAe,CAC1CD,EAAIC,CAAa,EAAE,QAAQvT,CAAQ,CACrC,CAEA,SAASwT,EAAOO,EAAS,CACvB,OAAOZ,EAAQ,OAAO1T,GAAWsU,CAAO,EAAIA,EAAU,SAAUhC,EAAQ,CACtE,OAAOrS,GAASqU,CAAO,EAAI/S,GAAQ+Q,EAAO,MAAOgC,CAAO,EAAI9T,GAASJ,GAAQkU,CAAO,EAAGhC,EAAO,KAAK,CACrG,CAAC,CACH,CAEA,SAAS5P,EAAMC,EAAM5I,EAAOiZ,EAAc,CACxCW,EAAU,SAAUrB,EAAQ,CAC1BA,EAAO,MAAM3P,EAAM5I,EAAOiZ,CAAY,CACxC,CAAC,CACH,CAEA,SAASoB,EAAcxT,EAAKmF,EAAU,CACpC,IAAIwO,EAAS9Q,GAAS7C,EAAK,KAAK,EAC5B4T,EAASD,EAAO,OAEhBC,EACFD,EAAO,QAAQ,SAAUE,EAAK,CAC5B7O,EAAK6O,EAAK,aAAc,UAAY,CAC5B,EAAED,GACNzO,EAAQ,CAEZ,CAAC,CACH,CAAC,EAEDA,EAAQ,CAEZ,CAEA,SAAS2O,EAAUZ,EAAe,CAChC,OAAOA,EAAgBlD,EAAO,OAAS8C,EAAQ,MACjD,CAEA,SAASiB,GAAW,CAClB,OAAOjB,EAAQ,OAAS/Z,EAAQ,OAClC,CAEA,MAAO,CACL,MAAOuX,EACP,QAASrK,EACT,OAAQkD,EACR,SAAUuB,EACV,IAAKuI,EACL,MAAOG,EACP,MAAOG,EACP,IAAKrT,EACL,OAAQuT,EACR,QAASV,EACT,OAAQI,EACR,MAAOrR,EACP,UAAWgS,EACX,SAAUC,CACd,CACA,CAEA,SAASC,GAAO7L,EAASgC,EAAapR,EAAS,CAC7C,IAAIkb,EAAmB/L,EAAeC,CAAO,EACzCG,EAAK2L,EAAiB,GACtBjP,EAAOiP,EAAiB,KACxB1L,EAAO0L,EAAiB,KAExBtB,EAASxI,EAAY,OACrB8B,EAAU9B,EAAY,UAAU,QAChC+J,EAAyB/J,EAAY,SACrC0F,EAAOqE,EAAuB,KAC9BzY,EAAQyY,EAAuB,MAC/BxY,EAAOwY,EAAuB,KAC9BX,EAAQZ,EAAO,MACfwB,EAAcxB,EAAO,MACrByB,EACAC,EACAC,EAEJ,SAAShE,GAAQ,CACfD,EAAI,EACJrL,EAAK,OAAQ,cAAegF,GAASvL,EAAM8J,EAAM1B,EAAY,CAAC,CAAC,EAC/DyB,EAAG,CAAC1B,EAAeD,CAAa,EAAG0J,CAAI,EACvC/H,EAAGzB,GAAc0N,CAAM,CACzB,CAEA,SAASlE,GAAO,CACd+D,EAAWrb,EAAQ,YAAc+S,GACjChK,GAAM+N,EAAM,WAAY7M,GAAKjK,EAAQ,KAAK,CAAC,EAC3C+I,GAAMrG,EAAOwQ,EAAQ,aAAa,EAAGuI,EAAW,EAAK,CAAC,EACtD1S,GAAMrG,EAAOwQ,EAAQ,cAAc,EAAGuI,EAAW,EAAI,CAAC,EACtDD,EAAO,EAAI,CACb,CAEA,SAASA,EAAOE,EAAO,CACrB,IAAIC,EAAUrS,GAAKwN,CAAI,GAEnB4E,GAASJ,EAAS,QAAUK,EAAQ,OAASL,EAAS,SAAWK,EAAQ,UAC3E5S,GAAMrG,EAAO,SAAUkZ,GAAgB,EACvCR,EAAYlI,EAAQ,aAAa,EAAGjJ,GAAKjK,EAAQ,GAAG,CAAC,EACrDob,EAAY,QAASS,GAAe,EACpCT,EAAY,SAAUU,EAAc,EAAI,EAAI,EAC5CR,EAAWK,EACXnM,EAAKzB,EAAa,EAEdwN,KAAcA,EAAWQ,EAAU,KACrC/U,GAAY8P,EAAMZ,GAAgBqF,CAAQ,EAC1C/L,EAAKnB,GAAgBkN,CAAQ,GAGnC,CAEA,SAASE,EAAWxT,EAAO,CACzB,IAAI+T,EAAUhc,EAAQ,QAClBgJ,EAAOkK,EAAQjL,EAAQ,QAAU,MAAM,EAC3C,OAAO+T,GAAW/R,GAAK+R,EAAQhT,CAAI,IAAM9C,GAAS8V,CAAO,EAAI,EAAIA,EAAQ,GAAK,KAChF,CAEA,SAASJ,GAAiB,CACxB,IAAIK,EAAS,GAEb,OAAIZ,IACFY,EAASC,EAAS,EAClB9R,GAAO6R,EAAQ,mCAAmC,EAClDA,EAAS,QAAUA,EAAS,MAAQR,EAAW,EAAK,EAAI,MAAQA,EAAW,EAAI,EAAI,KAG9EQ,CACT,CAEA,SAASC,GAAY,CACnB,OAAOjS,GAAKjK,EAAQ,QAAUsJ,GAAK3G,CAAI,EAAE,MAAQ3C,EAAQ,WAAW,CACtE,CAEA,SAAS6b,GAAgB,CACvB,OAAO7b,EAAQ,UAAY,KAAOiK,GAAKjK,EAAQ,UAAU,IAAMqb,EAAW,GAAKc,IACjF,CAEA,SAASL,GAAiB,CACxB,OAAO7R,GAAKjK,EAAQ,WAAW,IAAMqb,EAAWrb,EAAQ,WAAa,KAAOmc,EAAY,EAAKD,EAAS,EACxG,CAEA,SAASC,GAAe,CACtB,IAAIC,EAAMnS,GAAKjK,EAAQ,GAAG,EAC1B,MAAO,cAAgBoc,GAAO,MAAQA,GAAO,MAAQpc,EAAQ,SAAW,IAAMoc,GAAO,MAAQA,GAAO,GACtG,CAEA,SAASC,GAAW,CAClB,OAAO/S,GAAK3G,CAAI,EAAEuQ,EAAQ,OAAO,CAAC,CACpC,CAEA,SAASoJ,EAAUlJ,EAAOmJ,EAAY,CACpC,IAAIC,EAAQhC,EAAMpH,GAAS,CAAC,EAC5B,OAAOoJ,EAAQlT,GAAKkT,EAAM,KAAK,EAAEtJ,EAAQ,OAAO,CAAC,GAAKqJ,EAAa,EAAIE,EAAM,GAAM,CACrF,CAEA,SAASC,EAAUtJ,EAAOmJ,EAAY,CACpC,IAAIC,EAAQhC,EAAMpH,CAAK,EAEvB,GAAIoJ,EAAO,CACT,IAAIvU,EAAQqB,GAAKkT,EAAM,KAAK,EAAEtJ,EAAQ,OAAO,CAAC,EAC1CsG,EAAOlQ,GAAK3G,CAAI,EAAEuQ,EAAQ,MAAM,CAAC,EACrC,OAAOvI,EAAI1C,EAAQuR,CAAI,GAAK+C,EAAa,EAAIE,IAC/C,CAEA,MAAO,EACT,CAEA,SAASE,EAAWJ,EAAY,CAC9B,OAAOG,EAAUtN,EAAQ,OAAS,CAAC,EAAIsN,EAAU,CAAC,EAAIJ,EAAU,EAAGC,CAAU,CAC/E,CAEA,SAASE,GAAS,CAChB,IAAID,EAAQhC,EAAM,CAAC,EACnB,OAAOgC,GAAS,WAAWzT,GAAMyT,EAAM,MAAOtJ,EAAQ,aAAa,CAAC,CAAC,GAAK,CAC5E,CAEA,SAAS0J,EAAW3U,EAAO,CACzB,OAAO,WAAWc,GAAMrG,EAAOwQ,EAAQ,WAAajL,EAAQ,QAAU,OAAO,CAAC,CAAC,GAAK,CACtF,CAEA,SAAS8T,GAAa,CACpB,OAAO3M,EAAQ,GAAGyI,EAAI,GAAK8E,EAAW,EAAI,EAAIN,EAAQ,CACxD,CAEA,MAAO,CACL,MAAO9E,EACP,OAAQiE,EACR,SAAUa,EACV,UAAWC,EACX,WAAYK,EACZ,UAAWD,EACX,WAAYE,EACZ,WAAYb,CAChB,CACA,CAEA,IAAIc,GAAa,EAEjB,SAASC,GAAO1N,EAASgC,EAAapR,EAAS,CAC7C,IAAIsM,EAAQ6C,EAAeC,CAAO,EAC9BG,EAAKjD,EAAM,GACXsK,EAAWxF,EAAY,SACvBwI,EAASxI,EAAY,OACrB8B,EAAU9B,EAAY,UAAU,QAChC2L,EAAS,CAAA,EACTC,EAEJ,SAASzF,GAAQ,CACfhI,EAAG3B,EAAeqP,CAAO,EACzB1N,EAAG,CAAC1B,EAAeC,EAAY,EAAGoP,CAAO,GAErCF,EAAaG,OACfC,EAASJ,CAAU,EACnB5L,EAAY,OAAO,OAAO,EAAI,EAElC,CAEA,SAAS6L,GAAU,CACjB/P,EAAO,EACPqK,EAAK,CACP,CAEA,SAASrK,GAAU,CACjB3D,GAAOwT,CAAM,EACb3X,GAAM2X,CAAM,EACZzQ,EAAM,QAAO,CACf,CAEA,SAAS4Q,GAAU,CACjB,IAAI/M,EAAQgN,EAAiB,EAEzBH,IAAe7M,IACb6M,EAAa7M,GAAS,CAACA,IACzB7D,EAAM,KAAKsB,CAAa,CAG9B,CAEA,SAASwP,EAASjN,EAAO,CACvB,IAAI8G,EAAS2C,EAAO,IAAG,EAAG,MAAK,EAC3BiB,EAAS5D,EAAO,OAEpB,GAAI4D,EAAQ,CACV,KAAO5D,EAAO,OAAS9G,GACrBrJ,GAAKmQ,EAAQA,CAAM,EAGrBnQ,GAAKmQ,EAAO,MAAM,CAAC9G,CAAK,EAAG8G,EAAO,MAAM,EAAG9G,CAAK,CAAC,EAAE,QAAQ,SAAUqM,EAAOpJ,EAAO,CACjF,IAAIiK,EAASjK,EAAQjD,EACjBmN,EAAQC,EAAUf,EAAM,MAAOpJ,CAAK,EACxCiK,EAAS7V,GAAO8V,EAAOrG,EAAO,CAAC,EAAE,KAAK,EAAI3P,GAAOsP,EAAS,KAAM0G,CAAK,EACrExW,GAAKiW,EAAQO,CAAK,EAClB1D,EAAO,SAAS0D,EAAOlK,EAAQjD,GAASkN,EAAS,EAAIxC,GAAS2B,EAAM,KAAK,CAC3E,CAAC,CACH,CACF,CAEA,SAASe,EAAUtW,EAAKmM,EAAO,CAC7B,IAAIkK,EAAQrW,EAAI,UAAU,EAAI,EAC9B,OAAAI,GAASiW,EAAOtd,EAAQ,QAAQ,KAAK,EACrCsd,EAAM,GAAKlO,EAAQ,KAAK,GAAK,SAAWzD,GAAIyH,EAAQ,CAAC,EAC9CkK,CACT,CAEA,SAASH,GAAoB,CAC3B,IAAIK,EAAUxd,EAAQ,OAEtB,GAAI,CAACoP,EAAQ,GAAGwI,EAAI,EAClB4F,EAAU,UACDjX,GAAYiX,CAAO,EAAG,CAC/B,IAAIC,EAAYzd,EAAQkT,EAAQ,YAAY,CAAC,GAAK9B,EAAY,OAAO,UAAU,CAAC,EAC5EsM,EAAaD,GAAa/S,GAAKpB,GAAKsN,EAAS,KAAK,EAAE1D,EAAQ,OAAO,CAAC,EAAIuK,CAAS,EACrFD,EAAUE,GAAc1d,EAAQkT,EAAQ,WAAW,CAAC,GAAK9D,EAAQ,QAAUpP,EAAQ,QAAU6c,EAC/F,CAEA,OAAOW,CACT,CAEA,MAAO,CACL,MAAOjG,EACP,QAASrK,CACb,CACA,CAEA,SAASyQ,GAAKvO,EAASgC,EAAapR,EAAS,CAC3C,IAAI4d,EAAmBzO,EAAeC,CAAO,EACzCG,EAAKqO,EAAiB,GACtBpO,EAAOoO,EAAiB,KAExBnN,EAAMrB,EAAQ,MAAM,IACpByO,EAAsBzM,EAAY,OAClCkL,EAAYuB,EAAoB,UAChCjB,EAAaiB,EAAoB,WACjCnB,EAAYmB,EAAoB,UAChCxB,EAAWwB,EAAoB,SAC/BlB,EAAakB,EAAoB,WACjCC,EAAwB1M,EAAY,UACpC8B,EAAU4K,EAAsB,QAChCvK,EAASuK,EAAsB,OAC/BC,EAAyB3M,EAAY,SACrCzO,EAAOob,EAAuB,KAC9Brb,EAAQqb,EAAuB,MAC/BC,EAEJ,SAASzG,GAAQ,CACfyG,EAAa5M,EAAY,WACzB7B,EAAG,CAACpC,GAAeY,GAAeF,EAAeD,CAAa,EAAGqQ,CAAU,CAC7E,CAEA,SAASA,GAAa,CACf7M,EAAY,WAAW,WAC1BA,EAAY,OAAO,OAAM,EACzB8M,EAAK9O,EAAQ,KAAK,EAClBgC,EAAY,OAAO,OAAM,EAE7B,CAEA,SAAS+M,EAAKC,EAAMhL,EAAOiL,EAAMjS,EAAU,CACrCgS,IAAShL,GAASkL,EAASF,EAAOC,CAAI,IACxC9N,EAAM,EACNgO,EAAUC,EAAMC,EAAW,EAAIL,EAAOC,CAAI,EAAG,EAAI,GAGnD5N,EAAI1L,EAAM,EACVyK,EAAKnC,GAAY+F,EAAOiL,EAAMD,CAAI,EAClCJ,EAAW,MAAM5K,EAAO,UAAY,CAClC3C,EAAI3L,EAAI,EACR0K,EAAKlC,GAAa8F,EAAOiL,EAAMD,CAAI,EACnChS,GAAYA,EAAQ,CACtB,CAAC,CACH,CAEA,SAAS8R,EAAK9K,EAAO,CACnBmL,EAAUG,EAAWtL,EAAO,EAAI,CAAC,CACnC,CAEA,SAASmL,EAAUI,EAAUC,EAAa,CACxC,GAAI,CAACxP,EAAQ,GAAGyI,EAAI,EAAG,CACrB,IAAIgH,EAAcD,EAAcD,EAAWG,EAAKH,CAAQ,EACxD5V,GAAMpG,EAAM,YAAa,YAAcuQ,EAAQ,GAAG,EAAI,IAAM2L,EAAc,KAAK,EAC/EF,IAAaE,GAAerP,EAAKP,EAAa,CAChD,CACF,CAEA,SAAS6P,EAAKH,EAAU,CACtB,GAAIvP,EAAQ,GAAGwI,EAAI,EAAG,CACpB,IAAIxE,EAAQ2L,EAAQJ,CAAQ,EACxBK,EAAc5L,EAAQhC,EAAY,WAAW,OAAM,EACnD6N,EAAc7L,EAAQ,GAEtB6L,GAAeD,KACjBL,EAAWH,EAAMG,EAAUK,CAAW,EAE1C,CAEA,OAAOL,CACT,CAEA,SAASH,EAAMG,EAAUO,EAAW,CAClC,IAAIC,EAASR,EAAWS,EAASF,CAAS,EACtCG,EAAO1C,EAAU,EACrB,OAAAgC,GAAYpL,EAAO8L,GAAQ3U,GAAKC,EAAIwU,CAAM,EAAIE,CAAI,GAAK,EAAE,GAAKH,EAAY,EAAI,IACvEP,CACT,CAEA,SAASpO,GAAS,CAChBgO,EAAUE,EAAW,EAAI,EAAI,EAC7BT,EAAW,OAAM,CACnB,CAEA,SAASe,EAAQJ,EAAU,CAKzB,QAJI/E,EAASxI,EAAY,OAAO,IAAG,EAC/BgC,EAAQ,EACRkM,EAAc,IAETjb,EAAI,EAAGA,EAAIuV,EAAO,OAAQvV,IAAK,CACtC,IAAIV,GAAaiW,EAAOvV,CAAC,EAAE,MACvBqV,EAAW/O,EAAI+T,EAAW/a,GAAY,EAAI,EAAIgb,CAAQ,EAE1D,GAAIjF,GAAY4F,EACdA,EAAc5F,EACdtG,EAAQzP,OAER,MAEJ,CAEA,OAAOyP,CACT,CAEA,SAASsL,EAAWtL,EAAOmM,EAAU,CACnC,IAAIZ,EAAWpL,EAAOmJ,EAAUtJ,EAAQ,CAAC,EAAIE,EAAOF,CAAK,CAAC,EAC1D,OAAOmM,EAAWC,EAAKb,CAAQ,EAAIA,CACrC,CAEA,SAASF,GAAc,CACrB,IAAIjF,EAAOtG,EAAQ,MAAM,EACzB,OAAO5J,GAAK3G,CAAI,EAAE6W,CAAI,EAAIlQ,GAAK5G,CAAK,EAAE8W,CAAI,EAAIjG,EAAOqJ,EAAW,EAAK,CAAC,CACxE,CAEA,SAAS4C,EAAKb,EAAU,CACtB,OAAI3e,EAAQ,WAAaoP,EAAQ,GAAGuI,EAAK,IACvCgH,EAAWtT,GAAMsT,EAAU,EAAGpL,EAAOoJ,EAAW,EAAI,EAAIN,EAAQ,CAAE,CAAC,GAG9DsC,CACT,CAEA,SAASrL,EAAOF,EAAO,CACrB,IAAIjK,EAAQnJ,EAAQ,MACpB,OAAOmJ,IAAU,UAAYkT,EAAQ,EAAKC,EAAUlJ,EAAO,EAAI,GAAK,EAAI,CAACjK,EAAQmT,EAAUlJ,CAAK,GAAK,CACvG,CAEA,SAASgM,EAAS5U,EAAK,CACrB,OAAOkU,EAAWlU,EAAM4G,EAAY,WAAW,OAAM,EAAK,EAAG,CAAC,CAACpR,EAAQ,SAAS,CAClF,CAEA,SAASse,EAASY,EAAW,CAC3B,IAAIO,EAAUlM,EAAOiL,EAAMC,EAAW,EAAIS,CAAS,CAAC,EACpD,OAAOA,EAAYO,GAAW,EAAIA,GAAW9c,EAAKuQ,EAAQ,aAAa,CAAC,EAAI5J,GAAK5G,CAAK,EAAEwQ,EAAQ,OAAO,CAAC,CAC1G,CAEA,SAASwM,EAAclV,EAAKmU,EAAU,CACpCA,EAAWpY,GAAYoY,CAAQ,EAAIF,EAAW,EAAKE,EACnD,IAAIM,EAAczU,IAAQ,IAAQ+I,EAAOoL,CAAQ,EAAIpL,EAAO6L,EAAS,EAAK,CAAC,EACvEJ,EAAcxU,IAAQ,IAAS+I,EAAOoL,CAAQ,EAAIpL,EAAO6L,EAAS,EAAI,CAAC,EAC3E,OAAOH,GAAeD,CACxB,CAEA,MAAO,CACL,MAAOzH,EACP,KAAM4G,EACN,KAAMD,EACN,UAAWK,EACX,MAAOC,EACP,OAAQjO,EACR,QAASwO,EACT,WAAYL,EACZ,YAAaD,EACb,SAAUW,EACV,cAAeM,EACf,WAAYzB,CAChB,CACA,CAEA,SAAS1D,GAAWnL,EAASgC,EAAapR,EAAS,CACjD,IAAI2f,EAAmBxQ,EAAeC,CAAO,EACzCG,EAAKoQ,EAAiB,GACtBnQ,EAAOmQ,EAAiB,KAExBhC,EAAOvM,EAAY,KACnBqN,EAAcd,EAAK,YACnByB,EAAWzB,EAAK,SAChBe,EAAaf,EAAK,WAClBiC,EAAsBxO,EAAY,OAClC4J,EAAW4E,EAAoB,SAC/B7E,EAAY6E,EAAoB,UAChCC,EAAU7f,EAAQ,QAClB8f,EAAS1Q,EAAQ,GAAGwI,EAAI,EACxBmI,EAAU3Q,EAAQ,GAAGuI,EAAK,EAC1BqI,EAAUta,EAAMua,EAAa,EAAK,EAClCC,EAAUxa,EAAMua,EAAa,EAAI,EACjCE,EAAYngB,EAAQ,OAAS,EAC7BogB,EACAC,EAAYF,EACZ9e,EACAif,EACAC,EAEJ,SAAShJ,GAAQ,CACfD,EAAI,EACJ/H,EAAG,CAAC1B,EAAeD,EAAesB,EAAuB,EAAGoI,CAAI,EAChE/H,EAAGxB,GAAeyS,CAAS,CAC7B,CAEA,SAASlJ,GAAO,CACdjW,EAAa0Z,EAAU,EAAI,EAC3BuF,EAAUtgB,EAAQ,QAClBugB,EAAUvgB,EAAQ,QAClBogB,EAAWK,EAAM,EACjB,IAAIrN,EAAQ/H,GAAM8U,EAAW,EAAGN,EAAUO,EAAW/e,EAAa,CAAC,EAE/D+R,IAAU+M,IACZA,EAAY/M,EACZuK,EAAK,WAAU,EAEnB,CAEA,SAAS6C,GAAY,CACfJ,IAAaK,KACfjR,EAAKN,EAAuB,CAEhC,CAEA,SAASwR,EAAGC,EAASC,EAAgBxU,GAAU,CAC7C,GAAI,CAACyU,GAAM,EAAI,CACb,IAAIzC,EAAO0C,EAAMH,CAAO,EACpBvN,EAAQ0L,EAAKV,CAAI,EAEjBhL,EAAQ,KAAOwN,GAAkBxN,IAAU+M,KAC7CY,EAAS3N,CAAK,EACduK,EAAK,KAAKS,EAAMhL,EAAOiN,EAAWjU,EAAQ,EAE9C,CACF,CAEA,SAAS4U,EAAOnC,EAAa3N,EAAU+P,GAAM7U,EAAU,CACrDgF,EAAY,OAAO,OAAOyN,EAAa3N,EAAU+P,GAAM,UAAY,CACjE,IAAI7N,EAAQ0L,EAAKnB,EAAK,QAAQc,EAAW,CAAE,CAAC,EAC5CsC,EAASlB,EAAUtV,GAAI6I,EAAOgN,CAAQ,EAAIhN,CAAK,EAC/ChH,GAAYA,EAAQ,CACtB,CAAC,CACH,CAEA,SAAS0U,EAAMH,EAAS,CACtB,IAAIvN,EAAQ+M,EAEZ,GAAI7Z,GAASqa,CAAO,EAAG,CACrB,IAAIO,GAAOP,EAAQ,MAAM,iBAAiB,GAAK,CAAA,EAC3CQ,EAAYD,GAAK,CAAC,EAClBjW,EAASiW,GAAK,CAAC,EAEfC,IAAc,KAAOA,IAAc,IACrC/N,EAAQgO,EAAiBjB,GAAY,EAAE,GAAKgB,GAAa,CAAClW,GAAU,IAAKkV,CAAS,EACzEgB,IAAc,IACvB/N,EAAQnI,EAAS8T,EAAQ,CAAC9T,CAAM,EAAI+U,EAAQ,EAAI,EACvCmB,IAAc,MACvB/N,EAAQ8M,EAAQ,EAAI,EAExB,MACE9M,EAAQ0M,EAASa,EAAUtV,GAAMsV,EAAS,EAAGP,CAAQ,EAGvD,OAAOhN,CACT,CAEA,SAAS6M,EAAY5B,EAAMQ,EAAa,CACtC,IAAI5T,GAASqV,IAAYe,EAAQ,EAAK,EAAId,GACtCnC,EAAOgD,EAAiBjB,EAAYlV,IAAUoT,EAAO,GAAK,GAAI8B,EAAW,EAAEG,GAAWe,EAAQ,EAAG,EAErG,OAAIjD,IAAS,IAAM2B,GACb,CAACnV,GAAmB6T,EAAW,EAAIW,EAAS,CAACf,CAAI,EAAG,CAAC,EAChDA,EAAO,EAAI+B,EAIfvB,EAAcT,EAAOU,EAAKV,CAAI,CACvC,CAEA,SAASgD,EAAiBhD,EAAM9H,EAAMgL,GAAU,CAC9C,GAAItG,EAAQ,GAAMqG,IAAY,CAC5B,IAAIjO,EAAQmO,EAAwBnD,CAAI,EAEpChL,IAAUgL,IACZ9H,EAAO8H,EACPA,EAAOhL,EACPkO,GAAW,IAGTlD,EAAO,GAAKA,EAAOgC,EACjB,CAACE,IAAYtV,GAAQ,EAAGoT,EAAM9H,EAAM,EAAI,GAAKtL,GAAQoV,EAAU9J,EAAM8H,EAAM,EAAI,GACjFA,EAAOW,EAAQyC,EAAOpD,CAAI,CAAC,EAEvB0B,EACF1B,EAAOkD,GAAWlD,EAAO,EAAI,EAAE/c,EAAakf,GAAWA,GAAWlf,EAAa+c,EACtEpe,EAAQ,OACjBoe,EAAOA,EAAO,EAAIgC,EAAW,EAE7BhC,EAAO,GAIPkD,IAAYlD,IAAS9H,IACvB8H,EAAOW,EAAQyC,EAAOlL,CAAI,GAAK8H,EAAO9H,EAAO,GAAK,EAAE,EAG1D,MACE8H,EAAO,GAGT,OAAOA,CACT,CAEA,SAASmD,EAAwBnD,EAAM,CACrC,GAAI2B,GAAW/f,EAAQ,YAAc,QAAUoe,IAAS+B,EAGtD,QAFIxB,EAAWF,EAAW,EAEnBE,IAAaD,EAAWN,EAAM,EAAI,GAAKpT,GAAQoT,EAAM,EAAGhP,EAAQ,OAAS,EAAG,CAACpP,EAAQ,MAAM,GAChGoe,EAAO+B,EAAY,EAAE/B,EAAO,EAAEA,EAIlC,OAAOA,CACT,CAEA,SAASU,EAAK1L,EAAO,CACnB,OAAO0M,GAAU1M,EAAQ/R,GAAcA,GAAc,EAAI+R,CAC3D,CAEA,SAASqN,GAAS,CAGhB,QAFIhb,EAAMpE,GAAcggB,EAAQ,GAAMvB,GAAUQ,EAAU,EAAIC,GAEvDV,GAAWpa,KAAQ,GACxB,GAAIiZ,EAAWrd,EAAa,EAAG,EAAI,IAAMqd,EAAWjZ,EAAK,EAAI,EAAG,CAC9DA,IACA,KACF,CAGF,OAAO4F,GAAM5F,EAAK,EAAGpE,EAAa,CAAC,CACrC,CAEA,SAAS0d,EAAQzE,EAAM,CACrB,OAAOjP,GAAMgW,IAAa/G,EAAOiG,EAAUjG,EAAM,EAAG8F,CAAQ,CAC9D,CAEA,SAASoB,EAAOpO,EAAO,CACrB,OAAOiO,EAAQ,EAAK9W,GAAI6I,EAAOgN,CAAQ,EAAI3V,IAAO2I,GAASgN,EAAW/e,EAAa,EAAI+R,GAASmN,CAAO,CACzG,CAEA,SAASkB,EAAO5C,EAAa,CAC3B,IAAIxI,EAAUsH,EAAK,QAAQkB,CAAW,EACtC,OAAOkB,EAAU1U,GAAMgL,EAAS,EAAG+J,CAAQ,EAAI/J,CACjD,CAEA,SAAS0K,EAAS3N,EAAO,CACnBA,IAAU+M,IACZE,EAAYF,EACZA,EAAY/M,EAEhB,CAEA,SAASsO,EAASrD,EAAM,CACtB,OAAOA,EAAOgC,EAAYF,CAC5B,CAEA,SAASkB,GAAW,CAClB,MAAO,CAAC9a,GAAYvG,EAAQ,KAAK,GAAKA,EAAQ,YAChD,CAEA,SAAS6gB,IAAS,CAChB,OAAOzR,EAAQ,MAAM,GAAG,CAACrK,GAAQC,EAAS,CAAC,GAAK,CAAC,CAAChF,EAAQ,iBAC5D,CAEA,MAAO,CACL,MAAOuX,EACP,GAAImJ,EACJ,OAAQM,EACR,QAAShB,EACT,QAASE,EACT,YAAaD,EACb,OAAQQ,EACR,SAAUM,EACV,SAAUW,EACV,QAAS3C,EACT,OAAQyC,EACR,OAAQC,EACR,SAAUJ,EACV,OAAQR,EACZ,CACA,CAEA,IAAIc,GAAiB,6BACjBC,GAAO,wFACPC,GAAO,GAEX,SAASC,GAAO1S,EAASgC,EAAapR,EAAS,CAC7C,IAAIsM,EAAQ6C,EAAeC,CAAO,EAC9BG,EAAKjD,EAAM,GACXL,EAAOK,EAAM,KACbkD,EAAOlD,EAAM,KACbpF,EAAUlH,EAAQ,QAClB+W,EAAO/W,EAAQ,KACf4W,EAAWxF,EAAY,SACvBmJ,EAAanJ,EAAY,WACzB2Q,EAAcnL,EAAS,OACvBlU,EAAQkU,EAAS,MACjBoL,EAAUD,EACV1D,EAAOzH,EAAS,KAChBqL,EAAOrL,EAAS,KAChBsL,EACAC,EACAC,EAAS,CAAA,EAEb,SAAS7K,GAAQ,CACfD,EAAI,EACJ/H,EAAG1B,EAAeoP,CAAO,CAC3B,CAEA,SAASA,GAAU,CACjB/P,EAAO,EACPqK,EAAK,CACP,CAEA,SAASD,GAAO,CACd,IAAI+K,EAAUriB,EAAQ,OAElBqiB,GAAW,EAAEhE,GAAQ4D,IACvBK,EAAY,EAGVjE,GAAQ4D,IACV/Z,GAAOka,EAAQ,CACb,KAAM/D,EACN,KAAM4D,CACd,CAAO,EACDhZ,GAAQ+Y,EAASK,EAAU,GAAK,MAAM,EACtChb,GAAS2a,EAASG,EAAiBnN,GAAe,KAAOhV,EAAQ,SAAS,EAEtEqiB,IACF/J,EAAM,EACNlI,EAAM,EACNzH,EAAa,CAAC0V,EAAM4D,CAAI,EAAGrO,GAAelR,EAAM,EAAE,EAClD8M,EAAKjB,GAAsB8P,EAAM4D,CAAI,GAG3C,CAEA,SAAS/U,GAAU,CACjBZ,EAAM,QAAO,EACbvC,GAAYiY,EAASG,CAAc,EAE/BD,GACF3Y,GAAOwY,EAAc,CAAC1D,EAAM4D,CAAI,EAAID,CAAO,EAC3C3D,EAAO4D,EAAO,MAEd1Z,GAAgB,CAAC8V,EAAM4D,CAAI,EAAG1N,EAAc,CAEhD,CAEA,SAAS+D,GAAS,CAChB/I,EAAG,CAACpC,GAAeG,GAAaM,EAAeQ,GAAgBc,EAAuB,EAAGkB,CAAM,EAC/FnE,EAAKgW,EAAM,QAASvc,EAAMgb,EAAI,GAAG,CAAC,EAClCzU,EAAKoS,EAAM,QAAS3Y,EAAMgb,EAAI,GAAG,CAAC,CACpC,CAEA,SAASA,EAAGC,EAAS,CACnBpG,EAAW,GAAGoG,EAAS,EAAI,CAC7B,CAEA,SAAS2B,GAAe,CACtBN,EAAUD,GAAelZ,GAAO,MAAO3B,EAAQ,MAAM,EACrDmX,EAAOkE,EAAY,EAAI,EACvBN,EAAOM,EAAY,EAAK,EACxBL,EAAU,GACV5a,GAAO0a,EAAS,CAAC3D,EAAM4D,CAAI,CAAC,EAC5B,CAACF,GAAeva,GAAOwa,EAAStf,CAAK,CACvC,CAEA,SAAS6f,EAAYC,EAAO,CAC1B,IAAIC,EAAQ,kBAAqBvb,EAAQ,MAAQ,KAAOsb,EAAQtb,EAAQ,KAAOA,EAAQ,MAAQ,+BAAqCya,GAAiB,kBAAsBE,GAAO,IAAMA,GAAO,YAAgBA,GAAO,aAAiBA,GAAO,iCAAuC7hB,EAAQ,WAAa4hB,IAAQ,OAClT,OAAOpY,GAAUiZ,CAAK,CACxB,CAEA,SAASrS,GAAS,CAChB,GAAIiO,GAAQ4D,EAAM,CAChB,IAAI7O,EAAQhE,EAAQ,MAChBiR,EAAY9F,EAAW,QAAO,EAC9BmI,EAAYnI,EAAW,QAAO,EAC9BoI,EAAYtC,EAAY,IAAMjN,EAAQiN,EAAYtJ,EAAK,KAAOA,EAAK,KACnE6L,EAAYF,EAAY,IAAMtP,EAAQsP,EAAY3L,EAAK,MAAQA,EAAK,KACxEsH,EAAK,SAAWgC,EAAY,EAC5B4B,EAAK,SAAWS,EAAY,EAC5B/Z,EAAa0V,EAAMtK,GAAY4O,CAAS,EACxCha,EAAasZ,EAAMlO,GAAY6O,CAAS,EACxCpT,EAAKhB,GAAsB6P,EAAM4D,EAAM5B,EAAWqC,CAAS,CAC7D,CACF,CAEA,MAAO,CACL,OAAQN,EACR,MAAO7K,EACP,QAASrK,EACT,OAAQkD,CACZ,CACA,CAEA,IAAIyS,GAA0B1Y,GAAiB,YAE/C,SAAS2Y,GAAS1T,EAASgC,EAAapR,EAAS,CAC/C,IAAI+iB,EAAmB5T,EAAeC,CAAO,EACzCG,EAAKwT,EAAiB,GACtB9W,EAAO8W,EAAiB,KACxBvT,EAAOuT,EAAiB,KAExBrT,EAAWD,GAAgBzP,EAAQ,SAAUoP,EAAQ,GAAG,KAAKA,EAAS,GAAG,EAAG4T,CAAgB,EAC5FrS,EAAWjB,EAAS,SACpBkH,EAAWxF,EAAY,SACvB6R,EAAyB7R,EAAY,SACrC0F,EAAOmM,EAAuB,KAC9BC,EAASD,EAAuB,OAChCE,EAAWnjB,EAAQ,SACnBojB,EACAC,EACAC,EAAUH,IAAa,QAE3B,SAAS5L,GAAQ,CACX4L,IACF7K,EAAM,EACN4K,GAAUva,EAAaua,EAAQtP,GAAegD,EAAS,MAAM,EAAE,EAC/D0M,GAAWC,EAAI,EACfnT,EAAM,EAEV,CAEA,SAASkI,GAAS,CACZtY,EAAQ,cACViM,EAAK6K,EAAM,wBAAyB,SAAUnN,EAAG,CAC/CyZ,EAAUzZ,EAAE,OAAS,aACrB6Z,EAAU,CACZ,CAAC,EAGCxjB,EAAQ,cACViM,EAAK6K,EAAM,mBAAoB,SAAUnN,EAAG,CAC1C0Z,EAAU1Z,EAAE,OAAS,UACrB6Z,EAAU,CACZ,CAAC,EAGCN,GACFjX,EAAKiX,EAAQ,QAAS,UAAY,CAChCI,EAAUC,EAAI,EAAKlT,EAAM,EAAI,CAC/B,CAAC,EAGHd,EAAG,CAAClC,GAAYc,GAAcP,CAAa,EAAG8B,EAAS,MAAM,EAC7DH,EAAGlC,GAAYoL,CAAM,CACvB,CAEA,SAAS8K,GAAO,CACV5S,EAAQ,GAAMS,EAAY,OAAO,SAAQ,IAC3C1B,EAAS,MAAM,CAAC1P,EAAQ,aAAa,EACrCqjB,EAAUD,EAAUE,EAAU,GAC9BlT,EAAM,EACNZ,EAAKZ,EAAmB,EAE5B,CAEA,SAASyB,EAAMoT,EAAM,CACfA,IAAS,SACXA,EAAO,IAGTH,EAAU,CAAC,CAACG,EACZrT,EAAM,EAEDO,EAAQ,IACXjB,EAAS,MAAK,EACdF,EAAKV,EAAoB,EAE7B,CAEA,SAAS0U,GAAa,CACfF,IACHF,GAAWC,EAAUhT,EAAM,EAAK,EAAIkT,EAAI,EAE5C,CAEA,SAASnT,GAAS,CACZ8S,IACFlc,GAAYkc,EAAQtN,GAAc,CAAC0N,CAAO,EAC1C3a,EAAaua,EAAQnP,GAAY/T,EAAQ,KAAKsjB,EAAU,OAAS,OAAO,CAAC,EAE7E,CAEA,SAASN,EAAiBhT,EAAM,CAC9B,IAAI0T,EAAM9M,EAAS,IACnB8M,GAAO3a,GAAM2a,EAAK,QAAS1T,EAAO,IAAM,GAAG,EAC3CR,EAAKX,GAAwBmB,CAAI,CACnC,CAEA,SAASyI,EAAOrF,EAAO,CACrB,IAAIoJ,EAAQpL,EAAY,OAAO,MAAMgC,CAAK,EAC1C1D,EAAS,IAAI8M,GAAS,CAACpT,GAAaoT,EAAM,MAAOqG,EAAuB,GAAK7iB,EAAQ,QAAQ,CAC/F,CAEA,MAAO,CACL,MAAOuX,EACP,QAAS7H,EAAS,OAClB,KAAM6T,EACN,MAAOlT,EACP,SAAUM,CACd,CACA,CAEA,SAASgT,GAAMvU,EAASgC,EAAapR,EAAS,CAC5C,IAAI4jB,EAAmBzU,EAAeC,CAAO,EACzCG,EAAKqU,EAAiB,GAE1B,SAASrM,GAAQ,CACXvX,EAAQ,QACVuP,EAAGR,GAAuBrJ,EAAMwd,EAAQ,EAAI,CAAC,EAC7C3T,EAAG,CAACpC,GAAeU,EAAeD,CAAa,EAAGlI,EAAMme,EAAO,EAAI,CAAC,EAExE,CAEA,SAASA,EAAMC,EAAQ,CACrB1S,EAAY,OAAO,QAAQ,SAAUoL,EAAO,CAC1C,IAAI1B,EAAMva,GAAMic,EAAM,WAAaA,EAAM,MAAO,KAAK,EAEjD1B,GAAOA,EAAI,KACboI,EAAOY,EAAQhJ,EAAK0B,CAAK,CAE7B,CAAC,CACH,CAEA,SAAS0G,EAAOY,EAAQhJ,EAAK0B,EAAO,CAClCA,EAAM,MAAM,aAAcsH,EAAS,+BAAkChJ,EAAI,IAAM,KAAQ,GAAI,EAAI,EAC/F7R,GAAQ6R,EAAKgJ,EAAS,OAAS,EAAE,CACnC,CAEA,MAAO,CACL,MAAOvM,EACP,QAAS7R,EAAMme,EAAO,EAAK,CAC/B,CACA,CAEA,IAAIE,GAAwB,GACxBC,GAAkB,IAClBC,GAAkB,GAClBC,GAAgB,IAChBC,GAAe,IAEnB,SAASC,GAAOhV,EAASgC,EAAapR,EAAS,CAC7C,IAAIqkB,EAAmBlV,EAAeC,CAAO,EACzCG,EAAK8U,EAAiB,GACtB7U,EAAO6U,EAAiB,KAExB5T,EAAMrB,EAAQ,MAAM,IACpBuO,EAAOvM,EAAY,KACnBqN,EAAcd,EAAK,YACnByB,EAAWzB,EAAK,SAChB+B,EAAgB/B,EAAK,cACrBY,EAAYZ,EAAK,UACjBoC,EAAU3Q,EAAQ,GAAGuI,EAAK,EAC1BjI,EACAtD,EACAkY,EAAW,EAEf,SAAS/M,GAAQ,CACfhI,EAAGlC,GAAYkX,CAAK,EACpBhV,EAAG,CAAC1B,EAAeD,CAAa,EAAG2C,CAAM,CAC3C,CAEA,SAASyQ,EAAOnC,EAAa3N,EAAU+P,EAAMuD,EAAYC,EAAa,CACpE,IAAInO,EAAOmI,EAAW,EAGtB,GAFA8F,EAAK,EAEDtD,IAAS,CAAClB,GAAW,CAACL,EAAa,GAAK,CAC1C,IAAIL,EAAOjO,EAAY,OAAO,WAAU,EACpCkC,EAAShI,GAAKuT,CAAW,EAAIQ,EAAO5U,GAAME,EAAIkU,CAAW,EAAIQ,CAAI,GAAK,EAC1ER,EAAclB,EAAK,WAAWvM,EAAY,WAAW,OAAOyN,EAAcQ,CAAI,CAAC,EAAI/L,CACrF,CAEA,IAAIoR,EAAa9Z,GAAmB0L,EAAMuI,EAAa,CAAC,EACxDyF,EAAW,EACXpT,EAAWwT,EAAa,EAAIxT,GAAY1G,GAAIG,EAAIkU,EAAcvI,CAAI,EAAI4N,GAAeC,EAAY,EACjG/X,EAAWoY,EACX9U,EAAWD,GAAgByB,EAAUyT,EAAOjf,EAAM0K,EAAQkG,EAAMuI,EAAa4F,CAAW,EAAG,CAAC,EAC5FhU,EAAIzL,EAAS,EACbwK,EAAKrB,EAAY,EACjBuB,EAAS,MAAK,CAChB,CAEA,SAASiV,GAAQ,CACflU,EAAI3L,EAAI,EACRsH,GAAYA,EAAQ,EACpBoD,EAAKpB,EAAc,CACrB,CAEA,SAASgC,EAAOkG,EAAMsO,EAAIH,EAAazU,EAAM,CAC3C,IAAI2O,EAAWF,EAAW,EACtBta,EAASmS,GAAQsO,EAAKtO,GAAQuO,EAAO7U,CAAI,EACzC2J,GAAQxV,EAASwa,GAAY2F,EACjC/F,EAAUI,EAAWhF,CAAI,EAErBoG,GAAW,CAAC0E,GAAe/E,MAC7B4E,GAAYL,GAERtZ,EAAIgP,CAAI,EAAIoK,IACd/C,EAAO5B,EAASM,EAAc,EAAI,CAAC,EAAGsE,GAAiB,GAAO5X,EAAU,EAAI,EAGlF,CAEA,SAASmY,GAAQ,CACX7U,GACFA,EAAS,OAAM,CAEnB,CAEA,SAASa,GAAS,CACZb,GAAY,CAACA,EAAS,aACxB6U,EAAK,EACLI,EAAK,EAET,CAEA,SAASE,EAAOC,EAAG,CACjB,IAAIC,EAAa/kB,EAAQ,WACzB,OAAO+kB,EAAaA,EAAWD,CAAC,EAAI,EAAI,KAAK,IAAI,EAAIA,EAAG,CAAC,CAC3D,CAEA,MAAO,CACL,MAAOvN,EACP,QAASgN,EACT,OAAQvD,EACR,OAAQzQ,CACZ,CACA,CAEA,IAAIyU,GAA0B,CAC5B,QAAS,GACT,QAAS,EACX,EAEA,SAASC,GAAK7V,EAASgC,EAAapR,EAAS,CAC3C,IAAIklB,EAAmB/V,EAAeC,CAAO,EACzCG,EAAK2V,EAAiB,GACtB1V,EAAO0V,EAAiB,KACxBjZ,EAAOiZ,EAAiB,KACxBxY,EAASwY,EAAiB,OAE1BpU,EAAQ1B,EAAQ,MAChBuO,EAAOvM,EAAY,KACnBgT,EAAShT,EAAY,OACrBmJ,EAAanJ,EAAY,WACzB1O,EAAQ0O,EAAY,SAAS,MAC7BgB,EAAShB,EAAY,MAAM,OAC3B+T,EAAyB/T,EAAY,UACrC8B,EAAUiS,EAAuB,QACjC5R,EAAS4R,EAAuB,OAChC1G,EAAcd,EAAK,YACnB+B,EAAgB/B,EAAK,cACrByH,EACAC,EACAC,EACAC,EACAC,EACAC,EAAW,GACXC,EACAC,EACAxhB,EAEJ,SAASoT,GAAQ,CACftL,EAAKvJ,EAAOgU,GAAqB7Q,GAAMmf,EAAuB,EAC9D/Y,EAAKvJ,EAAOiU,GAAmB9Q,GAAMmf,EAAuB,EAC5D/Y,EAAKvJ,EAAO+T,GAAqBmP,EAAeZ,EAAuB,EACvE/Y,EAAKvJ,EAAO,QAASmjB,EAAS,CAC5B,QAAS,EACf,CAAK,EACD5Z,EAAKvJ,EAAO,YAAagH,EAAO,EAChC6F,EAAG,CAACpC,GAAeU,CAAa,EAAGyJ,CAAI,CACzC,CAEA,SAASA,GAAO,CACd,IAAIwO,EAAO9lB,EAAQ,KACnB+lB,GAAQ,CAACD,CAAI,EACbP,EAASO,IAAS,MACpB,CAEA,SAASF,EAAcjc,EAAG,CAGxB,GAFA+b,EAAiB,GAEb,CAACC,EAAU,CACb,IAAIK,EAAUC,EAAatc,CAAC,EAExBuc,EAAYvc,EAAE,MAAM,IAAMqc,GAAW,CAACrc,EAAE,UACrC4Q,EAAW,SAUd7Q,GAAQC,EAAG,EAAI,GATfxF,EAAS6hB,EAAUtjB,EAAQ,OAC3B8iB,EAAW1U,EAAM,GAAG,CAAC/L,GAAQC,EAAS,CAAC,EACvCsgB,EAAgB,KAChBrZ,EAAK9H,EAAQuS,GAAqByP,EAAenB,EAAuB,EACxE/Y,EAAK9H,EAAQwS,GAAmByP,EAAapB,EAAuB,EACpErH,EAAK,OAAM,EACXyG,EAAO,OAAM,EACbiC,EAAK1c,CAAC,GAKZ,CACF,CAEA,SAASwc,EAAcxc,EAAG,CAMxB,GALKmH,EAAM,GAAG7L,EAAQ,IACpB6L,EAAM,IAAI7L,EAAQ,EAClBuK,EAAKxB,EAAU,GAGbrE,EAAE,WACJ,GAAI6b,EAAU,CACZ7H,EAAK,UAAUyH,EAAekB,GAAUC,EAAU5c,CAAC,CAAC,CAAC,EACrD,IAAI6c,EAAUC,GAAS9c,CAAC,EAAI6M,GACxBkQ,GAAcjB,KAAcA,EAAW/F,EAAa,IAEpD8G,GAAWE,KACbL,EAAK1c,CAAC,EAGR+b,EAAiB,GACjBlW,EAAKvB,EAAc,EACnBvE,GAAQC,CAAC,CACX,MAAWgd,EAAkBhd,CAAC,IAC5B6b,EAAWoB,EAAYjd,CAAC,EACxBD,GAAQC,CAAC,EAGf,CAEA,SAASyc,EAAYzc,EAAG,CAClBmH,EAAM,GAAG7L,EAAQ,IACnB6L,EAAM,IAAIhM,EAAI,EACd0K,EAAKtB,EAAa,GAGhBsX,IACFrH,EAAKxU,CAAC,EACND,GAAQC,CAAC,GAGX+C,EAAOvI,EAAQuS,GAAqByP,CAAa,EACjDzZ,EAAOvI,EAAQwS,GAAmByP,CAAW,EAC7CZ,EAAW,EACb,CAEA,SAASK,EAAQlc,EAAG,CACd,CAACgc,GAAYD,GACfhc,GAAQC,EAAG,EAAI,CAEnB,CAEA,SAAS0c,EAAK1c,EAAG,CACf2b,EAAgBD,EAChBA,EAAY1b,EACZyb,EAAe3G,EAAW,CAC5B,CAEA,SAASN,EAAKxU,EAAG,CACf,IAAIkd,EAAWC,EAAgBnd,CAAC,EAC5BkV,GAAckI,EAAmBF,CAAQ,EACzCrW,GAASxQ,EAAQ,QAAUA,EAAQ,aACvCoS,EAAO,EAAK,EAERmT,EACFhL,EAAW,OAAOsE,GAAa,EAAG7e,EAAQ,IAAI,EACrCoP,EAAQ,GAAGyI,EAAI,EACxB0C,EAAW,GAAGhH,EAAOjI,GAAKub,CAAQ,CAAC,EAAI,EAAIrW,GAAS,IAAM,IAAMA,GAAS,IAAM,GAAG,EACzEpB,EAAQ,GAAGuI,EAAK,GAAK8N,GAAYjV,GAC1C+J,EAAW,GAAGmF,EAAc,EAAI,EAAI,IAAM,GAAG,EAE7CnF,EAAW,GAAGA,EAAW,OAAOsE,EAAW,EAAG,EAAI,EAGpDzM,EAAO,EAAI,CACb,CAEA,SAASwU,EAAYjd,EAAG,CACtB,IAAIqd,EAAahnB,EAAQ,iBACrBinB,GAAQ/gB,GAAS8gB,CAAU,EAC3BE,GAAQD,IAASD,EAAW,OAAS,EACrCG,IAASF,GAAQD,EAAW,MAAQ,CAACA,IAAe,GACxD,OAAOrc,EAAI4b,EAAU5c,CAAC,CAAC,GAAKsc,EAAatc,CAAC,EAAIwd,GAAQD,GACxD,CAEA,SAASP,EAAkBhd,EAAG,CAC5B,OAAOgB,EAAI4b,EAAU5c,CAAC,CAAC,EAAIgB,EAAI4b,EAAU5c,EAAG,EAAI,CAAC,CACnD,CAEA,SAASmd,EAAgBnd,EAAG,CAC1B,GAAIyF,EAAQ,GAAGwI,EAAI,GAAK,CAAC6N,EAAU,CACjC,IAAI/U,EAAO+V,GAAS9c,CAAC,EAErB,GAAI+G,GAAQA,EAAO8F,GACjB,OAAO+P,EAAU5c,CAAC,EAAI+G,CAE1B,CAEA,MAAO,EACT,CAEA,SAASqW,EAAmBF,EAAU,CACpC,OAAOpI,EAAW,EAAKnT,GAAKub,CAAQ,EAAItc,GAAII,EAAIkc,CAAQ,GAAK7mB,EAAQ,YAAc,KAAMulB,EAAS,IAAWnU,EAAY,OAAO,SAAQ,GAAMpR,EAAQ,eAAiB,EAAE,CAC3K,CAEA,SAASumB,EAAU5c,EAAGyd,EAAY,CAChC,OAAOC,EAAQ1d,EAAGyd,CAAU,EAAIC,EAAQC,EAAa3d,CAAC,EAAGyd,CAAU,CACrE,CAEA,SAASX,GAAS9c,EAAG,CACnB,OAAOK,GAAOL,CAAC,EAAIK,GAAOsd,EAAa3d,CAAC,CAAC,CAC3C,CAEA,SAAS2d,EAAa3d,EAAG,CACvB,OAAO0b,IAAc1b,GAAK2b,GAAiBD,CAC7C,CAEA,SAASgC,EAAQ1d,EAAGyd,EAAY,CAC9B,OAAQnB,EAAatc,CAAC,EAAIA,EAAE,eAAe,CAAC,EAAIA,GAAG,OAASuJ,EAAQkU,EAAa,IAAM,GAAG,CAAC,CAC7F,CAEA,SAASd,GAAU3M,EAAM,CACvB,OAAOA,GAAQ8L,GAAYrW,EAAQ,GAAGuI,EAAK,EAAIpB,GAAW,EAC5D,CAEA,SAAS2P,EAAYqB,EAAS,CAC5B,IAAIC,EAASxnB,EAAQ,OACrB,MAAO,CAAC4H,GAAQ2f,EAAS,IAAMlS,GAAwB,MAAQJ,EAAW,IAAM,CAACuS,GAAU,CAAC5f,GAAQ2f,EAASC,CAAM,EACrH,CAEA,SAASvB,EAAatc,EAAG,CACvB,OAAO,OAAO,WAAe,KAAeA,aAAa,UAC3D,CAEA,SAAS8d,IAAa,CACpB,OAAOjC,CACT,CAEA,SAASO,GAAQ3lB,EAAO,CACtBulB,EAAWvlB,CACb,CAEA,MAAO,CACL,MAAOmX,EACP,QAASwO,GACT,WAAY0B,EAChB,CACA,CAEA,IAAIC,GAAoB,CACtB,SAAU,IACV,MAAO/U,GACP,KAAMD,GACN,GAAIE,GACJ,KAAMC,EACR,EAEA,SAAS8U,GAAaxnB,EAAK,CACzB,OAAAA,EAAMmG,GAASnG,CAAG,EAAIA,EAAMA,EAAI,IACzBunB,GAAkBvnB,CAAG,GAAKA,CACnC,CAEA,IAAIynB,GAAiB,UAErB,SAASC,GAASzY,EAASgC,EAAapR,EAAS,CAC/C,IAAI8nB,EAAoB3Y,EAAeC,CAAO,EAC1CG,EAAKuY,EAAkB,GACvB7b,EAAO6b,EAAkB,KACzBpb,EAASob,EAAkB,OAE3BhR,EAAO1H,EAAQ,KACf8D,EAAU9B,EAAY,UAAU,QAChCjN,EACAwhB,EAEJ,SAASpO,GAAQ,CACfD,EAAI,EACJ/H,EAAG1B,EAAeX,CAAO,EACzBqC,EAAG1B,EAAeyJ,CAAI,EACtB/H,EAAGlC,GAAYoL,CAAM,CACvB,CAEA,SAASnB,GAAO,CACd,IAAIyQ,EAAW/nB,EAAQ,SAEnB+nB,IACF5jB,EAAS4jB,IAAa,SAAW,OAASjR,EAC1C7K,EAAK9H,EAAQyjB,GAAgBI,CAAS,EAE1C,CAEA,SAAS9a,GAAU,CACjBR,EAAOvI,EAAQyjB,EAAc,CAC/B,CAEA,SAAS7B,EAAQ3lB,EAAO,CACtBulB,EAAWvlB,CACb,CAEA,SAASqY,GAAS,CAChB,IAAIwP,EAAYtC,EAChBA,EAAW,GACX/f,GAAS,UAAY,CACnB+f,EAAWsC,CACb,CAAC,CACH,CAEA,SAASD,EAAUre,EAAG,CACpB,GAAI,CAACgc,EAAU,CACb,IAAIxlB,EAAMwnB,GAAahe,CAAC,EAEpBxJ,IAAQ+S,EAAQR,EAAU,EAC5BtD,EAAQ,GAAG,GAAG,EACLjP,IAAQ+S,EAAQP,EAAW,GACpCvD,EAAQ,GAAG,GAAG,CAElB,CACF,CAEA,MAAO,CACL,MAAOmI,EACP,QAASrK,EACT,QAAS6Y,CACb,CACA,CAEA,IAAImC,GAAqB/d,GAAiB,QACtCge,GAAwBD,GAAqB,UAC7CE,GAAiB,IAAMF,GAAqB,OAASC,GAAwB,IAEjF,SAASE,GAASjZ,EAASgC,EAAapR,EAAS,CAC/C,IAAIsoB,EAAoBnZ,EAAeC,CAAO,EAC1CG,EAAK+Y,EAAkB,GACvBC,EAAMD,EAAkB,IACxBrc,EAAOqc,EAAkB,KACzB9Y,EAAO8Y,EAAkB,KAEzBE,EAAexoB,EAAQ,WAAa,aACpCmM,EAAS,CAACmB,GAAac,EAAc,EACrCqa,EAAU,CAAA,EAEd,SAASlR,GAAQ,CACXvX,EAAQ,WACVsX,EAAI,EACJ/H,EAAG3B,EAAe0J,CAAI,EAE1B,CAEA,SAASA,GAAO,CACdlS,GAAMqjB,CAAO,EACb9W,EAAQ,EAEJ6W,EACFE,EAAQ,GAERH,EAAIpc,CAAM,EACVoD,EAAGpD,EAAQwc,CAAK,EAChBA,EAAK,EAET,CAEA,SAAShX,GAAW,CAClBP,EAAY,OAAO,QAAQ,SAAUoL,EAAO,CAC1C1S,GAAS0S,EAAM,MAAO4L,EAAc,EAAE,QAAQ,SAAUtN,EAAK,CAC3D,IAAI8N,EAAMxf,GAAa0R,EAAKoN,EAAkB,EAC1CW,EAASzf,GAAa0R,EAAKqN,EAAqB,EAEpD,GAAIS,IAAQ9N,EAAI,KAAO+N,IAAW/N,EAAI,OAAQ,CAC5C,IAAIvY,EAAYvC,EAAQ,QAAQ,QAC5BM,EAASwa,EAAI,cACbgO,EAAUvoB,GAAMD,EAAQ,IAAMiC,CAAS,GAAKsG,GAAO,OAAQtG,EAAWjC,CAAM,EAChFmoB,EAAQ,KAAK,CAAC3N,EAAK0B,EAAOsM,CAAO,CAAC,EAClChO,EAAI,KAAO7R,GAAQ6R,EAAK,MAAM,CAChC,CACF,CAAC,CACH,CAAC,CACH,CAEA,SAAS6N,GAAQ,CACfF,EAAUA,EAAQ,OAAO,SAAU7kB,EAAM,CACvC,IAAI8V,EAAW1Z,EAAQ,UAAYA,EAAQ,cAAgB,GAAK,GAAK,EACrE,OAAO4D,EAAK,CAAC,EAAE,SAASwL,EAAQ,MAAOsK,CAAQ,EAAIqP,EAAKnlB,CAAI,EAAI,EAClE,CAAC,EACD6kB,EAAQ,QAAUF,EAAIpc,CAAM,CAC9B,CAEA,SAAS4c,EAAKnlB,EAAM,CAClB,IAAIkX,EAAMlX,EAAK,CAAC,EAChByD,GAASzD,EAAK,CAAC,EAAE,MAAOoS,EAAa,EACrC/J,EAAK6O,EAAK,aAAcpV,EAAMsjB,EAAQplB,CAAI,CAAC,EAC3C+E,EAAamS,EAAK,MAAO1R,GAAa0R,EAAKoN,EAAkB,CAAC,EAC9Dvf,EAAamS,EAAK,SAAU1R,GAAa0R,EAAKqN,EAAqB,CAAC,EACpE5f,GAAgBuS,EAAKoN,EAAkB,EACvC3f,GAAgBuS,EAAKqN,EAAqB,CAC5C,CAEA,SAASa,EAAOplB,EAAM+F,EAAG,CACvB,IAAImR,EAAMlX,EAAK,CAAC,EACZ4Y,EAAQ5Y,EAAK,CAAC,EAClBmG,GAAYyS,EAAM,MAAOxG,EAAa,EAElCrM,EAAE,OAAS,UACbJ,GAAO3F,EAAK,CAAC,CAAC,EACdqF,GAAQ6R,EAAK,EAAE,EACftL,EAAKT,GAAuB+L,EAAK0B,CAAK,EACtChN,EAAK1B,EAAY,GAGnB0a,GAAgBE,EAAQ,CAC1B,CAEA,SAASA,GAAW,CAClBD,EAAQ,QAAUM,EAAKN,EAAQ,MAAK,CAAE,CACxC,CAEA,MAAO,CACL,MAAOlR,EACP,QAAS7R,EAAMN,GAAOqjB,CAAO,EAC7B,MAAOE,CACX,CACA,CAEA,SAASM,GAAW7Z,EAASgC,EAAapR,EAAS,CACjD,IAAIsM,EAAQ6C,EAAeC,CAAO,EAC9BG,EAAKjD,EAAM,GACXkD,EAAOlD,EAAM,KACbL,EAAOK,EAAM,KACbsN,EAASxI,EAAY,OACrBwF,EAAWxF,EAAY,SACvBmJ,EAAanJ,EAAY,WACzBiQ,EAAW9G,EAAW,SACtBmH,EAAWnH,EAAW,SACtBmG,EAAKnG,EAAW,GAChBrH,EAAU9B,EAAY,UAAU,QAChC2Q,EAAcnL,EAAS,WACvB7P,EAAQ,CAAA,EACRpE,EACAumB,EAEJ,SAAS3R,GAAQ,CACfrK,EAAO,EACPqC,EAAG,CAAC1B,EAAeD,EAAesB,EAAuB,EAAGqI,CAAK,EACjE,IAAI8K,EAAUriB,EAAQ,WACtB+hB,GAAe9Y,GAAQ8Y,EAAaM,EAAU,GAAK,MAAM,EAErDA,IACF9S,EAAG,CAAClC,GAAYc,GAAcC,EAAc,EAAGgC,CAAM,EACrD+Y,EAAgB,EAChB/Y,EAAM,EACNZ,EAAKf,GAA0B,CAC7B,KAAM9L,EACN,MAAOoE,CACf,EAASyT,EAAMpL,EAAQ,KAAK,CAAC,EAE3B,CAEA,SAASlC,GAAU,CACbvK,IACF4G,GAAOwY,EAAczc,GAAM3C,EAAK,QAAQ,EAAIA,CAAI,EAChDoH,GAAYpH,EAAMumB,CAAiB,EACnC9jB,GAAM2B,CAAK,EACXpE,EAAO,MAGT2J,EAAM,QAAO,CACf,CAEA,SAAS6c,GAAmB,CAC1B,IAAItO,EAASzL,EAAQ,OACjBlI,EAAUlH,EAAQ,QAClB+W,EAAO/W,EAAQ,KACfugB,EAAUvgB,EAAQ,QAClBwK,EAAM6W,EAAQ,EAAK9G,EAAW,OAAM,EAAK,EAAI7P,GAAKmQ,EAAS0F,CAAO,EACtE5d,EAAOof,GAAelZ,GAAO,KAAM3B,EAAQ,WAAY0P,EAAS,MAAM,aAAa,EACnFvP,GAAS1E,EAAMumB,EAAoB9T,GAAmB,KAAOgU,EAAY,CAAE,EAC3EzgB,EAAahG,EAAM6Q,GAAM,SAAS,EAClC7K,EAAahG,EAAMoR,GAAYgD,EAAK,MAAM,EAC1CpO,EAAahG,EAAMuR,GAAkBkV,EAAY,IAAOrW,GAAM,WAAa,EAAE,EAE7E,QAAS1O,EAAI,EAAGA,EAAImG,EAAKnG,IAAK,CAC5B,IAAIglB,EAAKxgB,GAAO,KAAM,KAAMlG,CAAI,EAC5B2mB,EAASzgB,GAAO,SAAU,CAC5B,MAAO3B,EAAQ,KACf,KAAM,QACd,EAASmiB,CAAE,EACD3Q,EAAWkB,EAAO,MAAMvV,CAAC,EAAE,IAAI,SAAUmY,EAAO,CAClD,OAAOA,EAAM,MAAM,EACrB,CAAC,EACG+M,EAAO,CAAClI,KAAcd,EAAU,EAAIxJ,EAAK,MAAQA,EAAK,OAC1D9K,EAAKqd,EAAQ,QAAS5jB,EAAMmgB,EAASxhB,CAAC,CAAC,EAEnCrE,EAAQ,oBACViM,EAAKqd,EAAQ,UAAW5jB,EAAMsiB,EAAW3jB,CAAC,CAAC,EAG7CsE,EAAa0gB,EAAI7V,GAAM,cAAc,EACrC7K,EAAa2gB,EAAQ9V,GAAM,KAAK,EAChC7K,EAAa2gB,EAAQ1V,GAAe8E,EAAS,KAAK,GAAG,CAAC,EACtD/P,EAAa2gB,EAAQvV,GAAYxI,GAAOge,EAAMllB,EAAI,CAAC,CAAC,EACpDsE,EAAa2gB,EAAQ7V,GAAW,EAAE,EAClC1M,EAAM,KAAK,CACT,GAAIsiB,EACJ,OAAQC,EACR,KAAMjlB,CACd,CAAO,CACH,CACF,CAEA,SAASwhB,EAAQvL,EAAM,CACrBoG,EAAG,IAAMpG,EAAM,EAAI,CACrB,CAEA,SAAS0N,EAAU1N,EAAM3Q,EAAG,CAC1B,IAAIkR,EAAS9T,EAAM,OACf5G,EAAMwnB,GAAahe,CAAC,EACpB6f,EAAMJ,EAAY,EAClBK,EAAW,GAEXtpB,IAAQ+S,EAAQP,GAAa,GAAO6W,CAAG,EACzCC,EAAW,EAAEnP,EAAOO,EACX1a,IAAQ+S,EAAQR,GAAY,GAAO8W,CAAG,EAC/CC,GAAY,EAAEnP,EAAOO,GAAUA,EACtB1a,IAAQ,OACjBspB,EAAW,EACFtpB,IAAQ,QACjBspB,EAAW5O,EAAS,GAGtB,IAAI6O,EAAO3iB,EAAM0iB,CAAQ,EAErBC,IACFvgB,GAAMugB,EAAK,MAAM,EACjBhJ,EAAG,IAAM+I,CAAQ,EACjB/f,GAAQC,EAAG,EAAI,EAEnB,CAEA,SAASyf,GAAe,CACtB,OAAOppB,EAAQ,qBAAuBA,EAAQ,SAChD,CAEA,SAASwa,EAAMpH,EAAO,CACpB,OAAOrM,EAAMwT,EAAW,OAAOnH,CAAK,CAAC,CACvC,CAEA,SAAShD,GAAS,CAChB,IAAIiO,EAAO7D,EAAMkH,EAAS,EAAI,CAAC,EAC3B9I,EAAO4B,EAAMkH,GAAU,EAE3B,GAAIrD,EAAM,CACR,IAAIiL,EAASjL,EAAK,OAClBtU,GAAYuf,EAAQ1T,EAAY,EAChCrN,GAAgB+gB,EAAQxV,EAAa,EACrCnL,EAAa2gB,EAAQ7V,GAAW,EAAE,CACpC,CAEA,GAAImF,EAAM,CACR,IAAI+Q,EAAU/Q,EAAK,OACnBvR,GAASsiB,EAAS/T,EAAY,EAC9BjN,EAAaghB,EAAS7V,GAAe,EAAI,EACzCnL,EAAaghB,EAASlW,GAAW,EAAE,CACrC,CAEAjE,EAAKd,GAA0B,CAC7B,KAAM/L,EACN,MAAOoE,CACb,EAAOsX,EAAMzF,CAAI,CACf,CAEA,MAAO,CACL,MAAO7R,EACP,MAAOwQ,EACP,QAASrK,EACT,MAAOsN,EACP,OAAQpK,CACZ,CACA,CAEA,IAAIwZ,GAAe,CAAC,IAAK,OAAO,EAEhC,SAASC,GAAKza,EAASgC,EAAapR,EAAS,CAC3C,IAAIgY,EAAehY,EAAQ,aACvBkY,EAAalY,EAAQ,WACrBmM,EAAS,CAAA,EAEb,SAASoL,GAAQ,CACfnI,EAAQ,QAAQ,QAAQ,SAAUjL,EAAQ,CACnCA,EAAO,WACV2lB,EAAK1a,EAASjL,EAAO,MAAM,EAC3B2lB,EAAK3lB,EAAO,OAAQiL,CAAO,EAE/B,CAAC,EAEG4I,GACF+R,EAAQ,CAEZ,CAEA,SAAS7c,GAAU,CACjBf,EAAO,QAAQ,SAAUG,EAAO,CAC9BA,EAAM,QAAO,CACf,CAAC,EACDlH,GAAM+G,CAAM,CACd,CAEA,SAAS8Q,GAAU,CACjB/P,EAAO,EACPqK,EAAK,CACP,CAEA,SAASuS,EAAKtnB,EAAQ2B,EAAQ,CAC5B,IAAImI,EAAQ6C,EAAe3M,CAAM,EACjC8J,EAAM,GAAGe,GAAY,SAAU+F,EAAOiL,EAAMD,EAAM,CAChDja,EAAO,GAAGA,EAAO,GAAGyT,EAAI,EAAIwG,EAAOhL,CAAK,CAC1C,CAAC,EACDjH,EAAO,KAAKG,CAAK,CACnB,CAEA,SAASyd,GAAW,CAClB,IAAIzd,EAAQ6C,EAAeC,CAAO,EAC9BG,EAAKjD,EAAM,GACfiD,EAAGhC,GAAasY,CAAO,EACvBtW,EAAGP,GAAqBgZ,CAAS,EACjCzY,EAAG,CAACpC,GAAeU,CAAa,EAAGuC,CAAM,EACzCjE,EAAO,KAAKG,CAAK,EACjBA,EAAM,KAAKqC,GAA0BS,EAAQ,OAAO,CACtD,CAEA,SAASgB,GAAS,CAChBzH,EAAayI,EAAY,SAAS,KAAM8C,GAAkBlU,EAAQ,YAAc+S,GAAM,WAAa,EAAE,CACvG,CAEA,SAAS8S,EAAQrJ,EAAO,CACtBpN,EAAQ,GAAGoN,EAAM,KAAK,CACxB,CAEA,SAASwL,EAAUxL,EAAO7S,EAAG,CACvB9C,GAAS+iB,GAAcjC,GAAahe,CAAC,CAAC,IACxCkc,EAAQrJ,CAAK,EACb9S,GAAQC,CAAC,EAEb,CAEA,MAAO,CACL,MAAOjE,EAAM0L,EAAY,MAAM,IAAK,CAClC,WAAY7K,GAAY2R,CAAU,EAAIF,EAAeE,CAC3D,EAAO,EAAI,EACP,MAAOX,EACP,QAASrK,EACT,QAAS+P,CACb,CACA,CAEA,SAAS+M,GAAM5a,EAASgC,EAAapR,EAAS,CAC5C,IAAIiqB,EAAoB9a,EAAeC,CAAO,EAC1CnD,EAAOge,EAAkB,KAEzBC,EAAW,EAEf,SAAS3S,GAAQ,CACXvX,EAAQ,OACViM,EAAKmF,EAAY,SAAS,MAAO,QAAS+Y,EAASnF,EAAuB,CAE9E,CAEA,SAASmF,EAAQxgB,EAAG,CAClB,GAAIA,EAAE,WAAY,CAChB,IAAIygB,EAASzgB,EAAE,OACXuV,EAAYkL,EAAS,EACrBC,EAAYrgB,GAAOL,CAAC,EAEpB2gB,EAAOtqB,EAAQ,mBAAqB,EAEpCuqB,EAAQvqB,EAAQ,YAAc,EAE9B2K,EAAIyf,CAAM,EAAIE,GAAQD,EAAYH,EAAWK,IAC/Cnb,EAAQ,GAAG8P,EAAY,IAAM,GAAG,EAChCgL,EAAWG,GAGbG,EAActL,CAAS,GAAKxV,GAAQC,CAAC,CACvC,CACF,CAEA,SAAS6gB,EAActL,EAAW,CAChC,MAAO,CAAClf,EAAQ,cAAgBoP,EAAQ,MAAM,GAAGrK,EAAM,GAAKqM,EAAY,WAAW,YAAY8N,CAAS,IAAM,EAChH,CAEA,MAAO,CACL,MAAO3H,CACX,CACA,CAEA,IAAIkT,GAAmB,GAEvB,SAASC,GAAKtb,EAASgC,EAAapR,EAAS,CAC3C,IAAI2qB,EAAoBxb,EAAeC,CAAO,EAC1CG,EAAKob,EAAkB,GAEvBjoB,EAAQ0O,EAAY,SAAS,MAC7BiR,EAAUriB,EAAQ,MAAQ,CAACA,EAAQ,aACnC4qB,EAAK/hB,GAAO,OAAQ6M,EAAQ,EAC5BhG,EAAWD,GAAgBgb,GAAkB/kB,EAAMwd,EAAQ,EAAK,CAAC,EAErE,SAAS3L,GAAQ,CACX8K,IACF0D,EAAQ,CAAC3U,EAAY,SAAS,SAAQ,CAAE,EACxCzI,EAAajG,EAAO4R,GAAa,EAAI,EACrCsW,EAAG,YAAc,IACjBrb,EAAGX,GAAqBlJ,EAAMqgB,EAAS,EAAI,CAAC,EAC5CxW,EAAGT,GAAsBpJ,EAAMqgB,EAAS,EAAK,CAAC,EAC9CxW,EAAG,CAACjC,GAAac,EAAc,EAAG1I,EAAMwd,EAAQ,EAAI,CAAC,EAEzD,CAEA,SAASA,EAAOnK,EAAQ,CACtBpQ,EAAajG,EAAO2R,GAAW0E,CAAM,EAEjCA,GACFzR,GAAO5E,EAAOkoB,CAAE,EAChBlb,EAAS,MAAK,IAEdnG,GAAOqhB,CAAE,EACTlb,EAAS,OAAM,EAEnB,CAEA,SAASxC,GAAU,CACjB3E,GAAgB7F,EAAO,CAAC0R,GAAWE,GAAaD,EAAS,CAAC,EAC1D9K,GAAOqhB,CAAE,CACX,CAEA,SAAS7E,EAAQJ,EAAU,CACrBtD,GACF1Z,EAAajG,EAAO0R,GAAWuR,EAAW,MAAQ,QAAQ,CAE9D,CAEA,MAAO,CACL,MAAOpO,EACP,QAASwO,EACT,QAAS7Y,CACb,CACA,CAEA,IAAI2d,GAAqC,OAAO,OAAO,CACrD,UAAW,KACX,MAAO1Z,GACP,UAAW8B,GACX,SAAU2D,GACV,OAAQgD,GACR,OAAQqB,GACR,OAAQ6B,GACR,KAAMa,GACN,WAAYpD,GACZ,OAAQuH,GACR,SAAUgB,GACV,MAAOa,GACP,OAAQS,GACR,KAAMa,GACN,SAAU4C,GACV,SAAUQ,GACV,WAAYY,GACZ,KAAMY,GACN,MAAOG,GACP,KAAMU,EACR,CAAC,EACGI,GAAO,CACT,KAAM,iBACN,KAAM,aACN,MAAO,oBACP,KAAM,mBACN,OAAQ,iBACR,MAAO,gBACP,KAAM,iBACN,MAAO,iBACP,SAAU,WACV,MAAO,QACP,OAAQ,yBACR,WAAY,UACd,EACIC,GAAW,CACb,KAAM,QACN,KAAM,SACN,MAAO,IACP,QAAS,EACT,YAAa,GACb,OAAQ,GACR,WAAY,GACZ,mBAAoB,GACpB,SAAU,IACV,aAAc,GACd,aAAc,GACd,cAAe,GACf,OAAQ,gCACR,KAAM,GACN,UAAW,MACX,UAAW,GACX,eAAgB,6CAChB,KAAM,GACN,QAAS3U,GACT,KAAM0U,GACN,cAAe,CACb,MAAO,EACP,YAAa,EACb,SAAU,OACd,CACA,EAEA,SAASE,GAAK5b,EAASgC,EAAapR,EAAS,CAC3C,IAAI4Z,EAASxI,EAAY,OAEzB,SAASmG,GAAQ,CACfpI,EAAeC,CAAO,EAAE,GAAG,CAACjC,GAAeS,CAAa,EAAG0J,CAAI,CACjE,CAEA,SAASA,GAAO,CACdsC,EAAO,QAAQ,SAAU4C,EAAO,CAC9BA,EAAM,MAAM,YAAa,eAAiB,IAAMA,EAAM,MAAQ,IAAI,CACpE,CAAC,CACH,CAEA,SAAShX,EAAM4N,EAAO6X,EAAM,CAC1BrR,EAAO,MAAM,aAAc,WAAa5Z,EAAQ,MAAQ,MAAQA,EAAQ,MAAM,EAC9E4F,GAASqlB,CAAI,CACf,CAEA,MAAO,CACL,MAAO1T,EACP,MAAO/R,EACP,OAAQK,EACZ,CACA,CAEA,SAAS2W,GAAMpN,EAASgC,EAAapR,EAAS,CAC5C,IAAI2d,EAAOvM,EAAY,KACnBmJ,EAAanJ,EAAY,WACzBgT,EAAShT,EAAY,OACrBzO,EAAOyO,EAAY,SAAS,KAC5B8Z,EAAaxlB,EAAMqD,GAAOpG,EAAM,YAAY,EAC5CwoB,EAEJ,SAAS5T,GAAQ,CACfpI,EAAeC,CAAO,EAAE,KAAKzM,EAAM,gBAAiB,SAAUgH,EAAG,CAC3DA,EAAE,SAAWhH,GAAQwoB,IACvB5a,EAAM,EACN4a,EAAW,EAEf,CAAC,CACH,CAEA,SAAS3lB,EAAM4N,EAAO6X,EAAM,CAC1B,IAAIpM,EAAclB,EAAK,WAAWvK,EAAO,EAAI,EACzCuL,EAAWhB,EAAK,YAAW,EAC3ByN,EAAQC,EAASjY,CAAK,EAEtBzI,EAAIkU,EAAcF,CAAQ,GAAK,GAAKyM,GAAS,EAC3CprB,EAAQ,UACVokB,EAAO,OAAOvF,EAAauM,EAAO,GAAOH,CAAI,GAE7CC,EAAW,aAAeE,EAAQ,MAAQprB,EAAQ,MAAM,EACxD2d,EAAK,UAAUkB,EAAa,EAAI,EAChCsM,EAAcF,IAGhBtN,EAAK,KAAKvK,CAAK,EACf6X,EAAI,EAER,CAEA,SAAS1a,GAAS,CAChB2a,EAAW,EAAE,EACb9G,EAAO,OAAM,CACf,CAEA,SAASiH,EAASjY,EAAO,CACvB,IAAIkY,EAActrB,EAAQ,YAE1B,GAAIoP,EAAQ,GAAGuI,EAAK,GAAK2T,EAAa,CACpC,IAAIjN,EAAO9D,EAAW,SAAS,EAAI,EAC/B9U,EAAM8U,EAAW,OAAM,EAE3B,GAAI8D,IAAS,GAAKjL,GAAS3N,GAAO4Y,GAAQ5Y,GAAO2N,IAAU,EACzD,OAAOkY,CAEX,CAEA,OAAOtrB,EAAQ,KACjB,CAEA,MAAO,CACL,MAAOuX,EACP,MAAO/R,EACP,OAAQ+K,CACZ,CACA,CAEA,IAAIgb,IAAuB,UAAY,CACrC,SAASA,EAAQpnB,EAAQnE,EAAS,CAChC,KAAK,MAAQmP,EAAc,EAC3B,KAAK,WAAa,CAAA,EAClB,KAAK,MAAQyB,GAAMhM,EAAO,EAC1B,KAAK,QAAU,CAAA,EACf,KAAK,GAAK,CAAA,EACV,KAAK,GAAK,CAAA,EACV,IAAIkS,EAAOxQ,GAASnC,CAAM,EAAI0F,GAAM,SAAU1F,CAAM,EAAIA,EACxDiG,GAAO0M,EAAMA,EAAO,cAAc,EAClC,KAAK,KAAOA,EACZ9W,EAAUoI,GAAM,CACd,MAAOgB,GAAa0N,EAAM/C,EAAU,GAAK,GACzC,WAAY3K,GAAa0N,EAAM9C,EAAe,GAAK,EACzD,EAAO+W,GAAUQ,EAAQ,SAAUvrB,GAAW,CAAA,CAAE,EAE5C,GAAI,CACFoI,GAAMpI,EAAS,KAAK,MAAMoJ,GAAa0N,EAAM3M,EAAc,CAAC,CAAC,CAC/D,MAAY,CACVC,GAAO,GAAO,cAAc,CAC9B,CAEA,KAAK,GAAK,OAAO,OAAOhC,GAAM,CAAA,EAAIpI,CAAO,CAAC,CAC5C,CAEA,IAAIwrB,EAASD,EAAQ,UAErB,OAAAC,EAAO,MAAQ,SAAeC,EAAYzN,EAAY,CACpD,IAAI0N,EAAQ,KAER5a,EAAQ,KAAK,MACbM,EAAc,KAAK,WACvBhH,GAAO0G,EAAM,GAAG,CAAClM,GAASM,EAAS,CAAC,EAAG,kBAAkB,EACzD4L,EAAM,IAAIlM,EAAO,EACjB,KAAK,GAAKwM,EACV,KAAK,GAAK4M,GAAc,KAAK,KAAO,KAAK,GAAGnG,EAAI,EAAImT,GAAOxO,IAC3D,KAAK,GAAKiP,GAAc,KAAK,GAC7B,IAAIE,EAAezjB,GAAO,CAAA,EAAI2iB,GAAuB,KAAK,GAAI,CAC5D,WAAY,KAAK,EACvB,CAAK,EACD,OAAA9iB,GAAO4jB,EAAc,SAAUC,EAAWzrB,EAAK,CAC7C,IAAI0rB,EAAYD,EAAUF,EAAOta,EAAasa,EAAM,EAAE,EACtDta,EAAYjR,CAAG,EAAI0rB,EACnBA,EAAU,OAASA,EAAU,MAAK,CACpC,CAAC,EACD9jB,GAAOqJ,EAAa,SAAUya,EAAW,CACvCA,EAAU,OAASA,EAAU,MAAK,CACpC,CAAC,EACD,KAAK,KAAK1e,EAAa,EACvB9F,GAAS,KAAK,KAAMsO,EAAiB,EACrC7E,EAAM,IAAIhM,EAAI,EACd,KAAK,KAAKsI,EAAW,EACd,IACT,EAEAoe,EAAO,KAAO,SAAchpB,EAAQ,CAClC,YAAK,QAAQ,KAAK,CAChB,OAAQA,CACd,CAAK,EACDA,EAAO,QAAQ,KAAK,CAClB,OAAQ,KACR,SAAU,EAChB,CAAK,EAEG,KAAK,MAAM,GAAGsC,EAAI,IACpB,KAAK,GAAG,KAAK,QAAO,EAEpBtC,EAAO,WAAW,KAAK,QAAO,GAGzB,IACT,EAEAgpB,EAAO,GAAK,SAAY7K,EAAS,CAC/B,YAAK,GAAG,WAAW,GAAGA,CAAO,EAEtB,IACT,EAEA6K,EAAO,GAAK,SAAYrf,EAAQC,EAAU,CACxC,YAAK,MAAM,GAAGD,EAAQC,CAAQ,EACvB,IACT,EAEAof,EAAO,IAAM,SAAarf,EAAQ,CAChC,YAAK,MAAM,IAAIA,CAAM,EACd,IACT,EAEAqf,EAAO,KAAO,SAAclf,EAAO,CACjC,IAAIwf,EAEJ,OAACA,EAAc,KAAK,OAAO,KAAK,MAAMA,EAAa,CAACxf,CAAK,EAAE,OAAOhH,GAAM,UAAW,CAAC,CAAC,CAAC,EAE/E,IACT,EAEAkmB,EAAO,IAAM,SAAavU,EAAQ7D,EAAO,CACvC,YAAK,GAAG,OAAO,IAAI6D,EAAQ7D,CAAK,EAEzB,IACT,EAEAoY,EAAO,OAAS,SAAgB7Q,EAAS,CACvC,YAAK,GAAG,OAAO,OAAOA,CAAO,EAEtB,IACT,EAEA6Q,EAAO,GAAK,SAAYxlB,EAAM,CAC5B,OAAO,KAAK,GAAG,OAASA,CAC1B,EAEAwlB,EAAO,QAAU,UAAmB,CAClC,YAAK,KAAK5d,CAAa,EAChB,IACT,EAEA4d,EAAO,QAAU,SAAiB5Z,EAAY,CACxCA,IAAe,SACjBA,EAAa,IAGf,IAAItF,EAAQ,KAAK,MACbwE,EAAQ,KAAK,MAEjB,OAAIA,EAAM,GAAGlM,EAAO,EAClBuK,EAAe,IAAI,EAAE,GAAG/B,GAAa,KAAK,QAAQ,KAAK,KAAMwE,CAAU,CAAC,GAExE7J,GAAO,KAAK,GAAI,SAAU8jB,EAAW,CACnCA,EAAU,SAAWA,EAAU,QAAQja,CAAU,CACnD,EAAG,EAAI,EACPtF,EAAM,KAAKgC,EAAa,EACxBhC,EAAM,QAAO,EACbsF,GAAcxM,GAAM,KAAK,OAAO,EAChC0L,EAAM,IAAI5L,EAAS,GAGd,IACT,EAEAX,GAAagnB,EAAS,CAAC,CACrB,IAAK,UACL,IAAK,UAAe,CAClB,OAAO,KAAK,EACd,EACA,IAAK,SAAavrB,EAAS,CACzB,KAAK,GAAG,MAAM,IAAIA,EAAS,GAAM,EAAI,CACvC,CACJ,EAAK,CACD,IAAK,SACL,IAAK,UAAe,CAClB,OAAO,KAAK,GAAG,OAAO,UAAU,EAAI,CACtC,CACJ,EAAK,CACD,IAAK,QACL,IAAK,UAAe,CAClB,OAAO,KAAK,GAAG,WAAW,SAAQ,CACpC,CACJ,CAAG,CAAC,EAEKurB,CACT,GAAC,EAEGQ,GAASR,GACbQ,GAAO,SAAW,CAAA,EAClBA,GAAO,OAAS5mB,GCpkGT,MAAM6mB,EAA0D,CAAhE,aAAA,CAEL,KAAQ,YAAc,EAAA,CAKtB,WAAWtqB,EAAsD,CAC/D,GAAI,CAEF,YAAK,QAAA,EAGL,KAAK,eAAiB,IAAIqqB,GAAO,IAAI7qB,EAAc,OAAO,IAAI,WAAW,GAAIQ,CAAM,EACnF,KAAK,eAAe,MAAA,EACpB,KAAK,YAAc,GAGf,KAAK,gBAAkBR,EAAc,MAAQ,eAC/C,KAAK,uBAAA,EAGA,KAAK,cACd,MAAQ,CAEN,MACF,CACF,CAKA,SAAgB,CACd,GAAI,KAAK,gBAAkB,CAAC,KAAK,YAC/B,GAAI,CACF,KAAK,eAAe,QAAA,CACtB,MAAQ,CAER,QAAA,CACE,OAAO,KAAK,eACZ,KAAK,YAAc,EACrB,CAEJ,CAKA,eAAyB,CACvB,OAAO,KAAK,iBAAmB,MAAQ,CAAC,KAAK,WAC/C,CAKA,aAA0C,CACxC,GAAI,KAAK,gBACP,OAAO,KAAK,cAGhB,CAKA,aAAaQ,EAAiD,CAC5D,YAAK,QAAA,EACE,KAAK,WAAWA,CAAM,CAC/B,CAKA,sBAAgC,CAC9B,GAAI,CAEF,OADkB,SAAS,cAAc,IAAIR,EAAc,OAAO,IAAI,WAAW,EAAE,IAC9D,IACvB,MAAQ,CACN,MAAO,EACT,CACF,CAKA,oBAAoBQ,EAA0BuqB,EAAQnrB,GAAO,eAAsB,CACjF,WAAW,IAAM,CACX,KAAK,wBACP,KAAK,WAAWY,CAAM,CAE1B,EAAGuqB,CAAK,CACV,CAKA,WAKE,CACA,MAAO,CACL,cAAe,KAAK,cAAA,EACpB,YAAa,KAAK,YAClB,YAAa,KAAK,iBAAmB,KACrC,mBAAoB,KAAK,qBAAA,CAAqB,CAElD,CAKQ,wBAA+B,CAChC,KAAK,gBAKV,KAAK,eAAe,GAAG,QAAS,CAACC,EAAkB7L,IAAsB,CAEvE,GAAInf,EAAc,MAAQ,cAAe,CAEvC,IAAI8Q,EAAY,UACZka,GAAY7L,IACdrO,EAAY,YAGb,WAAqD,cAAgB,CACpE,SAAU,CAAE,KAAMqO,EAAW,GAAI6L,EAAU,UAAAla,CAAA,EAC3C,UAAW,KAAK,IAAA,CAAI,CAExB,CACF,CAAC,CACH,CACF,CCvIO,MAAMma,EAAiB,CAO1B,aAAc,CAFd,KAAiB,UAAYjrB,EAAc,OAAO,UAG9C,KAAK,cAAgB,IAAIE,GACzB,KAAK,WAAa,IAAIgB,GACtB,KAAK,YAAc,IAAIqB,GACvB,KAAK,iBAAmB,IAAIuoB,EAChC,CAKA,sBAAsBI,EAA0B,CAC5C,GAAI,CACA,KAAK,QAAA,EAGL,MAAMC,EAAY,KAAK,YAAY,eAAA,EACnC,GAAIA,EAAU,SAAWxrB,GAAgB,aACrC,OAIJ,MAAMwB,EAAY,KAAK,WAAW,gBAAA,EAC5BG,EAAS,KAAK,WAAW,oBAAoBH,CAAS,EACtDM,EAAO,KAAK,WAAW,kBAAkBH,CAAM,EAG/CnB,EAAa,KAAK,eAAesB,EAAM0pB,CAAS,EACtD,KAAK,WAAW,iBAAiB7pB,CAAM,EAKvC,KAAK,WAAW,YAAYH,CAAS,EAGrC,WAAW,IAAM,CACb,KAAK,iBAAiBhB,CAAU,CACpC,EAAG,KAAK,SAAS,CACrB,MAAQ,CAER,CACJ,CAKQ,eAAe2gB,EAAsBqK,EAAyC,CAClF,IAAIhrB,EAAaR,GAAgB,aAEjC,UAAWiC,KAASupB,EAChB,GAAIvpB,EAAM,QAAS,CACf,MAAMwpB,EAAe,KAAK,WAAW,YAAYxpB,EAAM,SAAUA,EAAM,SAAS,EAChFL,GAAqBuf,EAASsK,CAAY,EAC1CjrB,GAAcT,GAAoB,eACtC,CAGJ,OAAOS,CACX,CAOQ,iBAAiBA,EAA0B,CAC/C,GAAI,CACA,MAAMC,EAAiB,KAAK,YAAY,kBAAA,EAClCirB,EAAe,KAAK,cAAc,uBAAuBlrB,EAAYC,CAAc,EAIzF,GAAI,CADqB,KAAK,cAAc,sBAAsBirB,EAAa,WAAW,EACpE,QAClB,OAIJ,KAAK,iBAAiB,WAAWA,EAAa,WAAW,CAC7D,MAAQ,CAER,CACJ,CAKA,SAAgB,CACZ,KAAK,iBAAiB,QAAA,EACtB,KAAK,WAAW,QAAA,CACpB,CAEJ,CCzGO,MAAMC,EAAa,CAKd,aAAc,CAHtB,KAAQ,SAA4B,CAAA,EACpC,KAAQ,cAAgB,EAIxB,CAKA,OAAc,aAA4B,CACtC,OAAKA,GAAa,WACdA,GAAa,SAAW,IAAIA,IAEzBA,GAAa,QACxB,CAKO,YAAsB,CACzB,GAAI,CACA,OAAI,KAAK,gBAKT,KAAK,yBAAA,EACL,KAAK,cAAgB,IACd,EACX,MAAQ,CACJ,MAAO,EACX,CACJ,CAKO,WAAoBC,EAAmBC,EAAkC,CAC5E,GAAI,CACA,OAAOD,EAAA,CACX,OAASE,EAAO,CACZ,YAAK,SAASA,EAAgBD,CAAO,EAC9B,EACX,CACJ,CAKO,YAAqBD,EAA4BC,EAA2C,CAC/F,OAAOD,EAAA,EAAK,MAAOE,IACf,KAAK,SAASA,EAAgBD,CAAO,EAC9B,GACV,CACL,CAKQ,SAASC,EAAcD,EAAuB,CAClD,GAAI,CACA,MAAMva,EAAuB,CACzB,cAAe,KACf,MAAAwa,EACA,QAAAD,CAAA,EAGJ,KAAK,SAAS,KAAKva,CAAK,EAGpB,KAAK,SAAS,OAASxR,GAAe,uBACtC,KAAK,SAAS,MAAA,CAQtB,MAAQ,CAER,CACJ,CAKQ,0BAAiC,CACrC,GAAI,CAEA,WAAW,iBAAiB,qBAAuB2L,GAAU,CACzD,KAAK,SACD,IAAI,MAAM,OAAOA,EAAM,MAAM,CAAC,EAC9B,6BAAA,CAER,CAAC,CACL,MAAQ,CAER,CACJ,CAKO,aAA+B,CAClC,MAAO,CAAC,GAAG,KAAK,QAAQ,CAC5B,CAKO,eAAsB,CACzB,KAAK,SAAW,CAAA,CACpB,CACJ,CCrHO,MAAMsgB,EAAc,CAGf,aAAc,CAEtB,CAKA,OAAc,aAA6B,CACvC,OAAKA,GAAc,WACfA,GAAc,SAAW,IAAIA,IAE1BA,GAAc,QACzB,CAKO,YAAsB,CACzB,GAAI,CAEA,OADqB5oB,GAAI,QAAQ,IAAI,WAAW,IACxB,MAC5B,MAAQ,CAEJ,GAAI,CACA,OAAO,WAAW,SAAS,SAAS,SAAS,OAAO,CACxD,MAAQ,CACJ,MAAO,EACX,CACJ,CACJ,CAKO,WAAkC,CACrC,OAAO9C,CACX,CAKO,uBAAiC,CACpC,GAAI,CAIA,QAASyC,EAAa,EAAmBA,GAAczC,EAAc,OAAO,UAAWyC,GAAc,EAEjG,GADcK,GAAI,MAAM,UAAU,wCAAwCL,CAAU,EAAE,EAElF,MAAO,GAGf,MAAO,EACX,MAAQ,CACJ,MAAO,EACX,CACJ,CACJ,CCpDAK,GAAI,aAAa,IAAI9C,EAAc,IAAI,YAAa,IAAM,CACtD,MAAM2rB,EAAeL,GAAa,YAAA,EAC5BM,EAAgBF,GAAc,YAAA,EAGpC,GAAI,CAACC,EAAa,aACd,OAGJ,MAAME,EAAmB,IAAIZ,GAG7Ba,GAAAA,OAAOC,GAAc,UAAW,OAAQ,SAAoCC,EAAgB,CACxFL,EAAa,WAAW,IAAM,CACtBC,EAAc,cACdK,GAAoBD,EAAOH,CAA2B,CAE9D,EAAG,8BAA8B,CACrC,CAAC,CACL,CAAC,EAKD,MAAMI,GAAsB,CACxBD,EACAH,EACAK,IACO,CACP,GAAI,CAIA,GAHsBR,GAAc,YAAA,EAGlB,wBACd,GAAI,CACAG,EAAiB,sBAAsBG,CAAK,CAChD,MAAQ,CAER,CAMA,CAAClpB,GAAI,QAAQ,MAAQ9B,MACrBmrB,GAAA,CAGR,MAAQ,CAER,CACJ,EAOMA,GAAgB,IAAY,CAC9B,IAAIC,EAAsB,SAAS,eAAepsB,EAAc,GAAG,YAAY,EAE/E,GAAIosB,IAAwB,KAAM,CAE9B,MAAMC,EAAgBvpB,GAAI,MAAM,UAAU,+CAA+C,GAAK9C,EAAc,GAAG,cAE/GosB,EAAsB,SAAS,cAAc,KAAK,EAClDA,EAAoB,GAAKpsB,EAAc,GAAG,aAC1CosB,EAAoB,UAAY,mCAChCA,EAAoB,UAAY,aAAaC,CAAa,kDAG1D,MAAMC,EAAsB,SAAS,cAAc,yDAAyD,EACxGA,GAEAA,EAAoB,YAAYF,CAAmB,CAE3D,CACJ", "x_google_ignoreList": [7]}