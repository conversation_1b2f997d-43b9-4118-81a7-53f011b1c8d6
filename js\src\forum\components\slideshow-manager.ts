import * as DOMUtils from '../utils/dom-utils';
import { defaultConfig } from '../../common/config';
import { ARRAY_CONSTANTS, SLIDESHOW_CONSTANTS } from '../../common/config/constants';
import { SplideConfigManager } from './splide/splide-config-manager';
import { SplideDOMBuilder } from './splide/splide-dom-builder';
import { SlideDataManager } from './splide/slide-data-manager';
import { SplideLifecycleManager } from './splide/splide-lifecycle-manager';
import type { FlarumVnode, ProcessedSlideData } from '../../common/config/types';

/**
 * Slideshow manager for header advertisements
 * Coordinates between different modules to manage the complete slideshow lifecycle
 */
export class SlideshowManager {
    private readonly configManager: SplideConfigManager;
    private readonly domBuilder: SplideDOMBuilder;
    private readonly dataManager: SlideDataManager;
    private readonly lifecycleManager: SplideLifecycleManager;
    private readonly checkTime = defaultConfig.slider.checkTime;

    constructor() {
        this.configManager = new SplideConfigManager();
        this.domBuilder = new SplideDOMBuilder();
        this.dataManager = new SlideDataManager();
        this.lifecycleManager = new SplideLifecycleManager();
    }

    /**
     * Initialize and attach slideshow to the DOM
     */
    attachAdvertiseHeader(_vdom: FlarumVnode): void {
        try {
            this.destroy(); // Clean up any existing instance

            // Fetch slide data
            const slideData = this.dataManager.fetchSlideData();
            if (slideData.length === ARRAY_CONSTANTS.EMPTY_LENGTH) {
                return; // No slides to display
            }

            // Build DOM structure
            const container = this.domBuilder.createContainer();
            const splide = this.domBuilder.createSplideElement(container);
            const list = this.domBuilder.createSplideTrack(splide);

            // Populate slides
            const slideCount = this.populateSlides(list, slideData);
            this.domBuilder.createPagination(splide);
            this.domBuilder.createNavigation(splide);

            // Append to DOM
            this.domBuilder.appendToDOM(container);

            // Initialize Splide after DOM attachment
            setTimeout(() => {
                this.initializeSplide(slideCount);
            }, this.checkTime);
        } catch {
            // Silently handle slideshow creation errors
        }
    }

    /**
     * Populate slides with processed slide data
     */
    private populateSlides(wrapper: HTMLElement, slideData: ProcessedSlideData[]): number {
        let slideCount = ARRAY_CONSTANTS.EMPTY_LENGTH;

        for (const slide of slideData) {
            if (slide.isValid) {
                const slideElement = this.domBuilder.createSlide(slide.imageSrc, slide.imageLink);
                DOMUtils.appendChild(wrapper, slideElement);
                slideCount += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT;
            }
        }

        return slideCount;
    }



    /**
     * Initialize Splide with calculated configuration
     */
    private initializeSplide(slideCount: number): void {
        try {
            const transitionTime = this.dataManager.getTransitionTime();
            const configResult = this.configManager.calculateConfiguration(slideCount, transitionTime);

            // Validate configuration
            const validationResult = this.configManager.validateConfiguration(configResult.finalConfig);
            if (!validationResult.isValid) {
                return;
            }

            // Initialize Splide instance
            this.lifecycleManager.initialize(configResult.finalConfig);
        } catch {
            // Silently handle Splide initialization errors
        }
    }

    /**
     * Destroy slideshow instance
     */
    destroy(): void {
        this.lifecycleManager.destroy();
        this.domBuilder.cleanup();
    }

}
