wusong8899-header-advertisement:
  admin:
    TransitionTime: 广告轮换时间 (默认5000毫秒)
    HeaderIconUrl: 头部图标URL
    HeaderIconUrlHelp: 输入头部图标的URL地址，或在下方上传文件
    # Tag轮播配置
    TagSplideMinSlidesForLoop: 启用循环模式的最小标签数量
    TagSplideMinSlidesForLoopHelp: "当标签数量达到此值时才启用无限循环模式 (默认: 2)"
    TagSplideEnableAutoplay: 启用自动播放
    TagSplideEnableAutoplayHelp: 是否自动滚动标签轮播
    TagSplideAutoplayInterval: 自动播放间隔时间 (毫秒)
    TagSplideAutoplayIntervalHelp: "每个标签停留的时间，单位毫秒 (默认: 3000)"
    TagSplideEnableLoopMode: 启用循环模式
    TagSplideEnableLoopModeHelp: 是否允许标签轮播无限循环
    TagSplideTransitionSpeed: 过渡动画速度 (毫秒)
    TagSplideTransitionSpeedHelp: "标签切换动画的持续时间 (默认: 800)"
    TagSplideGap: 标签间距
    TagSplideGapHelp: "标签之间的视觉间距 (默认: 10px)"
    TagSplidePauseOnMouseEnter: 鼠标悬停时暂停
    TagSplidePauseOnMouseEnterHelp: 鼠标悬停在标签上时是否暂停自动播放
    TagSplideEnableGrabCursor: 显示抓取光标
    TagSplideEnableGrabCursorHelp: 是否显示可拖拽的手型光标
    TagSplideEnableFreeMode: 启用自由滚动模式
    TagSplideEnableFreeModeHelp: 当标签数量不足时启用自由滚动模式
    # 动态幻灯片设置
    SlideSettings: 广告幻灯片
    SlideSettingsHelp: 管理您的广告幻灯片。根据需要添加或删除幻灯片。
    AddSlide: 添加幻灯片
    DeleteSlide: 删除
    SlideNumber: "幻灯片 {number}"
    SlideLink: 链接地址
    SlideImage: 图片地址
  forum:
    loading: 加载中...
    error: 发生错误
    no-data: 暂无数据
