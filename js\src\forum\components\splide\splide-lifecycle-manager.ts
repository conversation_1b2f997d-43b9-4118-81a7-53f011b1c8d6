import { Splide } from '@splidejs/splide';
import { defaultConfig } from '../../../common/config';
import { TIMING } from '../../../common/config/constants';
import type {
  ISplideLifecycleManager,
  SplideInstance,
  SplideFullConfig
} from '../../../common/config/types';

/**
 * Splide lifecycle manager
 * Handles Splide instance creation, destruction, and lifecycle management
 */
export class SplideLifecycleManager implements ISplideLifecycleManager {
  private splideInstance: SplideInstance | undefined;
  private isDestroyed = false;

  /**
   * Initialize Splide instance with the provided configuration
   */
  initialize(config: SplideFullConfig): SplideInstance | undefined {
    try {
      // Destroy existing instance if any
      this.destroy();

      // Create new Splide instance
      this.splideInstance = new Splide(`.${defaultConfig.slider.dom.splideClass}`, config) as SplideInstance;
      this.splideInstance.mount();
      this.isDestroyed = false;

      return this.splideInstance;
    } catch {
      // Silently handle initialization errors
      return;
    }
  }

  /**
   * Destroy the current Splide instance
   */
  destroy(): void {
    if (this.splideInstance && !this.isDestroyed) {
      try {
        this.splideInstance.destroy();
      } catch {
        // Silently handle destruction errors
      } finally {
        delete this.splideInstance;
        this.isDestroyed = true;
      }
    }
  }

  /**
   * Check if Splide instance is initialized and not destroyed
   */
  isInitialized(): boolean {
    return this.splideInstance !== null && !this.isDestroyed;
  }

  /**
   * Get the current Splide instance
   */
  getInstance(): SplideInstance | undefined {
    if (this.isInitialized()) {
      return this.splideInstance;
    }
    return;
  }

  /**
   * Reinitialize Splide with new configuration
   */
  reinitialize(config: SplideFullConfig): SplideInstance | null {
    this.destroy();
    return this.initialize(config);
  }

  /**
   * Check if the Splide container exists in the DOM
   */
  isContainerAvailable(): boolean {
    try {
      const container = document.querySelector(`.${defaultConfig.slider.dom.splideClass}`);
      return container !== null;
    } catch {
      return false;
    }
  }

  /**
   * Initialize with delay to wait for DOM readiness
   */
  initializeWithDelay(config: SplideFullConfig, delay = TIMING.CHECK_INTERVAL): void {
    setTimeout(() => {
      if (this.isContainerAvailable()) {
        this.initialize(config);
      }
    }, delay);
  }

  /**
   * Get Splide instance status information
   */
  getStatus(): {
    isInitialized: boolean;
    isDestroyed: boolean;
    hasInstance: boolean;
    containerAvailable: boolean;
  } {
    return {
      isInitialized: this.isInitialized(),
      isDestroyed: this.isDestroyed,
      hasInstance: this.splideInstance !== null,
      containerAvailable: this.isContainerAvailable()
    };
  }
}
