(function(er,ye,tr){"use strict";const an=t=>{try{return document.querySelector(t)||!1}catch{return!1}},nr=t=>{try{return document.querySelectorAll(t)}catch{return document.querySelectorAll("")}},he=(t,n={},e="")=>{try{const r=document.createElement(t);for(const[i,o]of Object.entries(n))i==="className"?r.className=String(o):i==="id"?r.id=String(o):r.setAttribute(i,String(o));return e&&(r.innerHTML=e),r}catch{return document.createElement("div")}},Ne=(t,n)=>{try{t.appendChild(n)}catch{}},rr=(t,n)=>{try{t.prepend(n)}catch{}},Pt=t=>{try{t.remove()}catch{}},on={USER_AGENT_SUBSTR_START:0,USER_AGENT_SUBSTR_LENGTH:4},ir={MAX_ERROR_LOG_ENTRIES:50,DOM_READY_TIMEOUT:5e3,SLIDE_NUMBER_MIN:1,SLIDE_NUMBER_MAX:30,TRANSITION_TIME_MIN:1e3,TRANSITION_TIME_MAX:3e4,CONFIG_MAX_SLIDES_MIN:1,CONFIG_MAX_SLIDES_MAX:50},ft={SLIDE_INCREMENT:1,INITIAL_SLIDE_INDEX:1},te={EMPTY_LENGTH:0,FIRST_INDEX:0,NOT_FOUND_INDEX:-1,NEXT_ITEM_OFFSET:1,LAST_ITEM_OFFSET:-1},dt={CHECK_INTERVAL:10,DATA_CHECK_INTERVAL:100,DEFAULT_TRANSITION_TIME:5e3},sn={SPLIDE_AD_CONTAINER_ID:"splideAdContainer",HEADER_ICON_ID:"wusong8899HeaderAdvIcon"},ar={AD_SPLIDE:"adSplide"},vt={ID:"wusong8899-header-advertisement",TRANSLATION_PREFIX:"wusong8899-header-advertisement",MAX_SLIDES:30,HEADER_ICON_URL:"https://ex.cc/assets/files/date/test.png"},W={env:"production",app:{extensionId:vt.ID,translationPrefix:vt.TRANSLATION_PREFIX},slider:{maxSlides:vt.MAX_SLIDES,defaultTransitionTime:dt.DEFAULT_TRANSITION_TIME,checkTime:dt.CHECK_INTERVAL,dataCheckInterval:dt.DATA_CHECK_INTERVAL,dom:{containerId:sn.SPLIDE_AD_CONTAINER_ID,splideClass:ar.AD_SPLIDE},splide:{gap:"15px",type:"loop",focus:"center",perPage:1,pagination:!0,arrows:!0}},ui:{headerIconId:sn.HEADER_ICON_ID,headerIconUrl:vt.HEADER_ICON_URL}},Le={MIN_SLIDES_FOR_LOOP:2,MOBILE_PER_PAGE:1,TABLET_PER_PAGE:1,DESKTOP_PER_PAGE:1};class or{calculateConfiguration(n,e){const r=this.shouldEnableLoop(n),i=this.calculateResponsiveConfig(),o=this.buildFinalConfig(e,r);return{enableLoop:r,responsiveConfig:i,finalConfig:o}}validateConfiguration(n){const e=[];if(n.autoplay&&typeof n.autoplay=="object"&&(typeof n.autoplay.interval!="number"||n.autoplay.interval<=te.EMPTY_LENGTH)&&e.push("Invalid autoplay interval configuration"),(typeof n.gap!="string"||!n.gap.match(/^\d+px$/))&&e.push('Invalid gap configuration - must be in format "Npx"'),(typeof n.perPage!="number"||n.perPage<=te.EMPTY_LENGTH)&&e.push("Invalid perPage configuration"),n.breakpoints&&typeof n.breakpoints=="object")for(const[r,i]of Object.entries(n.breakpoints)){const o=Number(r);(Number.isNaN(o)||o<=te.EMPTY_LENGTH)&&e.push(`Invalid breakpoint width: ${r}`),(typeof i.perPage!="number"||i.perPage<=te.EMPTY_LENGTH)&&e.push(`Invalid perPage for breakpoint ${r}`),(typeof i.gap!="string"||!i.gap.match(/^\d+px$/))&&e.push(`Invalid gap for breakpoint ${r}`)}return{isValid:e.length===te.EMPTY_LENGTH,errors:e}}shouldEnableLoop(n){return n>=Le.MIN_SLIDES_FOR_LOOP}calculateResponsiveConfig(){return{mobile:{perPage:Le.MOBILE_PER_PAGE,gap:"10px"},tablet:{perPage:Le.TABLET_PER_PAGE,gap:"12px"},desktop:{perPage:Le.DESKTOP_PER_PAGE,gap:W.slider.splide.gap}}}buildFinalConfig(n,e){const r={320:{perPage:Le.MOBILE_PER_PAGE,gap:"10px"},768:{perPage:Le.TABLET_PER_PAGE,gap:"12px"},1024:{perPage:Le.DESKTOP_PER_PAGE,gap:W.slider.splide.gap}};let i="slide";e&&(i="loop");let o=!1;return n>te.EMPTY_LENGTH&&(o={interval:n,pauseOnHover:!0}),{type:i,autoplay:o,gap:W.slider.splide.gap,focus:W.slider.splide.focus,perPage:W.slider.splide.perPage,breakpoints:r,pagination:W.slider.splide.pagination,arrows:W.slider.splide.arrows,speed:600}}}const un=()=>{try{const{userAgent:t}=navigator;return t.substring(on.USER_AGENT_SUBSTR_START,on.USER_AGENT_SUBSTR_LENGTH)==="Mobi"}catch{return!1}};class sr{createContainer(){this.removeExistingNavigation();const n=he("div",{id:W.slider.dom.containerId,className:"adContainer adContainer--forced"});return this.container=n,n}createSplideElement(n){let e=`splide ${W.slider.dom.splideClass} adSplide--forced`;un()&&(e+=" adSplide--mobile");const r=he("div",{className:e});return Ne(n,r),r}createSplideTrack(n){const e=he("div",{className:"splide__track splide__track--forced"}),r=he("ul",{className:"splide__list splide__list--forced"});return Ne(e,r),Ne(n,e),r}createSlide(n,e){const r=he("li",{className:"splide__slide splide__slide--forced"});let i="";return e&&(i=`window.location.href="${e}"`),r.innerHTML=`<img onclick='${i}' src='${n}' class='splide__slide__image' />`,r}createPagination(n){const e=he("ul",{className:"splide__pagination"});Ne(n,e)}createNavigation(n){const e=he("button",{className:"splide__arrow splide__arrow--prev splide__arrow--prev--custom"}),r=he("button",{className:"splide__arrow splide__arrow--next splide__arrow--next--custom"});Ne(n,e),Ne(n,r)}appendToDOM(n){const e=an("#content .container");e&&rr(e,n)}cleanup(){this.container&&(Pt(this.container),delete this.container)}getContainer(){return this.container}removeExistingNavigation(){const n=an(`#${W.slider.dom.containerId}`);n&&Pt(n);const e=nr(".item-nav");for(const r of e)Pt(r)}}class ur{constructor(){this.maxSlides=W.slider.maxSlides}fetchSlideData(){const n=[];for(let e=ft.INITIAL_SLIDE_INDEX;e<=this.maxSlides;e+=ft.SLIDE_INCREMENT){const r=this.getForumAttribute(`wusong8899-header-advertisement.Image${e}`),i=this.getForumAttribute(`wusong8899-header-advertisement.Link${e}`);r&&n.push({imageSrc:String(r),imageLink:String(i||""),slideIndex:e})}return this.processSlideData(n)}validateSlideData(n){const e=[];if(!Array.isArray(n))return e.push("Slide data must be an array"),{isValid:!1,errors:e};if(n.length===te.EMPTY_LENGTH)return e.push("No slide data provided"),{isValid:!1,errors:e};for(const r of n)(!r.imageSrc||typeof r.imageSrc!="string")&&e.push(`Invalid image source for slide ${r.slideIndex}`),r.imageLink&&typeof r.imageLink!="string"&&e.push(`Invalid image link for slide ${r.slideIndex}`),(typeof r.slideIndex!="number"||r.slideIndex<ft.INITIAL_SLIDE_INDEX)&&e.push(`Invalid slide index: ${r.slideIndex}`),r.imageSrc&&!this.isValidUrl(r.imageSrc)&&e.push(`Invalid image URL format for slide ${r.slideIndex}`),r.imageLink&&!this.isValidUrl(r.imageLink)&&e.push(`Invalid link URL format for slide ${r.slideIndex}`);return{isValid:e.length===te.EMPTY_LENGTH,errors:e}}getTransitionTime(){const n=this.getForumAttribute("wusong8899-header-advertisement.TransitionTime");if(n){const e=Number.parseInt(String(n),10);if(!Number.isNaN(e)&&e>te.EMPTY_LENGTH)return e}return W.slider.defaultTransitionTime}processSlideData(n){return n.map(e=>({imageSrc:e.imageSrc,imageLink:e.imageLink,slideIndex:e.slideIndex,isValid:this.isSlideValid(e)})).filter(e=>e.isValid)}isSlideValid(n){return!!(n.imageSrc&&typeof n.imageSrc=="string"&&this.isValidUrl(n.imageSrc)&&typeof n.slideIndex=="number"&&n.slideIndex>te.EMPTY_LENGTH)}isValidUrl(n){try{return!!new URL(n)}catch{return n.startsWith("/")||n.startsWith("./")||n.startsWith("data:")}}getForumAttribute(n){try{const e=ye&&ye.forum,r=e&&e.attribute;return typeof r=="function"?r.call(e,n):void 0}catch{return}}}function cr(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function lr(t,n,e){return n&&cr(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),t}/*!
 * Splide.js
 * Version  : 4.1.4
 * License  : MIT
 * Copyright: 2022 Naotoshi Fujita
 */var cn="(prefers-reduced-motion: reduce)",Me=1,fr=2,xe=3,Ve=4,We=5,Et=6,gt=7,dr={CREATED:Me,MOUNTED:fr,IDLE:xe,MOVING:Ve,SCROLLING:We,DRAGGING:Et,DESTROYED:gt};function fe(t){t.length=0}function me(t,n,e){return Array.prototype.slice.call(t,n,e)}function U(t){return t.bind.apply(t,[null].concat(me(arguments,1)))}var ln=setTimeout,wt=function(){};function fn(t){return requestAnimationFrame(t)}function ht(t,n){return typeof n===t}function Ye(t){return!xt(t)&&ht("object",t)}var Mt=Array.isArray,dn=U(ht,"function"),_e=U(ht,"string"),$e=U(ht,"undefined");function xt(t){return t===null}function vn(t){try{return t instanceof(t.ownerDocument.defaultView||window).HTMLElement}catch{return!1}}function Ke(t){return Mt(t)?t:[t]}function ne(t,n){Ke(t).forEach(n)}function Vt(t,n){return t.indexOf(n)>-1}function mt(t,n){return t.push.apply(t,Ke(n)),t}function de(t,n,e){t&&ne(n,function(r){r&&t.classList[e?"add":"remove"](r)})}function ue(t,n){de(t,_e(n)?n.split(" "):n,!0)}function qe(t,n){ne(n,t.appendChild.bind(t))}function Gt(t,n){ne(t,function(e){var r=(n||e).parentNode;r&&r.insertBefore(e,n)})}function je(t,n){return vn(t)&&(t.msMatchesSelector||t.matches).call(t,n)}function En(t,n){var e=t?me(t.children):[];return n?e.filter(function(r){return je(r,n)}):e}function Ze(t,n){return n?En(t,n)[0]:t.firstElementChild}var Je=Object.keys;function Re(t,n,e){return t&&(e?Je(t).reverse():Je(t)).forEach(function(r){r!=="__proto__"&&n(t[r],r)}),t}function Qe(t){return me(arguments,1).forEach(function(n){Re(n,function(e,r){t[r]=n[r]})}),t}function Ie(t){return me(arguments,1).forEach(function(n){Re(n,function(e,r){Mt(e)?t[r]=e.slice():Ye(e)?t[r]=Ie({},Ye(t[r])?t[r]:{},e):t[r]=e})}),t}function gn(t,n){ne(n||Je(t),function(e){delete t[e]})}function ce(t,n){ne(t,function(e){ne(n,function(r){e&&e.removeAttribute(r)})})}function M(t,n,e){Ye(n)?Re(n,function(r,i){M(t,i,r)}):ne(t,function(r){xt(e)||e===""?ce(r,n):r.setAttribute(n,String(e))})}function Ge(t,n,e){var r=document.createElement(t);return n&&(_e(n)?ue(r,n):M(r,n)),e&&qe(e,r),r}function ae(t,n,e){if($e(e))return getComputedStyle(t)[n];xt(e)||(t.style[n]=""+e)}function et(t,n){ae(t,"display",n)}function hn(t){t.setActive&&t.setActive()||t.focus({preventScroll:!0})}function oe(t,n){return t.getAttribute(n)}function mn(t,n){return t&&t.classList.contains(n)}function re(t){return t.getBoundingClientRect()}function De(t){ne(t,function(n){n&&n.parentNode&&n.parentNode.removeChild(n)})}function _n(t){return Ze(new DOMParser().parseFromString(t,"text/html").body)}function ve(t,n){t.preventDefault(),n&&(t.stopPropagation(),t.stopImmediatePropagation())}function In(t,n){return t&&t.querySelector(n)}function kt(t,n){return n?me(t.querySelectorAll(n)):[]}function Ee(t,n){de(t,n,!1)}function Ft(t){return t.timeStamp}function Ce(t){return _e(t)?t:t?t+"px":""}var tt="splide",Ut="data-"+tt;function nt(t,n){if(!t)throw new Error("["+tt+"] "+(n||""))}var Te=Math.min,_t=Math.max,It=Math.floor,rt=Math.ceil,J=Math.abs;function Tn(t,n,e){return J(t-n)<e}function Tt(t,n,e,r){var i=Te(n,e),o=_t(n,e);return r?i<t&&t<o:i<=t&&t<=o}function ke(t,n,e){var r=Te(n,e),i=_t(n,e);return Te(_t(r,t),i)}function Ht(t){return+(t>0)-+(t<0)}function Bt(t,n){return ne(n,function(e){t=t.replace("%s",""+e)}),t}function zt(t){return t<10?"0"+t:""+t}var Sn={};function vr(t){return""+t+zt(Sn[t]=(Sn[t]||0)+1)}function An(){var t=[];function n(a,c,l,d){i(a,c,function(s,m,v){var E="addEventListener"in s,u=E?s.removeEventListener.bind(s,m,l,d):s.removeListener.bind(s,l);E?s.addEventListener(m,l,d):s.addListener(l),t.push([s,m,v,l,u])})}function e(a,c,l){i(a,c,function(d,s,m){t=t.filter(function(v){return v[0]===d&&v[1]===s&&v[2]===m&&(!l||v[3]===l)?(v[4](),!1):!0})})}function r(a,c,l){var d,s=!0;return typeof CustomEvent=="function"?d=new CustomEvent(c,{bubbles:s,detail:l}):(d=document.createEvent("CustomEvent"),d.initCustomEvent(c,s,!1,l)),a.dispatchEvent(d),d}function i(a,c,l){ne(a,function(d){d&&ne(c,function(s){s.split(" ").forEach(function(m){var v=m.split(".");l(d,v[0],v[1])})})})}function o(){t.forEach(function(a){a[4]()}),fe(t)}return{bind:n,unbind:e,dispatch:r,destroy:o}}var be="mounted",pn="ready",Se="move",it="moved",yn="click",Er="active",gr="inactive",hr="visible",mr="hidden",K="refresh",Q="updated",at="resize",Xt="resized",_r="drag",Ir="dragging",Tr="dragged",Wt="scroll",Fe="scrolled",Sr="overflow",Nn="destroy",Ar="arrows:mounted",pr="arrows:updated",yr="pagination:mounted",Nr="pagination:updated",Ln="navigation:mounted",Rn="autoplay:play",Lr="autoplay:playing",Dn="autoplay:pause",Cn="lazyload:loaded",bn="sk",On="sh",St="ei";function z(t){var n=t?t.event.bus:document.createDocumentFragment(),e=An();function r(o,a){e.bind(n,Ke(o).join(" "),function(c){a.apply(a,Mt(c.detail)?c.detail:[])})}function i(o){e.dispatch(n,o,me(arguments,1))}return t&&t.event.on(Nn,e.destroy),Qe(e,{bus:n,on:r,off:U(e.unbind,n),emit:i})}function At(t,n,e,r){var i=Date.now,o,a=0,c,l=!0,d=0;function s(){if(!l){if(a=t?Te((i()-o)/t,1):1,e&&e(a),a>=1&&(n(),o=i(),r&&++d>=r))return v();c=fn(s)}}function m(_){_||u(),o=i()-(_?a*t:0),l=!1,c=fn(s)}function v(){l=!0}function E(){o=i(),a=0,e&&e(a)}function u(){c&&cancelAnimationFrame(c),a=0,c=0,l=!0}function f(_){t=_}function I(){return l}return{start:m,rewind:E,pause:v,cancel:u,set:f,isPaused:I}}function Rr(t){var n=t;function e(i){n=i}function r(i){return Vt(Ke(i),n)}return{set:e,is:r}}function Dr(t,n){var e=At(0,t,null,1);return function(){e.isPaused()&&e.start()}}function Cr(t,n,e){var r=t.state,i=e.breakpoints||{},o=e.reducedMotion||{},a=An(),c=[];function l(){var u=e.mediaQuery==="min";Je(i).sort(function(f,I){return u?+f-+I:+I-+f}).forEach(function(f){s(i[f],"("+(u?"min":"max")+"-width:"+f+"px)")}),s(o,cn),m()}function d(u){u&&a.destroy()}function s(u,f){var I=matchMedia(f);a.bind(I,"change",m),c.push([u,I])}function m(){var u=r.is(gt),f=e.direction,I=c.reduce(function(_,h){return Ie(_,h[1].matches?h[0]:{})},{});gn(e),E(I),e.destroy?t.destroy(e.destroy==="completely"):u?(d(!0),t.mount()):f!==e.direction&&t.refresh()}function v(u){matchMedia(cn).matches&&(u?Ie(e,o):gn(e,Je(o)))}function E(u,f,I){Ie(e,u),f&&Ie(Object.getPrototypeOf(e),u),(I||!r.is(Me))&&t.emit(Q,e)}return{setup:l,destroy:d,reduce:v,set:E}}var pt="Arrow",yt=pt+"Left",Nt=pt+"Right",Pn=pt+"Up",wn=pt+"Down",Mn="rtl",Lt="ttb",Yt={width:["height"],left:["top","right"],right:["bottom","left"],x:["y"],X:["Y"],Y:["X"],ArrowLeft:[Pn,Nt],ArrowRight:[wn,yt]};function br(t,n,e){function r(o,a,c){c=c||e.direction;var l=c===Mn&&!a?1:c===Lt?0:-1;return Yt[o]&&Yt[o][l]||o.replace(/width|left|right/i,function(d,s){var m=Yt[d.toLowerCase()][l]||d;return s>0?m.charAt(0).toUpperCase()+m.slice(1):m})}function i(o){return o*(e.direction===Mn?1:-1)}return{resolve:r,orient:i}}var ge="role",Ue="tabindex",Or="disabled",se="aria-",ot=se+"controls",xn=se+"current",Vn=se+"selected",ie=se+"label",$t=se+"labelledby",Gn=se+"hidden",Kt=se+"orientation",st=se+"roledescription",kn=se+"live",Fn=se+"busy",Un=se+"atomic",qt=[ge,Ue,Or,ot,xn,ie,$t,Gn,Kt,st],le=tt+"__",Ae="is-",jt=tt,Hn=le+"track",Pr=le+"list",Rt=le+"slide",Bn=Rt+"--clone",wr=Rt+"__container",Zt=le+"arrows",Dt=le+"arrow",zn=Dt+"--prev",Xn=Dt+"--next",Ct=le+"pagination",Wn=Ct+"__page",Mr=le+"progress",xr=Mr+"__bar",Vr=le+"toggle",Gr=le+"spinner",kr=le+"sr",Fr=Ae+"initialized",Oe=Ae+"active",Yn=Ae+"prev",$n=Ae+"next",Jt=Ae+"visible",Qt=Ae+"loading",Kn=Ae+"focus-in",qn=Ae+"overflow",Ur=[Oe,Jt,Yn,$n,Qt,Kn,qn],Hr={slide:Rt,clone:Bn,arrows:Zt,arrow:Dt,prev:zn,next:Xn,pagination:Ct,page:Wn,spinner:Gr};function Br(t,n){if(dn(t.closest))return t.closest(n);for(var e=t;e&&e.nodeType===1&&!je(e,n);)e=e.parentElement;return e}var zr=5,jn=200,Zn="touchstart mousedown",en="touchmove mousemove",tn="touchend touchcancel mouseup click";function Xr(t,n,e){var r=z(t),i=r.on,o=r.bind,a=t.root,c=e.i18n,l={},d=[],s=[],m=[],v,E,u;function f(){g(),P(),h()}function I(){i(K,_),i(K,f),i(Q,h),o(document,Zn+" keydown",function(S){u=S.type==="keydown"},{capture:!0}),o(a,"focusin",function(){de(a,Kn,!!u)})}function _(S){var D=qt.concat("style");fe(d),Ee(a,s),Ee(v,m),ce([v,E],D),ce(a,S?D:["style",st])}function h(){Ee(a,s),Ee(v,m),s=x(jt),m=x(Hn),ue(a,s),ue(v,m),M(a,ie,e.label),M(a,$t,e.labelledby)}function g(){v=R("."+Hn),E=Ze(v,"."+Pr),nt(v&&E,"A track/list element is missing."),mt(d,En(E,"."+Rt+":not(."+Bn+")")),Re({arrows:Zt,pagination:Ct,prev:zn,next:Xn,bar:xr,toggle:Vr},function(S,D){l[D]=R("."+S)}),Qe(l,{root:a,track:v,list:E,slides:d})}function P(){var S=a.id||vr(tt),D=e.role;a.id=S,v.id=v.id||S+"-track",E.id=E.id||S+"-list",!oe(a,ge)&&a.tagName!=="SECTION"&&D&&M(a,ge,D),M(a,st,c.carousel),M(E,ge,"presentation")}function R(S){var D=In(a,S);return D&&Br(D,"."+jt)===a?D:void 0}function x(S){return[S+"--"+e.type,S+"--"+e.direction,e.drag&&S+"--draggable",e.isNavigation&&S+"--nav",S===jt&&Oe]}return Qe(l,{setup:f,mount:I,destroy:_})}var He="slide",Be="loop",ut="fade";function Wr(t,n,e,r){var i=z(t),o=i.on,a=i.emit,c=i.bind,l=t.Components,d=t.root,s=t.options,m=s.isNavigation,v=s.updateOnMove,E=s.i18n,u=s.pagination,f=s.slideFocus,I=l.Direction.resolve,_=oe(r,"style"),h=oe(r,ie),g=e>-1,P=Ze(r,"."+wr),R;function x(){g||(r.id=d.id+"-slide"+zt(n+1),M(r,ge,u?"tabpanel":"group"),M(r,st,E.slide),M(r,ie,h||Bt(E.slideLabel,[n+1,t.length]))),S()}function S(){c(r,"click",U(a,yn,w)),c(r,"keydown",U(a,bn,w)),o([it,On,Fe],p),o(Ln,k),v&&o(Se,O)}function D(){R=!0,i.destroy(),Ee(r,Ur),ce(r,qt),M(r,"style",_),M(r,ie,h||"")}function k(){var b=t.splides.map(function(A){var C=A.splide.Components.Slides.getAt(n);return C?C.slide.id:""}).join(" ");M(r,ie,Bt(E.slideX,(g?e:n)+1)),M(r,ot,b),M(r,ge,f?"button":""),f&&ce(r,st)}function O(){R||p()}function p(){if(!R){var b=t.index;y(),N(),de(r,Yn,n===b-1),de(r,$n,n===b+1)}}function y(){var b=G();b!==mn(r,Oe)&&(de(r,Oe,b),M(r,xn,m&&b||""),a(b?Er:gr,w))}function N(){var b=Y(),A=!b&&(!G()||g);if(t.state.is([Ve,We])||M(r,Gn,A||""),M(kt(r,s.focusableNodes||""),Ue,A?-1:""),f&&M(r,Ue,A?-1:0),b!==mn(r,Jt)&&(de(r,Jt,b),a(b?hr:mr,w)),!b&&document.activeElement===r){var C=l.Slides.getAt(t.index);C&&hn(C.slide)}}function V(b,A,C){ae(C&&P||r,b,A)}function G(){var b=t.index;return b===n||s.cloneStatus&&b===e}function Y(){if(t.is(ut))return G();var b=re(l.Elements.track),A=re(r),C=I("left",!0),F=I("right",!0);return It(b[C])<=rt(A[C])&&It(A[F])<=rt(b[F])}function X(b,A){var C=J(b-n);return!g&&(s.rewind||t.is(Be))&&(C=Te(C,t.length-C)),C<=A}var w={index:n,slideIndex:e,slide:r,container:P,isClone:g,mount:x,destroy:D,update:p,style:V,isWithin:X};return w}function Yr(t,n,e){var r=z(t),i=r.on,o=r.emit,a=r.bind,c=n.Elements,l=c.slides,d=c.list,s=[];function m(){v(),i(K,E),i(K,v)}function v(){l.forEach(function(p,y){f(p,y,-1)})}function E(){R(function(p){p.destroy()}),fe(s)}function u(){R(function(p){p.update()})}function f(p,y,N){var V=Wr(t,y,N,p);V.mount(),s.push(V),s.sort(function(G,Y){return G.index-Y.index})}function I(p){return p?x(function(y){return!y.isClone}):s}function _(p){var y=n.Controller,N=y.toIndex(p),V=y.hasFocus()?1:e.perPage;return x(function(G){return Tt(G.index,N,N+V-1)})}function h(p){return x(p)[0]}function g(p,y){ne(p,function(N){if(_e(N)&&(N=_n(N)),vn(N)){var V=l[y];V?Gt(N,V):qe(d,N),ue(N,e.classes.slide),D(N,U(o,at))}}),o(K)}function P(p){De(x(p).map(function(y){return y.slide})),o(K)}function R(p,y){I(y).forEach(p)}function x(p){return s.filter(dn(p)?p:function(y){return _e(p)?je(y.slide,p):Vt(Ke(p),y.index)})}function S(p,y,N){R(function(V){V.style(p,y,N)})}function D(p,y){var N=kt(p,"img"),V=N.length;V?N.forEach(function(G){a(G,"load error",function(){--V||y()})}):y()}function k(p){return p?l.length:s.length}function O(){return s.length>e.perPage}return{mount:m,destroy:E,update:u,register:f,get:I,getIn:_,getAt:h,add:g,remove:P,forEach:R,filter:x,style:S,getLength:k,isEnough:O}}function $r(t,n,e){var r=z(t),i=r.on,o=r.bind,a=r.emit,c=n.Slides,l=n.Direction.resolve,d=n.Elements,s=d.root,m=d.track,v=d.list,E=c.getAt,u=c.style,f,I,_;function h(){g(),o(window,"resize load",Dr(U(a,at))),i([Q,K],g),i(at,P)}function g(){f=e.direction===Lt,ae(s,"maxWidth",Ce(e.width)),ae(m,l("paddingLeft"),R(!1)),ae(m,l("paddingRight"),R(!0)),P(!0)}function P(w){var b=re(s);(w||I.width!==b.width||I.height!==b.height)&&(ae(m,"height",x()),u(l("marginRight"),Ce(e.gap)),u("width",D()),u("height",k(),!0),I=b,a(Xt),_!==(_=X())&&(de(s,qn,_),a(Sr,_)))}function R(w){var b=e.padding,A=l(w?"right":"left");return b&&Ce(b[A]||(Ye(b)?0:b))||"0px"}function x(){var w="";return f&&(w=S(),nt(w,"height or heightRatio is missing."),w="calc("+w+" - "+R(!1)+" - "+R(!0)+")"),w}function S(){return Ce(e.height||re(v).width*e.heightRatio)}function D(){return e.autoWidth?null:Ce(e.fixedWidth)||(f?"":O())}function k(){return Ce(e.fixedHeight)||(f?e.autoHeight?null:O():S())}function O(){var w=Ce(e.gap);return"calc((100%"+(w&&" + "+w)+")/"+(e.perPage||1)+(w&&" - "+w)+")"}function p(){return re(v)[l("width")]}function y(w,b){var A=E(w||0);return A?re(A.slide)[l("width")]+(b?0:G()):0}function N(w,b){var A=E(w);if(A){var C=re(A.slide)[l("right")],F=re(v)[l("left")];return J(C-F)+(b?0:G())}return 0}function V(w){return N(t.length-1)-N(0)+y(0,w)}function G(){var w=E(0);return w&&parseFloat(ae(w.slide,l("marginRight")))||0}function Y(w){return parseFloat(ae(m,l("padding"+(w?"Right":"Left"))))||0}function X(){return t.is(ut)||V(!0)>p()}return{mount:h,resize:P,listSize:p,slideSize:y,sliderSize:V,totalSize:N,getPadding:Y,isOverflow:X}}var Kr=2;function qr(t,n,e){var r=z(t),i=r.on,o=n.Elements,a=n.Slides,c=n.Direction.resolve,l=[],d;function s(){i(K,m),i([Q,at],E),(d=I())&&(u(d),n.Layout.resize(!0))}function m(){v(),s()}function v(){De(l),fe(l),r.destroy()}function E(){var _=I();d!==_&&(d<_||!_)&&r.emit(K)}function u(_){var h=a.get().slice(),g=h.length;if(g){for(;h.length<_;)mt(h,h);mt(h.slice(-_),h.slice(0,_)).forEach(function(P,R){var x=R<_,S=f(P.slide,R);x?Gt(S,h[0].slide):qe(o.list,S),mt(l,S),a.register(S,R-_+(x?0:g),P.index)})}}function f(_,h){var g=_.cloneNode(!0);return ue(g,e.classes.clone),g.id=t.root.id+"-clone"+zt(h+1),g}function I(){var _=e.clones;if(!t.is(Be))_=0;else if($e(_)){var h=e[c("fixedWidth")]&&n.Layout.slideSize(0),g=h&&rt(re(o.track)[c("width")]/h);_=g||e[c("autoWidth")]&&t.length||e.perPage*Kr}return _}return{mount:s,destroy:v}}function jr(t,n,e){var r=z(t),i=r.on,o=r.emit,a=t.state.set,c=n.Layout,l=c.slideSize,d=c.getPadding,s=c.totalSize,m=c.listSize,v=c.sliderSize,E=n.Direction,u=E.resolve,f=E.orient,I=n.Elements,_=I.list,h=I.track,g;function P(){g=n.Transition,i([be,Xt,Q,K],R)}function R(){n.Controller.isBusy()||(n.Scroll.cancel(),S(t.index),n.Slides.update())}function x(A,C,F,j){A!==C&&w(A>F)&&(p(),D(O(V(),A>F),!0)),a(Ve),o(Se,C,F,A),g.start(C,function(){a(xe),o(it,C,F,A),j&&j()})}function S(A){D(N(A,!0))}function D(A,C){if(!t.is(ut)){var F=C?A:k(A);ae(_,"transform","translate"+u("X")+"("+F+"px)"),A!==F&&o(On)}}function k(A){if(t.is(Be)){var C=y(A),F=C>n.Controller.getEnd(),j=C<0;(j||F)&&(A=O(A,F))}return A}function O(A,C){var F=A-X(C),j=v();return A-=f(j*(rt(J(F)/j)||1))*(C?1:-1),A}function p(){D(V(),!0),g.cancel()}function y(A){for(var C=n.Slides.get(),F=0,j=1/0,q=0;q<C.length;q++){var pe=C[q].index,T=J(N(pe,!0)-A);if(T<=j)j=T,F=pe;else break}return F}function N(A,C){var F=f(s(A-1)-Y(A));return C?G(F):F}function V(){var A=u("left");return re(_)[A]-re(h)[A]+f(d(!1))}function G(A){return e.trimSpace&&t.is(He)&&(A=ke(A,0,f(v(!0)-m()))),A}function Y(A){var C=e.focus;return C==="center"?(m()-l(A,!0))/2:+C*l(A)||0}function X(A){return N(A?n.Controller.getEnd():0,!!e.trimSpace)}function w(A){var C=f(O(V(),A));return A?C>=0:C<=_[u("scrollWidth")]-re(h)[u("width")]}function b(A,C){C=$e(C)?V():C;var F=A!==!0&&f(C)<f(X(!1)),j=A!==!1&&f(C)>f(X(!0));return F||j}return{mount:P,move:x,jump:S,translate:D,shift:O,cancel:p,toIndex:y,toPosition:N,getPosition:V,getLimit:X,exceededLimit:b,reposition:R}}function Zr(t,n,e){var r=z(t),i=r.on,o=r.emit,a=n.Move,c=a.getPosition,l=a.getLimit,d=a.toPosition,s=n.Slides,m=s.isEnough,v=s.getLength,E=e.omitEnd,u=t.is(Be),f=t.is(He),I=U(V,!1),_=U(V,!0),h=e.start||0,g,P=h,R,x,S;function D(){k(),i([Q,K,St],k),i(Xt,O)}function k(){R=v(!0),x=e.perMove,S=e.perPage,g=w();var T=ke(h,0,E?g:R-1);T!==h&&(h=T,a.reposition())}function O(){g!==w()&&o(St)}function p(T,H,ee){if(!pe()){var $=N(T),Z=X($);Z>-1&&(H||Z!==h)&&(F(Z),a.move($,Z,P,ee))}}function y(T,H,ee,$){n.Scroll.scroll(T,H,ee,function(){var Z=X(a.toIndex(c()));F(E?Te(Z,g):Z),$&&$()})}function N(T){var H=h;if(_e(T)){var ee=T.match(/([+\-<>])(\d+)?/)||[],$=ee[1],Z=ee[2];$==="+"||$==="-"?H=G(h+ +(""+$+(+Z||1)),h):$===">"?H=Z?b(+Z):I(!0):$==="<"&&(H=_(!0))}else H=u?T:ke(T,0,g);return H}function V(T,H){var ee=x||(q()?1:S),$=G(h+ee*(T?-1:1),h,!(x||q()));return $===-1&&f&&!Tn(c(),l(!T),1)?T?0:g:H?$:X($)}function G(T,H,ee){if(m()||q()){var $=Y(T);$!==T&&(H=T,T=$,ee=!1),T<0||T>g?!x&&(Tt(0,T,H,!0)||Tt(g,H,T,!0))?T=b(A(T)):u?T=ee?T<0?-(R%S||S):R:T:e.rewind?T=T<0?g:0:T=-1:ee&&T!==H&&(T=b(A(H)+(T<H?-1:1)))}else T=-1;return T}function Y(T){if(f&&e.trimSpace==="move"&&T!==h)for(var H=c();H===d(T,!0)&&Tt(T,0,t.length-1,!e.rewind);)T<h?--T:++T;return T}function X(T){return u?(T+R)%R||0:T}function w(){for(var T=R-(q()||u&&x?1:S);E&&T-- >0;)if(d(R-1,!0)!==d(T,!0)){T++;break}return ke(T,0,R-1)}function b(T){return ke(q()?T:S*T,0,g)}function A(T){return q()?Te(T,g):It((T>=g?R-1:T)/S)}function C(T){var H=a.toIndex(T);return f?ke(H,0,g):H}function F(T){T!==h&&(P=h,h=T)}function j(T){return T?P:h}function q(){return!$e(e.focus)||e.isNavigation}function pe(){return t.state.is([Ve,We])&&!!e.waitForTransition}return{mount:D,go:p,scroll:y,getNext:I,getPrev:_,getAdjacent:V,getEnd:w,setIndex:F,getIndex:j,toIndex:b,toPage:A,toDest:C,hasFocus:q,isBusy:pe}}var Jr="http://www.w3.org/2000/svg",Qr="m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z",bt=40;function ei(t,n,e){var r=z(t),i=r.on,o=r.bind,a=r.emit,c=e.classes,l=e.i18n,d=n.Elements,s=n.Controller,m=d.arrows,v=d.track,E=m,u=d.prev,f=d.next,I,_,h={};function g(){R(),i(Q,P)}function P(){x(),g()}function R(){var y=e.arrows;y&&!(u&&f)&&k(),u&&f&&(Qe(h,{prev:u,next:f}),et(E,y?"":"none"),ue(E,_=Zt+"--"+e.direction),y&&(S(),p(),M([u,f],ot,v.id),a(Ar,u,f)))}function x(){r.destroy(),Ee(E,_),I?(De(m?[u,f]:E),u=f=null):ce([u,f],qt)}function S(){i([be,it,K,Fe,St],p),o(f,"click",U(D,">")),o(u,"click",U(D,"<"))}function D(y){s.go(y,!0)}function k(){E=m||Ge("div",c.arrows),u=O(!0),f=O(!1),I=!0,qe(E,[u,f]),!m&&Gt(E,v)}function O(y){var N='<button class="'+c.arrow+" "+(y?c.prev:c.next)+'" type="button"><svg xmlns="'+Jr+'" viewBox="0 0 '+bt+" "+bt+'" width="'+bt+'" height="'+bt+'" focusable="false"><path d="'+(e.arrowPath||Qr)+'" />';return _n(N)}function p(){if(u&&f){var y=t.index,N=s.getPrev(),V=s.getNext(),G=N>-1&&y<N?l.last:l.prev,Y=V>-1&&y>V?l.first:l.next;u.disabled=N<0,f.disabled=V<0,M(u,ie,G),M(f,ie,Y),a(pr,u,f,N,V)}}return{arrows:h,mount:g,destroy:x,update:p}}var ti=Ut+"-interval";function ni(t,n,e){var r=z(t),i=r.on,o=r.bind,a=r.emit,c=At(e.interval,t.go.bind(t,">"),S),l=c.isPaused,d=n.Elements,s=n.Elements,m=s.root,v=s.toggle,E=e.autoplay,u,f,I=E==="pause";function _(){E&&(h(),v&&M(v,ot,d.track.id),I||g(),x())}function h(){e.pauseOnHover&&o(m,"mouseenter mouseleave",function(k){u=k.type==="mouseenter",R()}),e.pauseOnFocus&&o(m,"focusin focusout",function(k){f=k.type==="focusin",R()}),v&&o(v,"click",function(){I?g():P(!0)}),i([Se,Wt,K],c.rewind),i(Se,D)}function g(){l()&&n.Slides.isEnough()&&(c.start(!e.resetProgress),f=u=I=!1,x(),a(Rn))}function P(k){k===void 0&&(k=!0),I=!!k,x(),l()||(c.pause(),a(Dn))}function R(){I||(u||f?P(!1):g())}function x(){v&&(de(v,Oe,!I),M(v,ie,e.i18n[I?"play":"pause"]))}function S(k){var O=d.bar;O&&ae(O,"width",k*100+"%"),a(Lr,k)}function D(k){var O=n.Slides.getAt(k);c.set(O&&+oe(O.slide,ti)||e.interval)}return{mount:_,destroy:c.cancel,play:g,pause:P,isPaused:l}}function ri(t,n,e){var r=z(t),i=r.on;function o(){e.cover&&(i(Cn,U(c,!0)),i([be,Q,K],U(a,!0)))}function a(l){n.Slides.forEach(function(d){var s=Ze(d.container||d.slide,"img");s&&s.src&&c(l,s,d)})}function c(l,d,s){s.style("background",l?'center/cover no-repeat url("'+d.src+'")':"",!0),et(d,l?"none":"")}return{mount:o,destroy:U(a,!1)}}var ii=10,ai=600,oi=.6,si=1.5,ui=800;function ci(t,n,e){var r=z(t),i=r.on,o=r.emit,a=t.state.set,c=n.Move,l=c.getPosition,d=c.getLimit,s=c.exceededLimit,m=c.translate,v=t.is(He),E,u,f=1;function I(){i(Se,P),i([Q,K],R)}function _(S,D,k,O,p){var y=l();if(P(),k&&(!v||!s())){var N=n.Layout.sliderSize(),V=Ht(S)*N*It(J(S)/N)||0;S=c.toPosition(n.Controller.toDest(S%N))+V}var G=Tn(y,S,1);f=1,D=G?0:D||_t(J(S-y)/si,ui),u=O,E=At(D,h,U(g,y,S,p),1),a(We),o(Wt),E.start()}function h(){a(xe),u&&u(),o(Fe)}function g(S,D,k,O){var p=l(),y=S+(D-S)*x(O),N=(y-p)*f;m(p+N),v&&!k&&s()&&(f*=oi,J(N)<ii&&_(d(s(!0)),ai,!1,u,!0))}function P(){E&&E.cancel()}function R(){E&&!E.isPaused()&&(P(),h())}function x(S){var D=e.easingFunc;return D?D(S):1-Math.pow(1-S,4)}return{mount:I,destroy:P,scroll:_,cancel:R}}var ze={passive:!1,capture:!0};function li(t,n,e){var r=z(t),i=r.on,o=r.emit,a=r.bind,c=r.unbind,l=t.state,d=n.Move,s=n.Scroll,m=n.Controller,v=n.Elements.track,E=n.Media.reduce,u=n.Direction,f=u.resolve,I=u.orient,_=d.getPosition,h=d.exceededLimit,g,P,R,x,S,D=!1,k,O,p;function y(){a(v,en,wt,ze),a(v,tn,wt,ze),a(v,Zn,V,ze),a(v,"click",X,{capture:!0}),a(v,"dragstart",ve),i([be,Q],N)}function N(){var L=e.drag;Qn(!L),x=L==="free"}function V(L){if(k=!1,!O){var B=Z(L);$(L.target)&&(B||!L.button)&&(m.isBusy()?ve(L,!0):(p=B?v:window,S=l.is([Ve,We]),R=null,a(p,en,G,ze),a(p,tn,Y,ze),d.cancel(),s.cancel(),w(L)))}}function G(L){if(l.is(Et)||(l.set(Et),o(_r)),L.cancelable)if(S){d.translate(g+ee(q(L)));var B=pe(L)>jn,we=D!==(D=h());(B||we)&&w(L),k=!0,o(Ir),ve(L)}else C(L)&&(S=A(L),ve(L))}function Y(L){l.is(Et)&&(l.set(xe),o(Tr)),S&&(b(L),ve(L)),c(p,en,G),c(p,tn,Y),S=!1}function X(L){!O&&k&&ve(L,!0)}function w(L){R=P,P=L,g=_()}function b(L){var B=F(L),we=j(B),lt=e.rewind&&e.rewindByDrag;E(!1),x?m.scroll(we,0,e.snap):t.is(ut)?m.go(I(Ht(B))<0?lt?"<":"-":lt?">":"+"):t.is(He)&&D&&lt?m.go(h(!0)?">":"<"):m.go(m.toDest(we),!0),E(!0)}function A(L){var B=e.dragMinThreshold,we=Ye(B),lt=we&&B.mouse||0,Pi=(we?B.touch:+B)||10;return J(q(L))>(Z(L)?Pi:lt)}function C(L){return J(q(L))>J(q(L,!0))}function F(L){if(t.is(Be)||!D){var B=pe(L);if(B&&B<jn)return q(L)/B}return 0}function j(L){return _()+Ht(L)*Te(J(L)*(e.flickPower||600),x?1/0:n.Layout.listSize()*(e.flickMaxPages||1))}function q(L,B){return H(L,B)-H(T(L),B)}function pe(L){return Ft(L)-Ft(T(L))}function T(L){return P===L&&R||P}function H(L,B){return(Z(L)?L.changedTouches[0]:L)["page"+f(B?"Y":"X")]}function ee(L){return L/(D&&t.is(He)?zr:1)}function $(L){var B=e.noDrag;return!je(L,"."+Wn+", ."+Dt)&&(!B||!je(L,B))}function Z(L){return typeof TouchEvent<"u"&&L instanceof TouchEvent}function Oi(){return S}function Qn(L){O=L}return{mount:y,disable:Qn,isDragging:Oi}}var fi={Spacebar:" ",Right:Nt,Left:yt,Up:Pn,Down:wn};function nn(t){return t=_e(t)?t:t.key,fi[t]||t}var Jn="keydown";function di(t,n,e){var r=z(t),i=r.on,o=r.bind,a=r.unbind,c=t.root,l=n.Direction.resolve,d,s;function m(){v(),i(Q,E),i(Q,v),i(Se,f)}function v(){var _=e.keyboard;_&&(d=_==="global"?window:c,o(d,Jn,I))}function E(){a(d,Jn)}function u(_){s=_}function f(){var _=s;s=!0,ln(function(){s=_})}function I(_){if(!s){var h=nn(_);h===l(yt)?t.go("<"):h===l(Nt)&&t.go(">")}}return{mount:m,destroy:E,disable:u}}var ct=Ut+"-lazy",Ot=ct+"-srcset",vi="["+ct+"], ["+Ot+"]";function Ei(t,n,e){var r=z(t),i=r.on,o=r.off,a=r.bind,c=r.emit,l=e.lazyLoad==="sequential",d=[it,Fe],s=[];function m(){e.lazyLoad&&(v(),i(K,v))}function v(){fe(s),E(),l?_():(o(d),i(d,u),u())}function E(){n.Slides.forEach(function(h){kt(h.slide,vi).forEach(function(g){var P=oe(g,ct),R=oe(g,Ot);if(P!==g.src||R!==g.srcset){var x=e.classes.spinner,S=g.parentElement,D=Ze(S,"."+x)||Ge("span",x,S);s.push([g,h,D]),g.src||et(g,"none")}})})}function u(){s=s.filter(function(h){var g=e.perPage*((e.preloadPages||1)+1)-1;return h[1].isWithin(t.index,g)?f(h):!0}),s.length||o(d)}function f(h){var g=h[0];ue(h[1].slide,Qt),a(g,"load error",U(I,h)),M(g,"src",oe(g,ct)),M(g,"srcset",oe(g,Ot)),ce(g,ct),ce(g,Ot)}function I(h,g){var P=h[0],R=h[1];Ee(R.slide,Qt),g.type!=="error"&&(De(h[2]),et(P,""),c(Cn,P,R),c(at)),l&&_()}function _(){s.length&&f(s.shift())}return{mount:m,destroy:U(fe,s),check:u}}function gi(t,n,e){var r=z(t),i=r.on,o=r.emit,a=r.bind,c=n.Slides,l=n.Elements,d=n.Controller,s=d.hasFocus,m=d.getIndex,v=d.go,E=n.Direction.resolve,u=l.pagination,f=[],I,_;function h(){g(),i([Q,K,St],h);var O=e.pagination;u&&et(u,O?"":"none"),O&&(i([Se,Wt,Fe],k),P(),k(),o(yr,{list:I,items:f},D(t.index)))}function g(){I&&(De(u?me(I.children):I),Ee(I,_),fe(f),I=null),r.destroy()}function P(){var O=t.length,p=e.classes,y=e.i18n,N=e.perPage,V=s()?d.getEnd()+1:rt(O/N);I=u||Ge("ul",p.pagination,l.track.parentElement),ue(I,_=Ct+"--"+S()),M(I,ge,"tablist"),M(I,ie,y.select),M(I,Kt,S()===Lt?"vertical":"");for(var G=0;G<V;G++){var Y=Ge("li",null,I),X=Ge("button",{class:p.page,type:"button"},Y),w=c.getIn(G).map(function(A){return A.slide.id}),b=!s()&&N>1?y.pageX:y.slideX;a(X,"click",U(R,G)),e.paginationKeyboard&&a(X,"keydown",U(x,G)),M(Y,ge,"presentation"),M(X,ge,"tab"),M(X,ot,w.join(" ")),M(X,ie,Bt(b,G+1)),M(X,Ue,-1),f.push({li:Y,button:X,page:G})}}function R(O){v(">"+O,!0)}function x(O,p){var y=f.length,N=nn(p),V=S(),G=-1;N===E(Nt,!1,V)?G=++O%y:N===E(yt,!1,V)?G=(--O+y)%y:N==="Home"?G=0:N==="End"&&(G=y-1);var Y=f[G];Y&&(hn(Y.button),v(">"+G),ve(p,!0))}function S(){return e.paginationDirection||e.direction}function D(O){return f[d.toPage(O)]}function k(){var O=D(m(!0)),p=D(m());if(O){var y=O.button;Ee(y,Oe),ce(y,Vn),M(y,Ue,-1)}if(p){var N=p.button;ue(N,Oe),M(N,Vn,!0),M(N,Ue,"")}o(Nr,{list:I,items:f},O,p)}return{items:f,mount:h,destroy:g,getAt:D,update:k}}var hi=[" ","Enter"];function mi(t,n,e){var r=e.isNavigation,i=e.slideFocus,o=[];function a(){t.splides.forEach(function(u){u.isParent||(d(t,u.splide),d(u.splide,t))}),r&&s()}function c(){o.forEach(function(u){u.destroy()}),fe(o)}function l(){c(),a()}function d(u,f){var I=z(u);I.on(Se,function(_,h,g){f.go(f.is(Be)?g:_)}),o.push(I)}function s(){var u=z(t),f=u.on;f(yn,v),f(bn,E),f([be,Q],m),o.push(u),u.emit(Ln,t.splides)}function m(){M(n.Elements.list,Kt,e.direction===Lt?"vertical":"")}function v(u){t.go(u.index)}function E(u,f){Vt(hi,nn(f))&&(v(u),ve(f))}return{setup:U(n.Media.set,{slideFocus:$e(i)?r:i},!0),mount:a,destroy:c,remount:l}}function _i(t,n,e){var r=z(t),i=r.bind,o=0;function a(){e.wheel&&i(n.Elements.track,"wheel",c,ze)}function c(d){if(d.cancelable){var s=d.deltaY,m=s<0,v=Ft(d),E=e.wheelMinThreshold||0,u=e.wheelSleep||0;J(s)>E&&v-o>u&&(t.go(m?"<":">"),o=v),l(m)&&ve(d)}}function l(d){return!e.releaseWheel||t.state.is(Ve)||n.Controller.getAdjacent(d)!==-1}return{mount:a}}var Ii=90;function Ti(t,n,e){var r=z(t),i=r.on,o=n.Elements.track,a=e.live&&!e.isNavigation,c=Ge("span",kr),l=At(Ii,U(s,!1));function d(){a&&(v(!n.Autoplay.isPaused()),M(o,Un,!0),c.textContent="…",i(Rn,U(v,!0)),i(Dn,U(v,!1)),i([it,Fe],U(s,!0)))}function s(E){M(o,Fn,E),E?(qe(o,c),l.start()):(De(c),l.cancel())}function m(){ce(o,[kn,Un,Fn]),De(c)}function v(E){a&&M(o,kn,E?"off":"polite")}return{mount:d,disable:v,destroy:m}}var Si=Object.freeze({__proto__:null,Media:Cr,Direction:br,Elements:Xr,Slides:Yr,Layout:$r,Clones:qr,Move:jr,Controller:Zr,Arrows:ei,Autoplay:ni,Cover:ri,Scroll:ci,Drag:li,Keyboard:di,LazyLoad:Ei,Pagination:gi,Sync:mi,Wheel:_i,Live:Ti}),Ai={prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay",carousel:"carousel",slide:"slide",select:"Select a slide to show",slideLabel:"%s of %s"},pi={type:"slide",role:"region",speed:400,perPage:1,cloneStatus:!0,arrows:!0,pagination:!0,paginationKeyboard:!0,interval:5e3,pauseOnHover:!0,pauseOnFocus:!0,resetProgress:!0,easing:"cubic-bezier(0.25, 1, 0.5, 1)",drag:!0,direction:"ltr",trimSpace:!0,focusableNodes:"a, button, textarea, input, select, iframe",live:!0,classes:Hr,i18n:Ai,reducedMotion:{speed:0,rewindSpeed:0,autoplay:"pause"}};function yi(t,n,e){var r=n.Slides;function i(){z(t).on([be,K],o)}function o(){r.forEach(function(c){c.style("transform","translateX(-"+100*c.index+"%)")})}function a(c,l){r.style("transition","opacity "+e.speed+"ms "+e.easing),ln(l)}return{mount:i,start:a,cancel:wt}}function Ni(t,n,e){var r=n.Move,i=n.Controller,o=n.Scroll,a=n.Elements.list,c=U(ae,a,"transition"),l;function d(){z(t).bind(a,"transitionend",function(E){E.target===a&&l&&(m(),l())})}function s(E,u){var f=r.toPosition(E,!0),I=r.getPosition(),_=v(E);J(f-I)>=1&&_>=1?e.useScroll?o.scroll(f,_,!1,u):(c("transform "+_+"ms "+e.easing),r.translate(f,!0),l=u):(r.jump(E),u())}function m(){c(""),o.cancel()}function v(E){var u=e.rewindSpeed;if(t.is(He)&&u){var f=i.getIndex(!0),I=i.getEnd();if(f===0&&E>=I||f>=I&&E===0)return u}return e.speed}return{mount:d,start:s,cancel:m}}var Li=(function(){function t(e,r){this.event=z(),this.Components={},this.state=Rr(Me),this.splides=[],this._o={},this._E={};var i=_e(e)?In(document,e):e;nt(i,i+" is invalid."),this.root=i,r=Ie({label:oe(i,ie)||"",labelledby:oe(i,$t)||""},pi,t.defaults,r||{});try{Ie(r,JSON.parse(oe(i,Ut)))}catch{nt(!1,"Invalid JSON")}this._o=Object.create(Ie({},r))}var n=t.prototype;return n.mount=function(r,i){var o=this,a=this.state,c=this.Components;nt(a.is([Me,gt]),"Already mounted!"),a.set(Me),this._C=c,this._T=i||this._T||(this.is(ut)?yi:Ni),this._E=r||this._E;var l=Qe({},Si,this._E,{Transition:this._T});return Re(l,function(d,s){var m=d(o,c,o._o);c[s]=m,m.setup&&m.setup()}),Re(c,function(d){d.mount&&d.mount()}),this.emit(be),ue(this.root,Fr),a.set(xe),this.emit(pn),this},n.sync=function(r){return this.splides.push({splide:r}),r.splides.push({splide:this,isParent:!0}),this.state.is(xe)&&(this._C.Sync.remount(),r.Components.Sync.remount()),this},n.go=function(r){return this._C.Controller.go(r),this},n.on=function(r,i){return this.event.on(r,i),this},n.off=function(r){return this.event.off(r),this},n.emit=function(r){var i;return(i=this.event).emit.apply(i,[r].concat(me(arguments,1))),this},n.add=function(r,i){return this._C.Slides.add(r,i),this},n.remove=function(r){return this._C.Slides.remove(r),this},n.is=function(r){return this._o.type===r},n.refresh=function(){return this.emit(K),this},n.destroy=function(r){r===void 0&&(r=!0);var i=this.event,o=this.state;return o.is(Me)?z(this).on(pn,this.destroy.bind(this,r)):(Re(this._C,function(a){a.destroy&&a.destroy(r)},!0),i.emit(Nn),i.destroy(),r&&fe(this.splides),o.set(gt)),this},lr(t,[{key:"options",get:function(){return this._o},set:function(r){this._C.Media.set(r,!0,!0)}},{key:"length",get:function(){return this._C.Slides.getLength(!0)}},{key:"index",get:function(){return this._C.Controller.getIndex()}}]),t})(),rn=Li;rn.defaults={},rn.STATES=dr;class Ri{constructor(){this.isDestroyed=!1}initialize(n){try{return this.destroy(),this.splideInstance=new rn(`.${W.slider.dom.splideClass}`,n),this.splideInstance.mount(),this.isDestroyed=!1,this.splideInstance}catch{return}}destroy(){if(this.splideInstance&&!this.isDestroyed)try{this.splideInstance.destroy()}catch{}finally{delete this.splideInstance,this.isDestroyed=!0}}isInitialized(){return this.splideInstance!==null&&!this.isDestroyed}getInstance(){if(this.isInitialized())return this.splideInstance}reinitialize(n){return this.destroy(),this.initialize(n)}isContainerAvailable(){try{return document.querySelector(`.${W.slider.dom.splideClass}`)!==null}catch{return!1}}initializeWithDelay(n,e=dt.CHECK_INTERVAL){setTimeout(()=>{this.isContainerAvailable()&&this.initialize(n)},e)}getStatus(){return{isInitialized:this.isInitialized(),isDestroyed:this.isDestroyed,hasInstance:this.splideInstance!==null,containerAvailable:this.isContainerAvailable()}}}class Di{constructor(){this.checkTime=W.slider.checkTime,this.configManager=new or,this.domBuilder=new sr,this.dataManager=new ur,this.lifecycleManager=new Ri}attachAdvertiseHeader(n){try{this.destroy();const e=this.dataManager.fetchSlideData();if(e.length===te.EMPTY_LENGTH)return;const r=this.domBuilder.createContainer(),i=this.domBuilder.createSplideElement(r),o=this.domBuilder.createSplideTrack(i),a=this.populateSlides(o,e);this.domBuilder.createPagination(i),this.domBuilder.createNavigation(i),this.domBuilder.appendToDOM(r),setTimeout(()=>{this.initializeSplide(a)},this.checkTime)}catch{}}populateSlides(n,e){let r=te.EMPTY_LENGTH;for(const i of e)if(i.isValid){const o=this.domBuilder.createSlide(i.imageSrc,i.imageLink);Ne(n,o),r+=ft.SLIDE_INCREMENT}return r}initializeSplide(n){try{const e=this.dataManager.getTransitionTime(),r=this.configManager.calculateConfiguration(n,e);if(!this.configManager.validateConfiguration(r.finalConfig).isValid)return;this.lifecycleManager.initialize(r.finalConfig)}catch{}}destroy(){this.lifecycleManager.destroy(),this.domBuilder.cleanup()}}class Xe{constructor(){this.errorLog=[],this.isInitialized=!1}static getInstance(){return Xe.instance||(Xe.instance=new Xe),Xe.instance}initialize(){try{return this.isInitialized||(this.setupGlobalErrorHandling(),this.isInitialized=!0),!0}catch{return!1}}handleSync(n,e){try{return n()}catch(r){return this.logError(r,e),!1}}handleAsync(n,e){return n().catch(r=>(this.logError(r,e),!1))}logError(n,e){try{const r={timestamp:new Date,error:n,context:e};this.errorLog.push(r),this.errorLog.length>ir.MAX_ERROR_LOG_ENTRIES&&this.errorLog.shift()}catch{}}setupGlobalErrorHandling(){try{globalThis.addEventListener("unhandledrejection",n=>{this.logError(new Error(String(n.reason)),"Unhandled Promise Rejection")})}catch{}}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[]}}class Pe{constructor(){}static getInstance(){return Pe.instance||(Pe.instance=new Pe),Pe.instance}isTagsPage(){try{return ye.current.get("routeName")==="tags"}catch{try{return globalThis.location.pathname.includes("/tags")}catch{return!1}}}getConfig(){return W}isSlideshowConfigured(){try{for(let r=1;r<=W.slider.maxSlides;r+=1)if(ye.forum.attribute(`wusong8899-header-advertisement.Image${r}`))return!0;return!1}catch{return!1}}}ye.initializers.add(W.app.extensionId,()=>{const t=Xe.getInstance(),n=Pe.getInstance();if(!t.initialize())return;const e=new Di;er.extend(tr.prototype,"view",function(i){t.handleSync(()=>{n.isTagsPage()&&Ci(i,e)},"HeaderPrimary view extension")})});const Ci=(t,n,e)=>{try{if(Pe.getInstance().isSlideshowConfigured())try{n.attachAdvertiseHeader(t)}catch{}!ye.session.user&&un()&&bi()}catch{}},bi=()=>{let t=document.getElementById(W.ui.headerIconId);if(t===null){const n=ye.forum.attribute("wusong8899-header-advertisement.HeaderIconUrl")||W.ui.headerIconUrl;t=document.createElement("div"),t.id=W.ui.headerIconId,t.className="HeaderIcon-container mobile-only",t.innerHTML=`<img src="${n}" alt="Header Icon" class="HeaderIcon-image" />`;const e=document.querySelector("#app-navigation .Navigation.ButtonGroup.App-backControl");e&&e.appendChild(t)}}})(flarum.core.compat["common/extend"],flarum.core.compat["forum/app"],flarum.core.compat["forum/components/HeaderPrimary"]);
//# sourceMappingURL=forum.js.map

module.exports={};