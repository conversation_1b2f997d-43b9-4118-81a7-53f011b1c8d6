(function(er,pe,tr){"use strict";const an=e=>{try{return document.querySelector(e)||!1}catch{return!1}},nr=e=>{try{return document.querySelectorAll(e)}catch{return document.querySelectorAll("")}},be=(e,n={},t="")=>{try{const r=document.createElement(e);for(const[i,o]of Object.entries(n))i==="className"?r.className=String(o):i==="id"?r.id=String(o):r.setAttribute(i,String(o));return t&&(r.innerHTML=t),r}catch{return document.createElement("div")}},Xe=(e,n)=>{try{e.appendChild(n)}catch{}},rr=(e,n)=>{try{e.prepend(n)}catch{}},bt=e=>{try{e.remove()}catch{}},on={USER_AGENT_SUBSTR_START:0,USER_AGENT_SUBSTR_LENGTH:4},ir={MAX_ERROR_LOG_ENTRIES:50,DOM_READY_TIMEOUT:5e3,SLIDE_NUMBER_MIN:1,SLIDE_NUMBER_MAX:30,TRANSITION_TIME_MIN:1e3,TRANSITION_TIME_MAX:3e4,CONFIG_MAX_SLIDES_MIN:1,CONFIG_MAX_SLIDES_MAX:50},ft={SLIDE_INCREMENT:1,INITIAL_SLIDE_INDEX:1},te={EMPTY_LENGTH:0,FIRST_INDEX:0,NOT_FOUND_INDEX:-1,NEXT_ITEM_OFFSET:1,LAST_ITEM_OFFSET:-1},dt={CHECK_INTERVAL:10,DATA_CHECK_INTERVAL:100,DEFAULT_TRANSITION_TIME:5e3},sn={SPLIDE_AD_CONTAINER_ID:"splideAdContainer",HEADER_ICON_ID:"wusong8899HeaderAdvIcon"},ar={AD_SPLIDE:"adSplide"},vt={ID:"wusong8899-header-advertisement",TRANSLATION_PREFIX:"wusong8899-header-advertisement",MAX_SLIDES:30,HEADER_ICON_URL:"https://ex.cc/assets/files/date/test.png"},W={env:"production",app:{extensionId:vt.ID,translationPrefix:vt.TRANSLATION_PREFIX},slider:{maxSlides:vt.MAX_SLIDES,defaultTransitionTime:dt.DEFAULT_TRANSITION_TIME,checkTime:dt.CHECK_INTERVAL,dataCheckInterval:dt.DATA_CHECK_INTERVAL,dom:{containerId:sn.SPLIDE_AD_CONTAINER_ID,splideClass:ar.AD_SPLIDE},splide:{gap:"15px",type:"loop",focus:"center",perPage:1,pagination:!0,arrows:!0}},ui:{headerIconId:sn.HEADER_ICON_ID,headerIconUrl:vt.HEADER_ICON_URL}},ye={MIN_SLIDES_FOR_LOOP:2,MOBILE_PER_PAGE:1,TABLET_PER_PAGE:1,DESKTOP_PER_PAGE:1};class or{calculateConfiguration(n,t){const r=this.shouldEnableLoop(n),i=this.calculateResponsiveConfig(),o=this.buildFinalConfig(t,r);return{enableLoop:r,responsiveConfig:i,finalConfig:o}}validateConfiguration(n){const t=[];if(n.autoplay&&typeof n.autoplay=="object"&&(typeof n.autoplay.interval!="number"||n.autoplay.interval<=te.EMPTY_LENGTH)&&t.push("Invalid autoplay interval configuration"),(typeof n.gap!="string"||!n.gap.match(/^\d+px$/))&&t.push('Invalid gap configuration - must be in format "Npx"'),(typeof n.perPage!="number"||n.perPage<=te.EMPTY_LENGTH)&&t.push("Invalid perPage configuration"),n.breakpoints&&typeof n.breakpoints=="object")for(const[r,i]of Object.entries(n.breakpoints)){const o=Number(r);(Number.isNaN(o)||o<=te.EMPTY_LENGTH)&&t.push(`Invalid breakpoint width: ${r}`),(typeof i.perPage!="number"||i.perPage<=te.EMPTY_LENGTH)&&t.push(`Invalid perPage for breakpoint ${r}`),(typeof i.gap!="string"||!i.gap.match(/^\d+px$/))&&t.push(`Invalid gap for breakpoint ${r}`)}return{isValid:t.length===te.EMPTY_LENGTH,errors:t}}shouldEnableLoop(n){return n>=ye.MIN_SLIDES_FOR_LOOP}calculateResponsiveConfig(){return{mobile:{perPage:ye.MOBILE_PER_PAGE,gap:"10px"},tablet:{perPage:ye.TABLET_PER_PAGE,gap:"12px"},desktop:{perPage:ye.DESKTOP_PER_PAGE,gap:W.slider.splide.gap}}}buildFinalConfig(n,t){const r={320:{perPage:ye.MOBILE_PER_PAGE,gap:"10px"},768:{perPage:ye.TABLET_PER_PAGE,gap:"12px"},1024:{perPage:ye.DESKTOP_PER_PAGE,gap:W.slider.splide.gap}};let i="slide";t&&(i="loop");let o=!1;return n>te.EMPTY_LENGTH&&(o={interval:n,pauseOnHover:!0}),{type:i,autoplay:o,gap:W.slider.splide.gap,focus:W.slider.splide.focus,perPage:W.slider.splide.perPage,breakpoints:r,pagination:W.slider.splide.pagination,arrows:W.slider.splide.arrows,speed:600}}}const un=()=>{try{const{userAgent:e}=navigator;return e.substring(on.USER_AGENT_SUBSTR_START,on.USER_AGENT_SUBSTR_LENGTH)==="Mobi"}catch{return!1}};class sr{createContainer(){this.removeExistingNavigation();const n=be("div",{id:W.slider.dom.containerId,className:"adContainer adContainer--forced"});return this.container=n,n}createSplideElement(n){let t=`splide ${W.slider.dom.splideClass} adSplide--forced`;un()&&(t+=" adSplide--mobile");const r=be("div",{className:t});return Xe(n,r),r}createSplideTrack(n){const t=be("div",{className:"splide__track splide__track--forced"}),r=be("ul",{className:"splide__list splide__list--forced"});return Xe(t,r),Xe(n,t),r}createSlide(n,t){const r=be("li",{className:"splide__slide splide__slide--forced"});let i="";return t&&(i=`window.location.href="${t}"`),r.innerHTML=`<img onclick='${i}' src='${n}' class='splide__slide__image' />`,r}createPagination(n){const t=be("ul",{className:"splide__pagination"});Xe(n,t)}createNavigation(n){}appendToDOM(n){const t=an("#content .container");t&&rr(t,n)}cleanup(){this.container&&(bt(this.container),delete this.container)}getContainer(){return this.container}removeExistingNavigation(){const n=an(`#${W.slider.dom.containerId}`);n&&bt(n);const t=nr(".item-nav");for(const r of t)bt(r)}}class ur{constructor(){this.maxSlides=W.slider.maxSlides}fetchSlideData(){const n=[];for(let t=ft.INITIAL_SLIDE_INDEX;t<=this.maxSlides;t+=ft.SLIDE_INCREMENT){const r=this.getForumAttribute(`wusong8899-header-advertisement.Image${t}`),i=this.getForumAttribute(`wusong8899-header-advertisement.Link${t}`);r&&n.push({imageSrc:String(r),imageLink:String(i||""),slideIndex:t})}return this.processSlideData(n)}validateSlideData(n){const t=[];if(!Array.isArray(n))return t.push("Slide data must be an array"),{isValid:!1,errors:t};if(n.length===te.EMPTY_LENGTH)return t.push("No slide data provided"),{isValid:!1,errors:t};for(const r of n)(!r.imageSrc||typeof r.imageSrc!="string")&&t.push(`Invalid image source for slide ${r.slideIndex}`),r.imageLink&&typeof r.imageLink!="string"&&t.push(`Invalid image link for slide ${r.slideIndex}`),(typeof r.slideIndex!="number"||r.slideIndex<ft.INITIAL_SLIDE_INDEX)&&t.push(`Invalid slide index: ${r.slideIndex}`),r.imageSrc&&!this.isValidUrl(r.imageSrc)&&t.push(`Invalid image URL format for slide ${r.slideIndex}`),r.imageLink&&!this.isValidUrl(r.imageLink)&&t.push(`Invalid link URL format for slide ${r.slideIndex}`);return{isValid:t.length===te.EMPTY_LENGTH,errors:t}}getTransitionTime(){const n=this.getForumAttribute("wusong8899-header-advertisement.TransitionTime");if(n){const t=Number.parseInt(String(n),10);if(!Number.isNaN(t)&&t>te.EMPTY_LENGTH)return t}return W.slider.defaultTransitionTime}processSlideData(n){return n.map(t=>({imageSrc:t.imageSrc,imageLink:t.imageLink,slideIndex:t.slideIndex,isValid:this.isSlideValid(t)})).filter(t=>t.isValid)}isSlideValid(n){return!!(n.imageSrc&&typeof n.imageSrc=="string"&&this.isValidUrl(n.imageSrc)&&typeof n.slideIndex=="number"&&n.slideIndex>te.EMPTY_LENGTH)}isValidUrl(n){try{return!!new URL(n)}catch{return n.startsWith("/")||n.startsWith("./")||n.startsWith("data:")}}getForumAttribute(n){try{const t=pe&&pe.forum,r=t&&t.attribute;return typeof r=="function"?r.call(t,n):void 0}catch{return}}}function cr(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function lr(e,n,t){return n&&cr(e.prototype,n),Object.defineProperty(e,"prototype",{writable:!1}),e}/*!
 * Splide.js
 * Version  : 4.1.4
 * License  : MIT
 * Copyright: 2022 Naotoshi Fujita
 */var cn="(prefers-reduced-motion: reduce)",we=1,fr=2,Me=3,xe=4,We=5,Et=6,gt=7,dr={CREATED:we,MOUNTED:fr,IDLE:Me,MOVING:xe,SCROLLING:We,DRAGGING:Et,DESTROYED:gt};function fe(e){e.length=0}function he(e,n,t){return Array.prototype.slice.call(e,n,t)}function U(e){return e.bind.apply(e,[null].concat(he(arguments,1)))}var ln=setTimeout,wt=function(){};function fn(e){return requestAnimationFrame(e)}function ht(e,n){return typeof n===e}function Ye(e){return!xt(e)&&ht("object",e)}var Mt=Array.isArray,dn=U(ht,"function"),me=U(ht,"string"),$e=U(ht,"undefined");function xt(e){return e===null}function vn(e){try{return e instanceof(e.ownerDocument.defaultView||window).HTMLElement}catch{return!1}}function Ke(e){return Mt(e)?e:[e]}function ne(e,n){Ke(e).forEach(n)}function Vt(e,n){return e.indexOf(n)>-1}function mt(e,n){return e.push.apply(e,Ke(n)),e}function de(e,n,t){e&&ne(n,function(r){r&&e.classList[t?"add":"remove"](r)})}function ue(e,n){de(e,me(n)?n.split(" "):n,!0)}function qe(e,n){ne(n,e.appendChild.bind(e))}function Gt(e,n){ne(e,function(t){var r=(n||t).parentNode;r&&r.insertBefore(t,n)})}function je(e,n){return vn(e)&&(e.msMatchesSelector||e.matches).call(e,n)}function En(e,n){var t=e?he(e.children):[];return n?t.filter(function(r){return je(r,n)}):t}function Ze(e,n){return n?En(e,n)[0]:e.firstElementChild}var Je=Object.keys;function Ne(e,n,t){return e&&(t?Je(e).reverse():Je(e)).forEach(function(r){r!=="__proto__"&&n(e[r],r)}),e}function Qe(e){return he(arguments,1).forEach(function(n){Ne(n,function(t,r){e[r]=n[r]})}),e}function _e(e){return he(arguments,1).forEach(function(n){Ne(n,function(t,r){Mt(t)?e[r]=t.slice():Ye(t)?e[r]=_e({},Ye(e[r])?e[r]:{},t):e[r]=t})}),e}function gn(e,n){ne(n||Je(e),function(t){delete e[t]})}function ce(e,n){ne(e,function(t){ne(n,function(r){t&&t.removeAttribute(r)})})}function M(e,n,t){Ye(n)?Ne(n,function(r,i){M(e,i,r)}):ne(e,function(r){xt(t)||t===""?ce(r,n):r.setAttribute(n,String(t))})}function Ve(e,n,t){var r=document.createElement(e);return n&&(me(n)?ue(r,n):M(r,n)),t&&qe(t,r),r}function ae(e,n,t){if($e(t))return getComputedStyle(e)[n];xt(t)||(e.style[n]=""+t)}function et(e,n){ae(e,"display",n)}function hn(e){e.setActive&&e.setActive()||e.focus({preventScroll:!0})}function oe(e,n){return e.getAttribute(n)}function mn(e,n){return e&&e.classList.contains(n)}function re(e){return e.getBoundingClientRect()}function Le(e){ne(e,function(n){n&&n.parentNode&&n.parentNode.removeChild(n)})}function _n(e){return Ze(new DOMParser().parseFromString(e,"text/html").body)}function ve(e,n){e.preventDefault(),n&&(e.stopPropagation(),e.stopImmediatePropagation())}function In(e,n){return e&&e.querySelector(n)}function kt(e,n){return n?he(e.querySelectorAll(n)):[]}function Ee(e,n){de(e,n,!1)}function Ft(e){return e.timeStamp}function Re(e){return me(e)?e:e?e+"px":""}var tt="splide",Ut="data-"+tt;function nt(e,n){if(!e)throw new Error("["+tt+"] "+(n||""))}var Ie=Math.min,_t=Math.max,It=Math.floor,rt=Math.ceil,J=Math.abs;function Tn(e,n,t){return J(e-n)<t}function Tt(e,n,t,r){var i=Ie(n,t),o=_t(n,t);return r?i<e&&e<o:i<=e&&e<=o}function Ge(e,n,t){var r=Ie(n,t),i=_t(n,t);return Ie(_t(r,e),i)}function Ht(e){return+(e>0)-+(e<0)}function Bt(e,n){return ne(n,function(t){e=e.replace("%s",""+t)}),e}function zt(e){return e<10?"0"+e:""+e}var Sn={};function vr(e){return""+e+zt(Sn[e]=(Sn[e]||0)+1)}function An(){var e=[];function n(a,c,l,d){i(a,c,function(s,m,v){var E="addEventListener"in s,u=E?s.removeEventListener.bind(s,m,l,d):s.removeListener.bind(s,l);E?s.addEventListener(m,l,d):s.addListener(l),e.push([s,m,v,l,u])})}function t(a,c,l){i(a,c,function(d,s,m){e=e.filter(function(v){return v[0]===d&&v[1]===s&&v[2]===m&&(!l||v[3]===l)?(v[4](),!1):!0})})}function r(a,c,l){var d,s=!0;return typeof CustomEvent=="function"?d=new CustomEvent(c,{bubbles:s,detail:l}):(d=document.createEvent("CustomEvent"),d.initCustomEvent(c,s,!1,l)),a.dispatchEvent(d),d}function i(a,c,l){ne(a,function(d){d&&ne(c,function(s){s.split(" ").forEach(function(m){var v=m.split(".");l(d,v[0],v[1])})})})}function o(){e.forEach(function(a){a[4]()}),fe(e)}return{bind:n,unbind:t,dispatch:r,destroy:o}}var De="mounted",pn="ready",Te="move",it="moved",yn="click",Er="active",gr="inactive",hr="visible",mr="hidden",K="refresh",Q="updated",at="resize",Xt="resized",_r="drag",Ir="dragging",Tr="dragged",Wt="scroll",ke="scrolled",Sr="overflow",Nn="destroy",Ar="arrows:mounted",pr="arrows:updated",yr="pagination:mounted",Nr="pagination:updated",Ln="navigation:mounted",Rn="autoplay:play",Lr="autoplay:playing",Dn="autoplay:pause",Cn="lazyload:loaded",On="sk",Pn="sh",St="ei";function z(e){var n=e?e.event.bus:document.createDocumentFragment(),t=An();function r(o,a){t.bind(n,Ke(o).join(" "),function(c){a.apply(a,Mt(c.detail)?c.detail:[])})}function i(o){t.dispatch(n,o,he(arguments,1))}return e&&e.event.on(Nn,t.destroy),Qe(t,{bus:n,on:r,off:U(t.unbind,n),emit:i})}function At(e,n,t,r){var i=Date.now,o,a=0,c,l=!0,d=0;function s(){if(!l){if(a=e?Ie((i()-o)/e,1):1,t&&t(a),a>=1&&(n(),o=i(),r&&++d>=r))return v();c=fn(s)}}function m(_){_||u(),o=i()-(_?a*e:0),l=!1,c=fn(s)}function v(){l=!0}function E(){o=i(),a=0,t&&t(a)}function u(){c&&cancelAnimationFrame(c),a=0,c=0,l=!0}function f(_){e=_}function I(){return l}return{start:m,rewind:E,pause:v,cancel:u,set:f,isPaused:I}}function Rr(e){var n=e;function t(i){n=i}function r(i){return Vt(Ke(i),n)}return{set:t,is:r}}function Dr(e,n){var t=At(0,e,null,1);return function(){t.isPaused()&&t.start()}}function Cr(e,n,t){var r=e.state,i=t.breakpoints||{},o=t.reducedMotion||{},a=An(),c=[];function l(){var u=t.mediaQuery==="min";Je(i).sort(function(f,I){return u?+f-+I:+I-+f}).forEach(function(f){s(i[f],"("+(u?"min":"max")+"-width:"+f+"px)")}),s(o,cn),m()}function d(u){u&&a.destroy()}function s(u,f){var I=matchMedia(f);a.bind(I,"change",m),c.push([u,I])}function m(){var u=r.is(gt),f=t.direction,I=c.reduce(function(_,h){return _e(_,h[1].matches?h[0]:{})},{});gn(t),E(I),t.destroy?e.destroy(t.destroy==="completely"):u?(d(!0),e.mount()):f!==t.direction&&e.refresh()}function v(u){matchMedia(cn).matches&&(u?_e(t,o):gn(t,Je(o)))}function E(u,f,I){_e(t,u),f&&_e(Object.getPrototypeOf(t),u),(I||!r.is(we))&&e.emit(Q,t)}return{setup:l,destroy:d,reduce:v,set:E}}var pt="Arrow",yt=pt+"Left",Nt=pt+"Right",bn=pt+"Up",wn=pt+"Down",Mn="rtl",Lt="ttb",Yt={width:["height"],left:["top","right"],right:["bottom","left"],x:["y"],X:["Y"],Y:["X"],ArrowLeft:[bn,Nt],ArrowRight:[wn,yt]};function Or(e,n,t){function r(o,a,c){c=c||t.direction;var l=c===Mn&&!a?1:c===Lt?0:-1;return Yt[o]&&Yt[o][l]||o.replace(/width|left|right/i,function(d,s){var m=Yt[d.toLowerCase()][l]||d;return s>0?m.charAt(0).toUpperCase()+m.slice(1):m})}function i(o){return o*(t.direction===Mn?1:-1)}return{resolve:r,orient:i}}var ge="role",Fe="tabindex",Pr="disabled",se="aria-",ot=se+"controls",xn=se+"current",Vn=se+"selected",ie=se+"label",$t=se+"labelledby",Gn=se+"hidden",Kt=se+"orientation",st=se+"roledescription",kn=se+"live",Fn=se+"busy",Un=se+"atomic",qt=[ge,Fe,Pr,ot,xn,ie,$t,Gn,Kt,st],le=tt+"__",Se="is-",jt=tt,Hn=le+"track",br=le+"list",Rt=le+"slide",Bn=Rt+"--clone",wr=Rt+"__container",Zt=le+"arrows",Dt=le+"arrow",zn=Dt+"--prev",Xn=Dt+"--next",Ct=le+"pagination",Wn=Ct+"__page",Mr=le+"progress",xr=Mr+"__bar",Vr=le+"toggle",Gr=le+"spinner",kr=le+"sr",Fr=Se+"initialized",Ce=Se+"active",Yn=Se+"prev",$n=Se+"next",Jt=Se+"visible",Qt=Se+"loading",Kn=Se+"focus-in",qn=Se+"overflow",Ur=[Ce,Jt,Yn,$n,Qt,Kn,qn],Hr={slide:Rt,clone:Bn,arrows:Zt,arrow:Dt,prev:zn,next:Xn,pagination:Ct,page:Wn,spinner:Gr};function Br(e,n){if(dn(e.closest))return e.closest(n);for(var t=e;t&&t.nodeType===1&&!je(t,n);)t=t.parentElement;return t}var zr=5,jn=200,Zn="touchstart mousedown",en="touchmove mousemove",tn="touchend touchcancel mouseup click";function Xr(e,n,t){var r=z(e),i=r.on,o=r.bind,a=e.root,c=t.i18n,l={},d=[],s=[],m=[],v,E,u;function f(){g(),b(),h()}function I(){i(K,_),i(K,f),i(Q,h),o(document,Zn+" keydown",function(S){u=S.type==="keydown"},{capture:!0}),o(a,"focusin",function(){de(a,Kn,!!u)})}function _(S){var D=qt.concat("style");fe(d),Ee(a,s),Ee(v,m),ce([v,E],D),ce(a,S?D:["style",st])}function h(){Ee(a,s),Ee(v,m),s=x(jt),m=x(Hn),ue(a,s),ue(v,m),M(a,ie,t.label),M(a,$t,t.labelledby)}function g(){v=R("."+Hn),E=Ze(v,"."+br),nt(v&&E,"A track/list element is missing."),mt(d,En(E,"."+Rt+":not(."+Bn+")")),Ne({arrows:Zt,pagination:Ct,prev:zn,next:Xn,bar:xr,toggle:Vr},function(S,D){l[D]=R("."+S)}),Qe(l,{root:a,track:v,list:E,slides:d})}function b(){var S=a.id||vr(tt),D=t.role;a.id=S,v.id=v.id||S+"-track",E.id=E.id||S+"-list",!oe(a,ge)&&a.tagName!=="SECTION"&&D&&M(a,ge,D),M(a,st,c.carousel),M(E,ge,"presentation")}function R(S){var D=In(a,S);return D&&Br(D,"."+jt)===a?D:void 0}function x(S){return[S+"--"+t.type,S+"--"+t.direction,t.drag&&S+"--draggable",t.isNavigation&&S+"--nav",S===jt&&Ce]}return Qe(l,{setup:f,mount:I,destroy:_})}var Ue="slide",He="loop",ut="fade";function Wr(e,n,t,r){var i=z(e),o=i.on,a=i.emit,c=i.bind,l=e.Components,d=e.root,s=e.options,m=s.isNavigation,v=s.updateOnMove,E=s.i18n,u=s.pagination,f=s.slideFocus,I=l.Direction.resolve,_=oe(r,"style"),h=oe(r,ie),g=t>-1,b=Ze(r,"."+wr),R;function x(){g||(r.id=d.id+"-slide"+zt(n+1),M(r,ge,u?"tabpanel":"group"),M(r,st,E.slide),M(r,ie,h||Bt(E.slideLabel,[n+1,e.length]))),S()}function S(){c(r,"click",U(a,yn,w)),c(r,"keydown",U(a,On,w)),o([it,Pn,ke],p),o(Ln,k),v&&o(Te,P)}function D(){R=!0,i.destroy(),Ee(r,Ur),ce(r,qt),M(r,"style",_),M(r,ie,h||"")}function k(){var O=e.splides.map(function(A){var C=A.splide.Components.Slides.getAt(n);return C?C.slide.id:""}).join(" ");M(r,ie,Bt(E.slideX,(g?t:n)+1)),M(r,ot,O),M(r,ge,f?"button":""),f&&ce(r,st)}function P(){R||p()}function p(){if(!R){var O=e.index;y(),N(),de(r,Yn,n===O-1),de(r,$n,n===O+1)}}function y(){var O=G();O!==mn(r,Ce)&&(de(r,Ce,O),M(r,xn,m&&O||""),a(O?Er:gr,w))}function N(){var O=Y(),A=!O&&(!G()||g);if(e.state.is([xe,We])||M(r,Gn,A||""),M(kt(r,s.focusableNodes||""),Fe,A?-1:""),f&&M(r,Fe,A?-1:0),O!==mn(r,Jt)&&(de(r,Jt,O),a(O?hr:mr,w)),!O&&document.activeElement===r){var C=l.Slides.getAt(e.index);C&&hn(C.slide)}}function V(O,A,C){ae(C&&b||r,O,A)}function G(){var O=e.index;return O===n||s.cloneStatus&&O===t}function Y(){if(e.is(ut))return G();var O=re(l.Elements.track),A=re(r),C=I("left",!0),F=I("right",!0);return It(O[C])<=rt(A[C])&&It(A[F])<=rt(O[F])}function X(O,A){var C=J(O-n);return!g&&(s.rewind||e.is(He))&&(C=Ie(C,e.length-C)),C<=A}var w={index:n,slideIndex:t,slide:r,container:b,isClone:g,mount:x,destroy:D,update:p,style:V,isWithin:X};return w}function Yr(e,n,t){var r=z(e),i=r.on,o=r.emit,a=r.bind,c=n.Elements,l=c.slides,d=c.list,s=[];function m(){v(),i(K,E),i(K,v)}function v(){l.forEach(function(p,y){f(p,y,-1)})}function E(){R(function(p){p.destroy()}),fe(s)}function u(){R(function(p){p.update()})}function f(p,y,N){var V=Wr(e,y,N,p);V.mount(),s.push(V),s.sort(function(G,Y){return G.index-Y.index})}function I(p){return p?x(function(y){return!y.isClone}):s}function _(p){var y=n.Controller,N=y.toIndex(p),V=y.hasFocus()?1:t.perPage;return x(function(G){return Tt(G.index,N,N+V-1)})}function h(p){return x(p)[0]}function g(p,y){ne(p,function(N){if(me(N)&&(N=_n(N)),vn(N)){var V=l[y];V?Gt(N,V):qe(d,N),ue(N,t.classes.slide),D(N,U(o,at))}}),o(K)}function b(p){Le(x(p).map(function(y){return y.slide})),o(K)}function R(p,y){I(y).forEach(p)}function x(p){return s.filter(dn(p)?p:function(y){return me(p)?je(y.slide,p):Vt(Ke(p),y.index)})}function S(p,y,N){R(function(V){V.style(p,y,N)})}function D(p,y){var N=kt(p,"img"),V=N.length;V?N.forEach(function(G){a(G,"load error",function(){--V||y()})}):y()}function k(p){return p?l.length:s.length}function P(){return s.length>t.perPage}return{mount:m,destroy:E,update:u,register:f,get:I,getIn:_,getAt:h,add:g,remove:b,forEach:R,filter:x,style:S,getLength:k,isEnough:P}}function $r(e,n,t){var r=z(e),i=r.on,o=r.bind,a=r.emit,c=n.Slides,l=n.Direction.resolve,d=n.Elements,s=d.root,m=d.track,v=d.list,E=c.getAt,u=c.style,f,I,_;function h(){g(),o(window,"resize load",Dr(U(a,at))),i([Q,K],g),i(at,b)}function g(){f=t.direction===Lt,ae(s,"maxWidth",Re(t.width)),ae(m,l("paddingLeft"),R(!1)),ae(m,l("paddingRight"),R(!0)),b(!0)}function b(w){var O=re(s);(w||I.width!==O.width||I.height!==O.height)&&(ae(m,"height",x()),u(l("marginRight"),Re(t.gap)),u("width",D()),u("height",k(),!0),I=O,a(Xt),_!==(_=X())&&(de(s,qn,_),a(Sr,_)))}function R(w){var O=t.padding,A=l(w?"right":"left");return O&&Re(O[A]||(Ye(O)?0:O))||"0px"}function x(){var w="";return f&&(w=S(),nt(w,"height or heightRatio is missing."),w="calc("+w+" - "+R(!1)+" - "+R(!0)+")"),w}function S(){return Re(t.height||re(v).width*t.heightRatio)}function D(){return t.autoWidth?null:Re(t.fixedWidth)||(f?"":P())}function k(){return Re(t.fixedHeight)||(f?t.autoHeight?null:P():S())}function P(){var w=Re(t.gap);return"calc((100%"+(w&&" + "+w)+")/"+(t.perPage||1)+(w&&" - "+w)+")"}function p(){return re(v)[l("width")]}function y(w,O){var A=E(w||0);return A?re(A.slide)[l("width")]+(O?0:G()):0}function N(w,O){var A=E(w);if(A){var C=re(A.slide)[l("right")],F=re(v)[l("left")];return J(C-F)+(O?0:G())}return 0}function V(w){return N(e.length-1)-N(0)+y(0,w)}function G(){var w=E(0);return w&&parseFloat(ae(w.slide,l("marginRight")))||0}function Y(w){return parseFloat(ae(m,l("padding"+(w?"Right":"Left"))))||0}function X(){return e.is(ut)||V(!0)>p()}return{mount:h,resize:b,listSize:p,slideSize:y,sliderSize:V,totalSize:N,getPadding:Y,isOverflow:X}}var Kr=2;function qr(e,n,t){var r=z(e),i=r.on,o=n.Elements,a=n.Slides,c=n.Direction.resolve,l=[],d;function s(){i(K,m),i([Q,at],E),(d=I())&&(u(d),n.Layout.resize(!0))}function m(){v(),s()}function v(){Le(l),fe(l),r.destroy()}function E(){var _=I();d!==_&&(d<_||!_)&&r.emit(K)}function u(_){var h=a.get().slice(),g=h.length;if(g){for(;h.length<_;)mt(h,h);mt(h.slice(-_),h.slice(0,_)).forEach(function(b,R){var x=R<_,S=f(b.slide,R);x?Gt(S,h[0].slide):qe(o.list,S),mt(l,S),a.register(S,R-_+(x?0:g),b.index)})}}function f(_,h){var g=_.cloneNode(!0);return ue(g,t.classes.clone),g.id=e.root.id+"-clone"+zt(h+1),g}function I(){var _=t.clones;if(!e.is(He))_=0;else if($e(_)){var h=t[c("fixedWidth")]&&n.Layout.slideSize(0),g=h&&rt(re(o.track)[c("width")]/h);_=g||t[c("autoWidth")]&&e.length||t.perPage*Kr}return _}return{mount:s,destroy:v}}function jr(e,n,t){var r=z(e),i=r.on,o=r.emit,a=e.state.set,c=n.Layout,l=c.slideSize,d=c.getPadding,s=c.totalSize,m=c.listSize,v=c.sliderSize,E=n.Direction,u=E.resolve,f=E.orient,I=n.Elements,_=I.list,h=I.track,g;function b(){g=n.Transition,i([De,Xt,Q,K],R)}function R(){n.Controller.isBusy()||(n.Scroll.cancel(),S(e.index),n.Slides.update())}function x(A,C,F,j){A!==C&&w(A>F)&&(p(),D(P(V(),A>F),!0)),a(xe),o(Te,C,F,A),g.start(C,function(){a(Me),o(it,C,F,A),j&&j()})}function S(A){D(N(A,!0))}function D(A,C){if(!e.is(ut)){var F=C?A:k(A);ae(_,"transform","translate"+u("X")+"("+F+"px)"),A!==F&&o(Pn)}}function k(A){if(e.is(He)){var C=y(A),F=C>n.Controller.getEnd(),j=C<0;(j||F)&&(A=P(A,F))}return A}function P(A,C){var F=A-X(C),j=v();return A-=f(j*(rt(J(F)/j)||1))*(C?1:-1),A}function p(){D(V(),!0),g.cancel()}function y(A){for(var C=n.Slides.get(),F=0,j=1/0,q=0;q<C.length;q++){var Ae=C[q].index,T=J(N(Ae,!0)-A);if(T<=j)j=T,F=Ae;else break}return F}function N(A,C){var F=f(s(A-1)-Y(A));return C?G(F):F}function V(){var A=u("left");return re(_)[A]-re(h)[A]+f(d(!1))}function G(A){return t.trimSpace&&e.is(Ue)&&(A=Ge(A,0,f(v(!0)-m()))),A}function Y(A){var C=t.focus;return C==="center"?(m()-l(A,!0))/2:+C*l(A)||0}function X(A){return N(A?n.Controller.getEnd():0,!!t.trimSpace)}function w(A){var C=f(P(V(),A));return A?C>=0:C<=_[u("scrollWidth")]-re(h)[u("width")]}function O(A,C){C=$e(C)?V():C;var F=A!==!0&&f(C)<f(X(!1)),j=A!==!1&&f(C)>f(X(!0));return F||j}return{mount:b,move:x,jump:S,translate:D,shift:P,cancel:p,toIndex:y,toPosition:N,getPosition:V,getLimit:X,exceededLimit:O,reposition:R}}function Zr(e,n,t){var r=z(e),i=r.on,o=r.emit,a=n.Move,c=a.getPosition,l=a.getLimit,d=a.toPosition,s=n.Slides,m=s.isEnough,v=s.getLength,E=t.omitEnd,u=e.is(He),f=e.is(Ue),I=U(V,!1),_=U(V,!0),h=t.start||0,g,b=h,R,x,S;function D(){k(),i([Q,K,St],k),i(Xt,P)}function k(){R=v(!0),x=t.perMove,S=t.perPage,g=w();var T=Ge(h,0,E?g:R-1);T!==h&&(h=T,a.reposition())}function P(){g!==w()&&o(St)}function p(T,H,ee){if(!Ae()){var $=N(T),Z=X($);Z>-1&&(H||Z!==h)&&(F(Z),a.move($,Z,b,ee))}}function y(T,H,ee,$){n.Scroll.scroll(T,H,ee,function(){var Z=X(a.toIndex(c()));F(E?Ie(Z,g):Z),$&&$()})}function N(T){var H=h;if(me(T)){var ee=T.match(/([+\-<>])(\d+)?/)||[],$=ee[1],Z=ee[2];$==="+"||$==="-"?H=G(h+ +(""+$+(+Z||1)),h):$===">"?H=Z?O(+Z):I(!0):$==="<"&&(H=_(!0))}else H=u?T:Ge(T,0,g);return H}function V(T,H){var ee=x||(q()?1:S),$=G(h+ee*(T?-1:1),h,!(x||q()));return $===-1&&f&&!Tn(c(),l(!T),1)?T?0:g:H?$:X($)}function G(T,H,ee){if(m()||q()){var $=Y(T);$!==T&&(H=T,T=$,ee=!1),T<0||T>g?!x&&(Tt(0,T,H,!0)||Tt(g,H,T,!0))?T=O(A(T)):u?T=ee?T<0?-(R%S||S):R:T:t.rewind?T=T<0?g:0:T=-1:ee&&T!==H&&(T=O(A(H)+(T<H?-1:1)))}else T=-1;return T}function Y(T){if(f&&t.trimSpace==="move"&&T!==h)for(var H=c();H===d(T,!0)&&Tt(T,0,e.length-1,!t.rewind);)T<h?--T:++T;return T}function X(T){return u?(T+R)%R||0:T}function w(){for(var T=R-(q()||u&&x?1:S);E&&T-- >0;)if(d(R-1,!0)!==d(T,!0)){T++;break}return Ge(T,0,R-1)}function O(T){return Ge(q()?T:S*T,0,g)}function A(T){return q()?Ie(T,g):It((T>=g?R-1:T)/S)}function C(T){var H=a.toIndex(T);return f?Ge(H,0,g):H}function F(T){T!==h&&(b=h,h=T)}function j(T){return T?b:h}function q(){return!$e(t.focus)||t.isNavigation}function Ae(){return e.state.is([xe,We])&&!!t.waitForTransition}return{mount:D,go:p,scroll:y,getNext:I,getPrev:_,getAdjacent:V,getEnd:w,setIndex:F,getIndex:j,toIndex:O,toPage:A,toDest:C,hasFocus:q,isBusy:Ae}}var Jr="http://www.w3.org/2000/svg",Qr="m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z",Ot=40;function ei(e,n,t){var r=z(e),i=r.on,o=r.bind,a=r.emit,c=t.classes,l=t.i18n,d=n.Elements,s=n.Controller,m=d.arrows,v=d.track,E=m,u=d.prev,f=d.next,I,_,h={};function g(){R(),i(Q,b)}function b(){x(),g()}function R(){var y=t.arrows;y&&!(u&&f)&&k(),u&&f&&(Qe(h,{prev:u,next:f}),et(E,y?"":"none"),ue(E,_=Zt+"--"+t.direction),y&&(S(),p(),M([u,f],ot,v.id),a(Ar,u,f)))}function x(){r.destroy(),Ee(E,_),I?(Le(m?[u,f]:E),u=f=null):ce([u,f],qt)}function S(){i([De,it,K,ke,St],p),o(f,"click",U(D,">")),o(u,"click",U(D,"<"))}function D(y){s.go(y,!0)}function k(){E=m||Ve("div",c.arrows),u=P(!0),f=P(!1),I=!0,qe(E,[u,f]),!m&&Gt(E,v)}function P(y){var N='<button class="'+c.arrow+" "+(y?c.prev:c.next)+'" type="button"><svg xmlns="'+Jr+'" viewBox="0 0 '+Ot+" "+Ot+'" width="'+Ot+'" height="'+Ot+'" focusable="false"><path d="'+(t.arrowPath||Qr)+'" />';return _n(N)}function p(){if(u&&f){var y=e.index,N=s.getPrev(),V=s.getNext(),G=N>-1&&y<N?l.last:l.prev,Y=V>-1&&y>V?l.first:l.next;u.disabled=N<0,f.disabled=V<0,M(u,ie,G),M(f,ie,Y),a(pr,u,f,N,V)}}return{arrows:h,mount:g,destroy:x,update:p}}var ti=Ut+"-interval";function ni(e,n,t){var r=z(e),i=r.on,o=r.bind,a=r.emit,c=At(t.interval,e.go.bind(e,">"),S),l=c.isPaused,d=n.Elements,s=n.Elements,m=s.root,v=s.toggle,E=t.autoplay,u,f,I=E==="pause";function _(){E&&(h(),v&&M(v,ot,d.track.id),I||g(),x())}function h(){t.pauseOnHover&&o(m,"mouseenter mouseleave",function(k){u=k.type==="mouseenter",R()}),t.pauseOnFocus&&o(m,"focusin focusout",function(k){f=k.type==="focusin",R()}),v&&o(v,"click",function(){I?g():b(!0)}),i([Te,Wt,K],c.rewind),i(Te,D)}function g(){l()&&n.Slides.isEnough()&&(c.start(!t.resetProgress),f=u=I=!1,x(),a(Rn))}function b(k){k===void 0&&(k=!0),I=!!k,x(),l()||(c.pause(),a(Dn))}function R(){I||(u||f?b(!1):g())}function x(){v&&(de(v,Ce,!I),M(v,ie,t.i18n[I?"play":"pause"]))}function S(k){var P=d.bar;P&&ae(P,"width",k*100+"%"),a(Lr,k)}function D(k){var P=n.Slides.getAt(k);c.set(P&&+oe(P.slide,ti)||t.interval)}return{mount:_,destroy:c.cancel,play:g,pause:b,isPaused:l}}function ri(e,n,t){var r=z(e),i=r.on;function o(){t.cover&&(i(Cn,U(c,!0)),i([De,Q,K],U(a,!0)))}function a(l){n.Slides.forEach(function(d){var s=Ze(d.container||d.slide,"img");s&&s.src&&c(l,s,d)})}function c(l,d,s){s.style("background",l?'center/cover no-repeat url("'+d.src+'")':"",!0),et(d,l?"none":"")}return{mount:o,destroy:U(a,!1)}}var ii=10,ai=600,oi=.6,si=1.5,ui=800;function ci(e,n,t){var r=z(e),i=r.on,o=r.emit,a=e.state.set,c=n.Move,l=c.getPosition,d=c.getLimit,s=c.exceededLimit,m=c.translate,v=e.is(Ue),E,u,f=1;function I(){i(Te,b),i([Q,K],R)}function _(S,D,k,P,p){var y=l();if(b(),k&&(!v||!s())){var N=n.Layout.sliderSize(),V=Ht(S)*N*It(J(S)/N)||0;S=c.toPosition(n.Controller.toDest(S%N))+V}var G=Tn(y,S,1);f=1,D=G?0:D||_t(J(S-y)/si,ui),u=P,E=At(D,h,U(g,y,S,p),1),a(We),o(Wt),E.start()}function h(){a(Me),u&&u(),o(ke)}function g(S,D,k,P){var p=l(),y=S+(D-S)*x(P),N=(y-p)*f;m(p+N),v&&!k&&s()&&(f*=oi,J(N)<ii&&_(d(s(!0)),ai,!1,u,!0))}function b(){E&&E.cancel()}function R(){E&&!E.isPaused()&&(b(),h())}function x(S){var D=t.easingFunc;return D?D(S):1-Math.pow(1-S,4)}return{mount:I,destroy:b,scroll:_,cancel:R}}var Be={passive:!1,capture:!0};function li(e,n,t){var r=z(e),i=r.on,o=r.emit,a=r.bind,c=r.unbind,l=e.state,d=n.Move,s=n.Scroll,m=n.Controller,v=n.Elements.track,E=n.Media.reduce,u=n.Direction,f=u.resolve,I=u.orient,_=d.getPosition,h=d.exceededLimit,g,b,R,x,S,D=!1,k,P,p;function y(){a(v,en,wt,Be),a(v,tn,wt,Be),a(v,Zn,V,Be),a(v,"click",X,{capture:!0}),a(v,"dragstart",ve),i([De,Q],N)}function N(){var L=t.drag;Qn(!L),x=L==="free"}function V(L){if(k=!1,!P){var B=Z(L);$(L.target)&&(B||!L.button)&&(m.isBusy()?ve(L,!0):(p=B?v:window,S=l.is([xe,We]),R=null,a(p,en,G,Be),a(p,tn,Y,Be),d.cancel(),s.cancel(),w(L)))}}function G(L){if(l.is(Et)||(l.set(Et),o(_r)),L.cancelable)if(S){d.translate(g+ee(q(L)));var B=Ae(L)>jn,Pe=D!==(D=h());(B||Pe)&&w(L),k=!0,o(Ir),ve(L)}else C(L)&&(S=A(L),ve(L))}function Y(L){l.is(Et)&&(l.set(Me),o(Tr)),S&&(O(L),ve(L)),c(p,en,G),c(p,tn,Y),S=!1}function X(L){!P&&k&&ve(L,!0)}function w(L){R=b,b=L,g=_()}function O(L){var B=F(L),Pe=j(B),lt=t.rewind&&t.rewindByDrag;E(!1),x?m.scroll(Pe,0,t.snap):e.is(ut)?m.go(I(Ht(B))<0?lt?"<":"-":lt?">":"+"):e.is(Ue)&&D&&lt?m.go(h(!0)?">":"<"):m.go(m.toDest(Pe),!0),E(!0)}function A(L){var B=t.dragMinThreshold,Pe=Ye(B),lt=Pe&&B.mouse||0,bi=(Pe?B.touch:+B)||10;return J(q(L))>(Z(L)?bi:lt)}function C(L){return J(q(L))>J(q(L,!0))}function F(L){if(e.is(He)||!D){var B=Ae(L);if(B&&B<jn)return q(L)/B}return 0}function j(L){return _()+Ht(L)*Ie(J(L)*(t.flickPower||600),x?1/0:n.Layout.listSize()*(t.flickMaxPages||1))}function q(L,B){return H(L,B)-H(T(L),B)}function Ae(L){return Ft(L)-Ft(T(L))}function T(L){return b===L&&R||b}function H(L,B){return(Z(L)?L.changedTouches[0]:L)["page"+f(B?"Y":"X")]}function ee(L){return L/(D&&e.is(Ue)?zr:1)}function $(L){var B=t.noDrag;return!je(L,"."+Wn+", ."+Dt)&&(!B||!je(L,B))}function Z(L){return typeof TouchEvent<"u"&&L instanceof TouchEvent}function Pi(){return S}function Qn(L){P=L}return{mount:y,disable:Qn,isDragging:Pi}}var fi={Spacebar:" ",Right:Nt,Left:yt,Up:bn,Down:wn};function nn(e){return e=me(e)?e:e.key,fi[e]||e}var Jn="keydown";function di(e,n,t){var r=z(e),i=r.on,o=r.bind,a=r.unbind,c=e.root,l=n.Direction.resolve,d,s;function m(){v(),i(Q,E),i(Q,v),i(Te,f)}function v(){var _=t.keyboard;_&&(d=_==="global"?window:c,o(d,Jn,I))}function E(){a(d,Jn)}function u(_){s=_}function f(){var _=s;s=!0,ln(function(){s=_})}function I(_){if(!s){var h=nn(_);h===l(yt)?e.go("<"):h===l(Nt)&&e.go(">")}}return{mount:m,destroy:E,disable:u}}var ct=Ut+"-lazy",Pt=ct+"-srcset",vi="["+ct+"], ["+Pt+"]";function Ei(e,n,t){var r=z(e),i=r.on,o=r.off,a=r.bind,c=r.emit,l=t.lazyLoad==="sequential",d=[it,ke],s=[];function m(){t.lazyLoad&&(v(),i(K,v))}function v(){fe(s),E(),l?_():(o(d),i(d,u),u())}function E(){n.Slides.forEach(function(h){kt(h.slide,vi).forEach(function(g){var b=oe(g,ct),R=oe(g,Pt);if(b!==g.src||R!==g.srcset){var x=t.classes.spinner,S=g.parentElement,D=Ze(S,"."+x)||Ve("span",x,S);s.push([g,h,D]),g.src||et(g,"none")}})})}function u(){s=s.filter(function(h){var g=t.perPage*((t.preloadPages||1)+1)-1;return h[1].isWithin(e.index,g)?f(h):!0}),s.length||o(d)}function f(h){var g=h[0];ue(h[1].slide,Qt),a(g,"load error",U(I,h)),M(g,"src",oe(g,ct)),M(g,"srcset",oe(g,Pt)),ce(g,ct),ce(g,Pt)}function I(h,g){var b=h[0],R=h[1];Ee(R.slide,Qt),g.type!=="error"&&(Le(h[2]),et(b,""),c(Cn,b,R),c(at)),l&&_()}function _(){s.length&&f(s.shift())}return{mount:m,destroy:U(fe,s),check:u}}function gi(e,n,t){var r=z(e),i=r.on,o=r.emit,a=r.bind,c=n.Slides,l=n.Elements,d=n.Controller,s=d.hasFocus,m=d.getIndex,v=d.go,E=n.Direction.resolve,u=l.pagination,f=[],I,_;function h(){g(),i([Q,K,St],h);var P=t.pagination;u&&et(u,P?"":"none"),P&&(i([Te,Wt,ke],k),b(),k(),o(yr,{list:I,items:f},D(e.index)))}function g(){I&&(Le(u?he(I.children):I),Ee(I,_),fe(f),I=null),r.destroy()}function b(){var P=e.length,p=t.classes,y=t.i18n,N=t.perPage,V=s()?d.getEnd()+1:rt(P/N);I=u||Ve("ul",p.pagination,l.track.parentElement),ue(I,_=Ct+"--"+S()),M(I,ge,"tablist"),M(I,ie,y.select),M(I,Kt,S()===Lt?"vertical":"");for(var G=0;G<V;G++){var Y=Ve("li",null,I),X=Ve("button",{class:p.page,type:"button"},Y),w=c.getIn(G).map(function(A){return A.slide.id}),O=!s()&&N>1?y.pageX:y.slideX;a(X,"click",U(R,G)),t.paginationKeyboard&&a(X,"keydown",U(x,G)),M(Y,ge,"presentation"),M(X,ge,"tab"),M(X,ot,w.join(" ")),M(X,ie,Bt(O,G+1)),M(X,Fe,-1),f.push({li:Y,button:X,page:G})}}function R(P){v(">"+P,!0)}function x(P,p){var y=f.length,N=nn(p),V=S(),G=-1;N===E(Nt,!1,V)?G=++P%y:N===E(yt,!1,V)?G=(--P+y)%y:N==="Home"?G=0:N==="End"&&(G=y-1);var Y=f[G];Y&&(hn(Y.button),v(">"+G),ve(p,!0))}function S(){return t.paginationDirection||t.direction}function D(P){return f[d.toPage(P)]}function k(){var P=D(m(!0)),p=D(m());if(P){var y=P.button;Ee(y,Ce),ce(y,Vn),M(y,Fe,-1)}if(p){var N=p.button;ue(N,Ce),M(N,Vn,!0),M(N,Fe,"")}o(Nr,{list:I,items:f},P,p)}return{items:f,mount:h,destroy:g,getAt:D,update:k}}var hi=[" ","Enter"];function mi(e,n,t){var r=t.isNavigation,i=t.slideFocus,o=[];function a(){e.splides.forEach(function(u){u.isParent||(d(e,u.splide),d(u.splide,e))}),r&&s()}function c(){o.forEach(function(u){u.destroy()}),fe(o)}function l(){c(),a()}function d(u,f){var I=z(u);I.on(Te,function(_,h,g){f.go(f.is(He)?g:_)}),o.push(I)}function s(){var u=z(e),f=u.on;f(yn,v),f(On,E),f([De,Q],m),o.push(u),u.emit(Ln,e.splides)}function m(){M(n.Elements.list,Kt,t.direction===Lt?"vertical":"")}function v(u){e.go(u.index)}function E(u,f){Vt(hi,nn(f))&&(v(u),ve(f))}return{setup:U(n.Media.set,{slideFocus:$e(i)?r:i},!0),mount:a,destroy:c,remount:l}}function _i(e,n,t){var r=z(e),i=r.bind,o=0;function a(){t.wheel&&i(n.Elements.track,"wheel",c,Be)}function c(d){if(d.cancelable){var s=d.deltaY,m=s<0,v=Ft(d),E=t.wheelMinThreshold||0,u=t.wheelSleep||0;J(s)>E&&v-o>u&&(e.go(m?"<":">"),o=v),l(m)&&ve(d)}}function l(d){return!t.releaseWheel||e.state.is(xe)||n.Controller.getAdjacent(d)!==-1}return{mount:a}}var Ii=90;function Ti(e,n,t){var r=z(e),i=r.on,o=n.Elements.track,a=t.live&&!t.isNavigation,c=Ve("span",kr),l=At(Ii,U(s,!1));function d(){a&&(v(!n.Autoplay.isPaused()),M(o,Un,!0),c.textContent="…",i(Rn,U(v,!0)),i(Dn,U(v,!1)),i([it,ke],U(s,!0)))}function s(E){M(o,Fn,E),E?(qe(o,c),l.start()):(Le(c),l.cancel())}function m(){ce(o,[kn,Un,Fn]),Le(c)}function v(E){a&&M(o,kn,E?"off":"polite")}return{mount:d,disable:v,destroy:m}}var Si=Object.freeze({__proto__:null,Media:Cr,Direction:Or,Elements:Xr,Slides:Yr,Layout:$r,Clones:qr,Move:jr,Controller:Zr,Arrows:ei,Autoplay:ni,Cover:ri,Scroll:ci,Drag:li,Keyboard:di,LazyLoad:Ei,Pagination:gi,Sync:mi,Wheel:_i,Live:Ti}),Ai={prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay",carousel:"carousel",slide:"slide",select:"Select a slide to show",slideLabel:"%s of %s"},pi={type:"slide",role:"region",speed:400,perPage:1,cloneStatus:!0,arrows:!0,pagination:!0,paginationKeyboard:!0,interval:5e3,pauseOnHover:!0,pauseOnFocus:!0,resetProgress:!0,easing:"cubic-bezier(0.25, 1, 0.5, 1)",drag:!0,direction:"ltr",trimSpace:!0,focusableNodes:"a, button, textarea, input, select, iframe",live:!0,classes:Hr,i18n:Ai,reducedMotion:{speed:0,rewindSpeed:0,autoplay:"pause"}};function yi(e,n,t){var r=n.Slides;function i(){z(e).on([De,K],o)}function o(){r.forEach(function(c){c.style("transform","translateX(-"+100*c.index+"%)")})}function a(c,l){r.style("transition","opacity "+t.speed+"ms "+t.easing),ln(l)}return{mount:i,start:a,cancel:wt}}function Ni(e,n,t){var r=n.Move,i=n.Controller,o=n.Scroll,a=n.Elements.list,c=U(ae,a,"transition"),l;function d(){z(e).bind(a,"transitionend",function(E){E.target===a&&l&&(m(),l())})}function s(E,u){var f=r.toPosition(E,!0),I=r.getPosition(),_=v(E);J(f-I)>=1&&_>=1?t.useScroll?o.scroll(f,_,!1,u):(c("transform "+_+"ms "+t.easing),r.translate(f,!0),l=u):(r.jump(E),u())}function m(){c(""),o.cancel()}function v(E){var u=t.rewindSpeed;if(e.is(Ue)&&u){var f=i.getIndex(!0),I=i.getEnd();if(f===0&&E>=I||f>=I&&E===0)return u}return t.speed}return{mount:d,start:s,cancel:m}}var Li=(function(){function e(t,r){this.event=z(),this.Components={},this.state=Rr(we),this.splides=[],this._o={},this._E={};var i=me(t)?In(document,t):t;nt(i,i+" is invalid."),this.root=i,r=_e({label:oe(i,ie)||"",labelledby:oe(i,$t)||""},pi,e.defaults,r||{});try{_e(r,JSON.parse(oe(i,Ut)))}catch{nt(!1,"Invalid JSON")}this._o=Object.create(_e({},r))}var n=e.prototype;return n.mount=function(r,i){var o=this,a=this.state,c=this.Components;nt(a.is([we,gt]),"Already mounted!"),a.set(we),this._C=c,this._T=i||this._T||(this.is(ut)?yi:Ni),this._E=r||this._E;var l=Qe({},Si,this._E,{Transition:this._T});return Ne(l,function(d,s){var m=d(o,c,o._o);c[s]=m,m.setup&&m.setup()}),Ne(c,function(d){d.mount&&d.mount()}),this.emit(De),ue(this.root,Fr),a.set(Me),this.emit(pn),this},n.sync=function(r){return this.splides.push({splide:r}),r.splides.push({splide:this,isParent:!0}),this.state.is(Me)&&(this._C.Sync.remount(),r.Components.Sync.remount()),this},n.go=function(r){return this._C.Controller.go(r),this},n.on=function(r,i){return this.event.on(r,i),this},n.off=function(r){return this.event.off(r),this},n.emit=function(r){var i;return(i=this.event).emit.apply(i,[r].concat(he(arguments,1))),this},n.add=function(r,i){return this._C.Slides.add(r,i),this},n.remove=function(r){return this._C.Slides.remove(r),this},n.is=function(r){return this._o.type===r},n.refresh=function(){return this.emit(K),this},n.destroy=function(r){r===void 0&&(r=!0);var i=this.event,o=this.state;return o.is(we)?z(this).on(pn,this.destroy.bind(this,r)):(Ne(this._C,function(a){a.destroy&&a.destroy(r)},!0),i.emit(Nn),i.destroy(),r&&fe(this.splides),o.set(gt)),this},lr(e,[{key:"options",get:function(){return this._o},set:function(r){this._C.Media.set(r,!0,!0)}},{key:"length",get:function(){return this._C.Slides.getLength(!0)}},{key:"index",get:function(){return this._C.Controller.getIndex()}}]),e})(),rn=Li;rn.defaults={},rn.STATES=dr;class Ri{constructor(){this.isDestroyed=!1}initialize(n){try{return this.destroy(),this.splideInstance=new rn(`.${W.slider.dom.splideClass}`,n),this.splideInstance.mount(),this.isDestroyed=!1,this.splideInstance}catch{return}}destroy(){if(this.splideInstance&&!this.isDestroyed)try{this.splideInstance.destroy()}catch{}finally{delete this.splideInstance,this.isDestroyed=!0}}isInitialized(){return this.splideInstance!==null&&!this.isDestroyed}getInstance(){if(this.isInitialized())return this.splideInstance}reinitialize(n){return this.destroy(),this.initialize(n)}isContainerAvailable(){try{return document.querySelector(`.${W.slider.dom.splideClass}`)!==null}catch{return!1}}initializeWithDelay(n,t=dt.CHECK_INTERVAL){setTimeout(()=>{this.isContainerAvailable()&&this.initialize(n)},t)}getStatus(){return{isInitialized:this.isInitialized(),isDestroyed:this.isDestroyed,hasInstance:this.splideInstance!==null,containerAvailable:this.isContainerAvailable()}}}class Di{constructor(){this.checkTime=W.slider.checkTime,this.configManager=new or,this.domBuilder=new sr,this.dataManager=new ur,this.lifecycleManager=new Ri}attachAdvertiseHeader(n){try{this.destroy();const t=this.dataManager.fetchSlideData();if(t.length===te.EMPTY_LENGTH)return;const r=this.domBuilder.createContainer(),i=this.domBuilder.createSplideElement(r),o=this.domBuilder.createSplideTrack(i),a=this.populateSlides(o,t);this.domBuilder.createPagination(i),this.domBuilder.appendToDOM(r),setTimeout(()=>{this.initializeSplide(a)},this.checkTime)}catch{}}populateSlides(n,t){let r=te.EMPTY_LENGTH;for(const i of t)if(i.isValid){const o=this.domBuilder.createSlide(i.imageSrc,i.imageLink);Xe(n,o),r+=ft.SLIDE_INCREMENT}return r}initializeSplide(n){try{const t=this.dataManager.getTransitionTime(),r=this.configManager.calculateConfiguration(n,t);if(!this.configManager.validateConfiguration(r.finalConfig).isValid)return;this.lifecycleManager.initialize(r.finalConfig)}catch{}}destroy(){this.lifecycleManager.destroy(),this.domBuilder.cleanup()}}class ze{constructor(){this.errorLog=[],this.isInitialized=!1}static getInstance(){return ze.instance||(ze.instance=new ze),ze.instance}initialize(){try{return this.isInitialized||(this.setupGlobalErrorHandling(),this.isInitialized=!0),!0}catch{return!1}}handleSync(n,t){try{return n()}catch(r){return this.logError(r,t),!1}}handleAsync(n,t){return n().catch(r=>(this.logError(r,t),!1))}logError(n,t){try{const r={timestamp:new Date,error:n,context:t};this.errorLog.push(r),this.errorLog.length>ir.MAX_ERROR_LOG_ENTRIES&&this.errorLog.shift()}catch{}}setupGlobalErrorHandling(){try{globalThis.addEventListener("unhandledrejection",n=>{this.logError(new Error(String(n.reason)),"Unhandled Promise Rejection")})}catch{}}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[]}}class Oe{constructor(){}static getInstance(){return Oe.instance||(Oe.instance=new Oe),Oe.instance}isTagsPage(){try{return pe.current.get("routeName")==="tags"}catch{try{return globalThis.location.pathname.includes("/tags")}catch{return!1}}}getConfig(){return W}isSlideshowConfigured(){try{for(let r=1;r<=W.slider.maxSlides;r+=1)if(pe.forum.attribute(`wusong8899-header-advertisement.Image${r}`))return!0;return!1}catch{return!1}}}pe.initializers.add(W.app.extensionId,()=>{const e=ze.getInstance(),n=Oe.getInstance();if(!e.initialize())return;const t=new Di;er.extend(tr.prototype,"view",function(i){e.handleSync(()=>{n.isTagsPage()&&Ci(i,t)},"HeaderPrimary view extension")})});const Ci=(e,n,t)=>{try{if(Oe.getInstance().isSlideshowConfigured())try{n.attachAdvertiseHeader(e)}catch{}!pe.session.user&&un()&&Oi()}catch{}},Oi=()=>{let e=document.getElementById(W.ui.headerIconId);if(e===null){const n=pe.forum.attribute("wusong8899-header-advertisement.HeaderIconUrl")||W.ui.headerIconUrl;e=document.createElement("div"),e.id=W.ui.headerIconId,e.className="HeaderIcon-container mobile-only",e.innerHTML=`<img src="${n}" alt="Header Icon" class="HeaderIcon-image" />`;const t=document.querySelector("#app-navigation .Navigation.ButtonGroup.App-backControl");t&&t.appendChild(e)}}})(flarum.core.compat["common/extend"],flarum.core.compat["forum/app"],flarum.core.compat["forum/components/HeaderPrimary"]);
//# sourceMappingURL=forum.js.map

module.exports={};