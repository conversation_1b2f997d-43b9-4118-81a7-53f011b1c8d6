(function(er,Ae,tr){"use strict";const an=t=>{try{return document.querySelector(t)||!1}catch{return!1}},nr=t=>{try{return document.querySelectorAll(t)}catch{return document.querySelectorAll("")}},Oe=(t,n={},e="")=>{try{const r=document.createElement(t);for(const[i,o]of Object.entries(n))i==="className"?r.className=String(o):i==="id"?r.id=String(o):r.setAttribute(i,String(o));return e&&(r.innerHTML=e),r}catch{return document.createElement("div")}},Xe=(t,n)=>{try{t.appendChild(n)}catch{}},rr=(t,n)=>{try{t.prepend(n)}catch{}},Ot=t=>{try{t.remove()}catch{}},on={USER_AGENT_SUBSTR_START:0,USER_AGENT_SUBSTR_LENGTH:4},ir={MAX_ERROR_LOG_ENTRIES:50,DOM_READY_TIMEOUT:5e3,SLIDE_NUMBER_MIN:1,SLIDE_NUMBER_MAX:30,TRANSITION_TIME_MIN:1e3,TRANSITION_TIME_MAX:3e4,CONFIG_MAX_SLIDES_MIN:1,CONFIG_MAX_SLIDES_MAX:50},ft={SLIDE_INCREMENT:1,INITIAL_SLIDE_INDEX:1},te={EMPTY_LENGTH:0,FIRST_INDEX:0,NOT_FOUND_INDEX:-1,NEXT_ITEM_OFFSET:1,LAST_ITEM_OFFSET:-1},dt={CHECK_INTERVAL:10,DATA_CHECK_INTERVAL:100,DEFAULT_TRANSITION_TIME:5e3},sn={SPLIDE_AD_CONTAINER_ID:"splideAdContainer",HEADER_ICON_ID:"wusong8899HeaderAdvIcon"},ar={AD_SPLIDE:"adSplide"},vt={ID:"wusong8899-header-advertisement",TRANSLATION_PREFIX:"wusong8899-header-advertisement",MAX_SLIDES:30,HEADER_ICON_URL:"https://ex.cc/assets/files/date/test.png"},z={env:"production",app:{extensionId:vt.ID,translationPrefix:vt.TRANSLATION_PREFIX},slider:{maxSlides:vt.MAX_SLIDES,defaultTransitionTime:dt.DEFAULT_TRANSITION_TIME,checkTime:dt.CHECK_INTERVAL,dataCheckInterval:dt.DATA_CHECK_INTERVAL,dom:{containerId:sn.SPLIDE_AD_CONTAINER_ID,splideClass:ar.AD_SPLIDE},splide:{gap:"15px",type:"loop",focus:"center",perPage:1,pagination:!0,arrows:!0}},ui:{headerIconId:sn.HEADER_ICON_ID,headerIconUrl:vt.HEADER_ICON_URL}},ye={MIN_SLIDES_FOR_LOOP:2,MOBILE_PER_PAGE:1,TABLET_PER_PAGE:1,DESKTOP_PER_PAGE:1};class or{calculateConfiguration(n,e){const r=this.shouldEnableLoop(n),i=this.calculateResponsiveConfig(),o=this.buildFinalConfig(e,r);return{enableLoop:r,responsiveConfig:i,finalConfig:o}}validateConfiguration(n){const e=[];if(n.autoplay&&typeof n.autoplay=="object"&&(typeof n.autoplay.interval!="number"||n.autoplay.interval<=te.EMPTY_LENGTH)&&e.push("Invalid autoplay interval configuration"),(typeof n.gap!="string"||!n.gap.match(/^\d+px$/))&&e.push('Invalid gap configuration - must be in format "Npx"'),(typeof n.perPage!="number"||n.perPage<=te.EMPTY_LENGTH)&&e.push("Invalid perPage configuration"),n.breakpoints&&typeof n.breakpoints=="object")for(const[r,i]of Object.entries(n.breakpoints)){const o=Number(r);(Number.isNaN(o)||o<=te.EMPTY_LENGTH)&&e.push(`Invalid breakpoint width: ${r}`),(typeof i.perPage!="number"||i.perPage<=te.EMPTY_LENGTH)&&e.push(`Invalid perPage for breakpoint ${r}`),(typeof i.gap!="string"||!i.gap.match(/^\d+px$/))&&e.push(`Invalid gap for breakpoint ${r}`)}return{isValid:e.length===te.EMPTY_LENGTH,errors:e}}shouldEnableLoop(n){return n>=ye.MIN_SLIDES_FOR_LOOP}calculateResponsiveConfig(){return{mobile:{perPage:ye.MOBILE_PER_PAGE,gap:"10px"},tablet:{perPage:ye.TABLET_PER_PAGE,gap:"12px"},desktop:{perPage:ye.DESKTOP_PER_PAGE,gap:z.slider.splide.gap}}}buildFinalConfig(n,e){const r={320:{perPage:ye.MOBILE_PER_PAGE,gap:"10px"},768:{perPage:ye.TABLET_PER_PAGE,gap:"12px"},1024:{perPage:ye.DESKTOP_PER_PAGE,gap:z.slider.splide.gap}};let i="slide";e&&(i="loop");let o=!1;return n>te.EMPTY_LENGTH&&(o={interval:n,pauseOnHover:!0}),{type:i,autoplay:o,gap:z.slider.splide.gap,focus:z.slider.splide.focus,perPage:z.slider.splide.perPage,breakpoints:r,pagination:z.slider.splide.pagination,arrows:z.slider.splide.arrows,speed:600,direction:"ltr",arrowPath:"M15.41,16.58L10.83,12L15.41,7.41L14,6L8,12L14,18L15.41,16.58Z",i18n:{prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay"}}}}const un=()=>{try{const{userAgent:t}=navigator;return t.substring(on.USER_AGENT_SUBSTR_START,on.USER_AGENT_SUBSTR_LENGTH)==="Mobi"}catch{return!1}};class sr{createContainer(){this.removeExistingNavigation();const n=Oe("div",{id:z.slider.dom.containerId,className:"adContainer adContainer--forced"});return this.container=n,n}createSplideElement(n){let e=`splide ${z.slider.dom.splideClass} adSplide--forced`;un()&&(e+=" adSplide--mobile");const r=Oe("div",{className:e});return Xe(n,r),r}createSplideTrack(n){const e=Oe("div",{className:"splide__track splide__track--forced"}),r=Oe("ul",{className:"splide__list splide__list--forced"});return Xe(e,r),Xe(n,e),r}createSlide(n,e){const r=Oe("li",{className:"splide__slide splide__slide--forced"});let i="";return e&&(i=`window.location.href="${e}"`),r.innerHTML=`<img onclick='${i}' src='${n}' class='splide__slide__image' />`,r}createPagination(n){const e=Oe("ul",{className:"splide__pagination"});Xe(n,e)}createNavigation(n){}appendToDOM(n){const e=an("#content .container");e&&rr(e,n)}cleanup(){this.container&&(Ot(this.container),delete this.container)}getContainer(){return this.container}removeExistingNavigation(){const n=an(`#${z.slider.dom.containerId}`);n&&Ot(n);const e=nr(".item-nav");for(const r of e)Ot(r)}}class ur{constructor(){this.maxSlides=z.slider.maxSlides}fetchSlideData(){const n=[];for(let e=ft.INITIAL_SLIDE_INDEX;e<=this.maxSlides;e+=ft.SLIDE_INCREMENT){const r=this.getForumAttribute(`wusong8899-header-advertisement.Image${e}`),i=this.getForumAttribute(`wusong8899-header-advertisement.Link${e}`);r&&n.push({imageSrc:String(r),imageLink:String(i||""),slideIndex:e})}return this.processSlideData(n)}validateSlideData(n){const e=[];if(!Array.isArray(n))return e.push("Slide data must be an array"),{isValid:!1,errors:e};if(n.length===te.EMPTY_LENGTH)return e.push("No slide data provided"),{isValid:!1,errors:e};for(const r of n)(!r.imageSrc||typeof r.imageSrc!="string")&&e.push(`Invalid image source for slide ${r.slideIndex}`),r.imageLink&&typeof r.imageLink!="string"&&e.push(`Invalid image link for slide ${r.slideIndex}`),(typeof r.slideIndex!="number"||r.slideIndex<ft.INITIAL_SLIDE_INDEX)&&e.push(`Invalid slide index: ${r.slideIndex}`),r.imageSrc&&!this.isValidUrl(r.imageSrc)&&e.push(`Invalid image URL format for slide ${r.slideIndex}`),r.imageLink&&!this.isValidUrl(r.imageLink)&&e.push(`Invalid link URL format for slide ${r.slideIndex}`);return{isValid:e.length===te.EMPTY_LENGTH,errors:e}}getTransitionTime(){const n=this.getForumAttribute("wusong8899-header-advertisement.TransitionTime");if(n){const e=Number.parseInt(String(n),10);if(!Number.isNaN(e)&&e>te.EMPTY_LENGTH)return e}return z.slider.defaultTransitionTime}processSlideData(n){return n.map(e=>({imageSrc:e.imageSrc,imageLink:e.imageLink,slideIndex:e.slideIndex,isValid:this.isSlideValid(e)})).filter(e=>e.isValid)}isSlideValid(n){return!!(n.imageSrc&&typeof n.imageSrc=="string"&&this.isValidUrl(n.imageSrc)&&typeof n.slideIndex=="number"&&n.slideIndex>te.EMPTY_LENGTH)}isValidUrl(n){try{return!!new URL(n)}catch{return n.startsWith("/")||n.startsWith("./")||n.startsWith("data:")}}getForumAttribute(n){try{const e=Ae&&Ae.forum,r=e&&e.attribute;return typeof r=="function"?r.call(e,n):void 0}catch{return}}}function cr(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function lr(t,n,e){return n&&cr(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),t}/*!
 * Splide.js
 * Version  : 4.1.4
 * License  : MIT
 * Copyright: 2022 Naotoshi Fujita
 */var cn="(prefers-reduced-motion: reduce)",we=1,fr=2,Me=3,xe=4,We=5,gt=6,Et=7,dr={CREATED:we,MOUNTED:fr,IDLE:Me,MOVING:xe,SCROLLING:We,DRAGGING:gt,DESTROYED:Et};function fe(t){t.length=0}function he(t,n,e){return Array.prototype.slice.call(t,n,e)}function U(t){return t.bind.apply(t,[null].concat(he(arguments,1)))}var ln=setTimeout,wt=function(){};function fn(t){return requestAnimationFrame(t)}function ht(t,n){return typeof n===t}function Ye(t){return!xt(t)&&ht("object",t)}var Mt=Array.isArray,dn=U(ht,"function"),me=U(ht,"string"),$e=U(ht,"undefined");function xt(t){return t===null}function vn(t){try{return t instanceof(t.ownerDocument.defaultView||window).HTMLElement}catch{return!1}}function Ke(t){return Mt(t)?t:[t]}function ne(t,n){Ke(t).forEach(n)}function Gt(t,n){return t.indexOf(n)>-1}function mt(t,n){return t.push.apply(t,Ke(n)),t}function de(t,n,e){t&&ne(n,function(r){r&&t.classList[e?"add":"remove"](r)})}function ue(t,n){de(t,me(n)?n.split(" "):n,!0)}function qe(t,n){ne(n,t.appendChild.bind(t))}function Vt(t,n){ne(t,function(e){var r=(n||e).parentNode;r&&r.insertBefore(e,n)})}function je(t,n){return vn(t)&&(t.msMatchesSelector||t.matches).call(t,n)}function gn(t,n){var e=t?he(t.children):[];return n?e.filter(function(r){return je(r,n)}):e}function Ze(t,n){return n?gn(t,n)[0]:t.firstElementChild}var Je=Object.keys;function Le(t,n,e){return t&&(e?Je(t).reverse():Je(t)).forEach(function(r){r!=="__proto__"&&n(t[r],r)}),t}function Qe(t){return he(arguments,1).forEach(function(n){Le(n,function(e,r){t[r]=n[r]})}),t}function _e(t){return he(arguments,1).forEach(function(n){Le(n,function(e,r){Mt(e)?t[r]=e.slice():Ye(e)?t[r]=_e({},Ye(t[r])?t[r]:{},e):t[r]=e})}),t}function En(t,n){ne(n||Je(t),function(e){delete t[e]})}function ce(t,n){ne(t,function(e){ne(n,function(r){e&&e.removeAttribute(r)})})}function M(t,n,e){Ye(n)?Le(n,function(r,i){M(t,i,r)}):ne(t,function(r){xt(e)||e===""?ce(r,n):r.setAttribute(n,String(e))})}function Ge(t,n,e){var r=document.createElement(t);return n&&(me(n)?ue(r,n):M(r,n)),e&&qe(e,r),r}function ae(t,n,e){if($e(e))return getComputedStyle(t)[n];xt(e)||(t.style[n]=""+e)}function et(t,n){ae(t,"display",n)}function hn(t){t.setActive&&t.setActive()||t.focus({preventScroll:!0})}function oe(t,n){return t.getAttribute(n)}function mn(t,n){return t&&t.classList.contains(n)}function re(t){return t.getBoundingClientRect()}function Ne(t){ne(t,function(n){n&&n.parentNode&&n.parentNode.removeChild(n)})}function _n(t){return Ze(new DOMParser().parseFromString(t,"text/html").body)}function ve(t,n){t.preventDefault(),n&&(t.stopPropagation(),t.stopImmediatePropagation())}function In(t,n){return t&&t.querySelector(n)}function kt(t,n){return n?he(t.querySelectorAll(n)):[]}function ge(t,n){de(t,n,!1)}function Ft(t){return t.timeStamp}function Re(t){return me(t)?t:t?t+"px":""}var tt="splide",Ut="data-"+tt;function nt(t,n){if(!t)throw new Error("["+tt+"] "+(n||""))}var Ie=Math.min,_t=Math.max,It=Math.floor,rt=Math.ceil,J=Math.abs;function Tn(t,n,e){return J(t-n)<e}function Tt(t,n,e,r){var i=Ie(n,e),o=_t(n,e);return r?i<t&&t<o:i<=t&&t<=o}function Ve(t,n,e){var r=Ie(n,e),i=_t(n,e);return Ie(_t(r,t),i)}function Ht(t){return+(t>0)-+(t<0)}function Bt(t,n){return ne(n,function(e){t=t.replace("%s",""+e)}),t}function zt(t){return t<10?"0"+t:""+t}var Sn={};function vr(t){return""+t+zt(Sn[t]=(Sn[t]||0)+1)}function pn(){var t=[];function n(a,c,l,d){i(a,c,function(s,m,v){var g="addEventListener"in s,u=g?s.removeEventListener.bind(s,m,l,d):s.removeListener.bind(s,l);g?s.addEventListener(m,l,d):s.addListener(l),t.push([s,m,v,l,u])})}function e(a,c,l){i(a,c,function(d,s,m){t=t.filter(function(v){return v[0]===d&&v[1]===s&&v[2]===m&&(!l||v[3]===l)?(v[4](),!1):!0})})}function r(a,c,l){var d,s=!0;return typeof CustomEvent=="function"?d=new CustomEvent(c,{bubbles:s,detail:l}):(d=document.createEvent("CustomEvent"),d.initCustomEvent(c,s,!1,l)),a.dispatchEvent(d),d}function i(a,c,l){ne(a,function(d){d&&ne(c,function(s){s.split(" ").forEach(function(m){var v=m.split(".");l(d,v[0],v[1])})})})}function o(){t.forEach(function(a){a[4]()}),fe(t)}return{bind:n,unbind:e,dispatch:r,destroy:o}}var De="mounted",An="ready",Te="move",it="moved",yn="click",gr="active",Er="inactive",hr="visible",mr="hidden",K="refresh",Q="updated",at="resize",Xt="resized",_r="drag",Ir="dragging",Tr="dragged",Wt="scroll",ke="scrolled",Sr="overflow",Ln="destroy",pr="arrows:mounted",Ar="arrows:updated",yr="pagination:mounted",Lr="pagination:updated",Nn="navigation:mounted",Rn="autoplay:play",Nr="autoplay:playing",Dn="autoplay:pause",Cn="lazyload:loaded",bn="sk",Pn="sh",St="ei";function X(t){var n=t?t.event.bus:document.createDocumentFragment(),e=pn();function r(o,a){e.bind(n,Ke(o).join(" "),function(c){a.apply(a,Mt(c.detail)?c.detail:[])})}function i(o){e.dispatch(n,o,he(arguments,1))}return t&&t.event.on(Ln,e.destroy),Qe(e,{bus:n,on:r,off:U(e.unbind,n),emit:i})}function pt(t,n,e,r){var i=Date.now,o,a=0,c,l=!0,d=0;function s(){if(!l){if(a=t?Ie((i()-o)/t,1):1,e&&e(a),a>=1&&(n(),o=i(),r&&++d>=r))return v();c=fn(s)}}function m(_){_||u(),o=i()-(_?a*t:0),l=!1,c=fn(s)}function v(){l=!0}function g(){o=i(),a=0,e&&e(a)}function u(){c&&cancelAnimationFrame(c),a=0,c=0,l=!0}function f(_){t=_}function I(){return l}return{start:m,rewind:g,pause:v,cancel:u,set:f,isPaused:I}}function Rr(t){var n=t;function e(i){n=i}function r(i){return Gt(Ke(i),n)}return{set:e,is:r}}function Dr(t,n){var e=pt(0,t,null,1);return function(){e.isPaused()&&e.start()}}function Cr(t,n,e){var r=t.state,i=e.breakpoints||{},o=e.reducedMotion||{},a=pn(),c=[];function l(){var u=e.mediaQuery==="min";Je(i).sort(function(f,I){return u?+f-+I:+I-+f}).forEach(function(f){s(i[f],"("+(u?"min":"max")+"-width:"+f+"px)")}),s(o,cn),m()}function d(u){u&&a.destroy()}function s(u,f){var I=matchMedia(f);a.bind(I,"change",m),c.push([u,I])}function m(){var u=r.is(Et),f=e.direction,I=c.reduce(function(_,h){return _e(_,h[1].matches?h[0]:{})},{});En(e),g(I),e.destroy?t.destroy(e.destroy==="completely"):u?(d(!0),t.mount()):f!==e.direction&&t.refresh()}function v(u){matchMedia(cn).matches&&(u?_e(e,o):En(e,Je(o)))}function g(u,f,I){_e(e,u),f&&_e(Object.getPrototypeOf(e),u),(I||!r.is(we))&&t.emit(Q,e)}return{setup:l,destroy:d,reduce:v,set:g}}var At="Arrow",yt=At+"Left",Lt=At+"Right",On=At+"Up",wn=At+"Down",Mn="rtl",Nt="ttb",Yt={width:["height"],left:["top","right"],right:["bottom","left"],x:["y"],X:["Y"],Y:["X"],ArrowLeft:[On,Lt],ArrowRight:[wn,yt]};function br(t,n,e){function r(o,a,c){c=c||e.direction;var l=c===Mn&&!a?1:c===Nt?0:-1;return Yt[o]&&Yt[o][l]||o.replace(/width|left|right/i,function(d,s){var m=Yt[d.toLowerCase()][l]||d;return s>0?m.charAt(0).toUpperCase()+m.slice(1):m})}function i(o){return o*(e.direction===Mn?1:-1)}return{resolve:r,orient:i}}var Ee="role",Fe="tabindex",Pr="disabled",se="aria-",ot=se+"controls",xn=se+"current",Gn=se+"selected",ie=se+"label",$t=se+"labelledby",Vn=se+"hidden",Kt=se+"orientation",st=se+"roledescription",kn=se+"live",Fn=se+"busy",Un=se+"atomic",qt=[Ee,Fe,Pr,ot,xn,ie,$t,Vn,Kt,st],le=tt+"__",Se="is-",jt=tt,Hn=le+"track",Or=le+"list",Rt=le+"slide",Bn=Rt+"--clone",wr=Rt+"__container",Zt=le+"arrows",Dt=le+"arrow",zn=Dt+"--prev",Xn=Dt+"--next",Ct=le+"pagination",Wn=Ct+"__page",Mr=le+"progress",xr=Mr+"__bar",Gr=le+"toggle",Vr=le+"spinner",kr=le+"sr",Fr=Se+"initialized",Ce=Se+"active",Yn=Se+"prev",$n=Se+"next",Jt=Se+"visible",Qt=Se+"loading",Kn=Se+"focus-in",qn=Se+"overflow",Ur=[Ce,Jt,Yn,$n,Qt,Kn,qn],Hr={slide:Rt,clone:Bn,arrows:Zt,arrow:Dt,prev:zn,next:Xn,pagination:Ct,page:Wn,spinner:Vr};function Br(t,n){if(dn(t.closest))return t.closest(n);for(var e=t;e&&e.nodeType===1&&!je(e,n);)e=e.parentElement;return e}var zr=5,jn=200,Zn="touchstart mousedown",en="touchmove mousemove",tn="touchend touchcancel mouseup click";function Xr(t,n,e){var r=X(t),i=r.on,o=r.bind,a=t.root,c=e.i18n,l={},d=[],s=[],m=[],v,g,u;function f(){E(),O(),h()}function I(){i(K,_),i(K,f),i(Q,h),o(document,Zn+" keydown",function(S){u=S.type==="keydown"},{capture:!0}),o(a,"focusin",function(){de(a,Kn,!!u)})}function _(S){var D=qt.concat("style");fe(d),ge(a,s),ge(v,m),ce([v,g],D),ce(a,S?D:["style",st])}function h(){ge(a,s),ge(v,m),s=x(jt),m=x(Hn),ue(a,s),ue(v,m),M(a,ie,e.label),M(a,$t,e.labelledby)}function E(){v=R("."+Hn),g=Ze(v,"."+Or),nt(v&&g,"A track/list element is missing."),mt(d,gn(g,"."+Rt+":not(."+Bn+")")),Le({arrows:Zt,pagination:Ct,prev:zn,next:Xn,bar:xr,toggle:Gr},function(S,D){l[D]=R("."+S)}),Qe(l,{root:a,track:v,list:g,slides:d})}function O(){var S=a.id||vr(tt),D=e.role;a.id=S,v.id=v.id||S+"-track",g.id=g.id||S+"-list",!oe(a,Ee)&&a.tagName!=="SECTION"&&D&&M(a,Ee,D),M(a,st,c.carousel),M(g,Ee,"presentation")}function R(S){var D=In(a,S);return D&&Br(D,"."+jt)===a?D:void 0}function x(S){return[S+"--"+e.type,S+"--"+e.direction,e.drag&&S+"--draggable",e.isNavigation&&S+"--nav",S===jt&&Ce]}return Qe(l,{setup:f,mount:I,destroy:_})}var Ue="slide",He="loop",ut="fade";function Wr(t,n,e,r){var i=X(t),o=i.on,a=i.emit,c=i.bind,l=t.Components,d=t.root,s=t.options,m=s.isNavigation,v=s.updateOnMove,g=s.i18n,u=s.pagination,f=s.slideFocus,I=l.Direction.resolve,_=oe(r,"style"),h=oe(r,ie),E=e>-1,O=Ze(r,"."+wr),R;function x(){E||(r.id=d.id+"-slide"+zt(n+1),M(r,Ee,u?"tabpanel":"group"),M(r,st,g.slide),M(r,ie,h||Bt(g.slideLabel,[n+1,t.length]))),S()}function S(){c(r,"click",U(a,yn,w)),c(r,"keydown",U(a,bn,w)),o([it,Pn,ke],A),o(Nn,k),v&&o(Te,P)}function D(){R=!0,i.destroy(),ge(r,Ur),ce(r,qt),M(r,"style",_),M(r,ie,h||"")}function k(){var b=t.splides.map(function(p){var C=p.splide.Components.Slides.getAt(n);return C?C.slide.id:""}).join(" ");M(r,ie,Bt(g.slideX,(E?e:n)+1)),M(r,ot,b),M(r,Ee,f?"button":""),f&&ce(r,st)}function P(){R||A()}function A(){if(!R){var b=t.index;y(),L(),de(r,Yn,n===b-1),de(r,$n,n===b+1)}}function y(){var b=V();b!==mn(r,Ce)&&(de(r,Ce,b),M(r,xn,m&&b||""),a(b?gr:Er,w))}function L(){var b=Y(),p=!b&&(!V()||E);if(t.state.is([xe,We])||M(r,Vn,p||""),M(kt(r,s.focusableNodes||""),Fe,p?-1:""),f&&M(r,Fe,p?-1:0),b!==mn(r,Jt)&&(de(r,Jt,b),a(b?hr:mr,w)),!b&&document.activeElement===r){var C=l.Slides.getAt(t.index);C&&hn(C.slide)}}function G(b,p,C){ae(C&&O||r,b,p)}function V(){var b=t.index;return b===n||s.cloneStatus&&b===e}function Y(){if(t.is(ut))return V();var b=re(l.Elements.track),p=re(r),C=I("left",!0),F=I("right",!0);return It(b[C])<=rt(p[C])&&It(p[F])<=rt(b[F])}function W(b,p){var C=J(b-n);return!E&&(s.rewind||t.is(He))&&(C=Ie(C,t.length-C)),C<=p}var w={index:n,slideIndex:e,slide:r,container:O,isClone:E,mount:x,destroy:D,update:A,style:G,isWithin:W};return w}function Yr(t,n,e){var r=X(t),i=r.on,o=r.emit,a=r.bind,c=n.Elements,l=c.slides,d=c.list,s=[];function m(){v(),i(K,g),i(K,v)}function v(){l.forEach(function(A,y){f(A,y,-1)})}function g(){R(function(A){A.destroy()}),fe(s)}function u(){R(function(A){A.update()})}function f(A,y,L){var G=Wr(t,y,L,A);G.mount(),s.push(G),s.sort(function(V,Y){return V.index-Y.index})}function I(A){return A?x(function(y){return!y.isClone}):s}function _(A){var y=n.Controller,L=y.toIndex(A),G=y.hasFocus()?1:e.perPage;return x(function(V){return Tt(V.index,L,L+G-1)})}function h(A){return x(A)[0]}function E(A,y){ne(A,function(L){if(me(L)&&(L=_n(L)),vn(L)){var G=l[y];G?Vt(L,G):qe(d,L),ue(L,e.classes.slide),D(L,U(o,at))}}),o(K)}function O(A){Ne(x(A).map(function(y){return y.slide})),o(K)}function R(A,y){I(y).forEach(A)}function x(A){return s.filter(dn(A)?A:function(y){return me(A)?je(y.slide,A):Gt(Ke(A),y.index)})}function S(A,y,L){R(function(G){G.style(A,y,L)})}function D(A,y){var L=kt(A,"img"),G=L.length;G?L.forEach(function(V){a(V,"load error",function(){--G||y()})}):y()}function k(A){return A?l.length:s.length}function P(){return s.length>e.perPage}return{mount:m,destroy:g,update:u,register:f,get:I,getIn:_,getAt:h,add:E,remove:O,forEach:R,filter:x,style:S,getLength:k,isEnough:P}}function $r(t,n,e){var r=X(t),i=r.on,o=r.bind,a=r.emit,c=n.Slides,l=n.Direction.resolve,d=n.Elements,s=d.root,m=d.track,v=d.list,g=c.getAt,u=c.style,f,I,_;function h(){E(),o(window,"resize load",Dr(U(a,at))),i([Q,K],E),i(at,O)}function E(){f=e.direction===Nt,ae(s,"maxWidth",Re(e.width)),ae(m,l("paddingLeft"),R(!1)),ae(m,l("paddingRight"),R(!0)),O(!0)}function O(w){var b=re(s);(w||I.width!==b.width||I.height!==b.height)&&(ae(m,"height",x()),u(l("marginRight"),Re(e.gap)),u("width",D()),u("height",k(),!0),I=b,a(Xt),_!==(_=W())&&(de(s,qn,_),a(Sr,_)))}function R(w){var b=e.padding,p=l(w?"right":"left");return b&&Re(b[p]||(Ye(b)?0:b))||"0px"}function x(){var w="";return f&&(w=S(),nt(w,"height or heightRatio is missing."),w="calc("+w+" - "+R(!1)+" - "+R(!0)+")"),w}function S(){return Re(e.height||re(v).width*e.heightRatio)}function D(){return e.autoWidth?null:Re(e.fixedWidth)||(f?"":P())}function k(){return Re(e.fixedHeight)||(f?e.autoHeight?null:P():S())}function P(){var w=Re(e.gap);return"calc((100%"+(w&&" + "+w)+")/"+(e.perPage||1)+(w&&" - "+w)+")"}function A(){return re(v)[l("width")]}function y(w,b){var p=g(w||0);return p?re(p.slide)[l("width")]+(b?0:V()):0}function L(w,b){var p=g(w);if(p){var C=re(p.slide)[l("right")],F=re(v)[l("left")];return J(C-F)+(b?0:V())}return 0}function G(w){return L(t.length-1)-L(0)+y(0,w)}function V(){var w=g(0);return w&&parseFloat(ae(w.slide,l("marginRight")))||0}function Y(w){return parseFloat(ae(m,l("padding"+(w?"Right":"Left"))))||0}function W(){return t.is(ut)||G(!0)>A()}return{mount:h,resize:O,listSize:A,slideSize:y,sliderSize:G,totalSize:L,getPadding:Y,isOverflow:W}}var Kr=2;function qr(t,n,e){var r=X(t),i=r.on,o=n.Elements,a=n.Slides,c=n.Direction.resolve,l=[],d;function s(){i(K,m),i([Q,at],g),(d=I())&&(u(d),n.Layout.resize(!0))}function m(){v(),s()}function v(){Ne(l),fe(l),r.destroy()}function g(){var _=I();d!==_&&(d<_||!_)&&r.emit(K)}function u(_){var h=a.get().slice(),E=h.length;if(E){for(;h.length<_;)mt(h,h);mt(h.slice(-_),h.slice(0,_)).forEach(function(O,R){var x=R<_,S=f(O.slide,R);x?Vt(S,h[0].slide):qe(o.list,S),mt(l,S),a.register(S,R-_+(x?0:E),O.index)})}}function f(_,h){var E=_.cloneNode(!0);return ue(E,e.classes.clone),E.id=t.root.id+"-clone"+zt(h+1),E}function I(){var _=e.clones;if(!t.is(He))_=0;else if($e(_)){var h=e[c("fixedWidth")]&&n.Layout.slideSize(0),E=h&&rt(re(o.track)[c("width")]/h);_=E||e[c("autoWidth")]&&t.length||e.perPage*Kr}return _}return{mount:s,destroy:v}}function jr(t,n,e){var r=X(t),i=r.on,o=r.emit,a=t.state.set,c=n.Layout,l=c.slideSize,d=c.getPadding,s=c.totalSize,m=c.listSize,v=c.sliderSize,g=n.Direction,u=g.resolve,f=g.orient,I=n.Elements,_=I.list,h=I.track,E;function O(){E=n.Transition,i([De,Xt,Q,K],R)}function R(){n.Controller.isBusy()||(n.Scroll.cancel(),S(t.index),n.Slides.update())}function x(p,C,F,j){p!==C&&w(p>F)&&(A(),D(P(G(),p>F),!0)),a(xe),o(Te,C,F,p),E.start(C,function(){a(Me),o(it,C,F,p),j&&j()})}function S(p){D(L(p,!0))}function D(p,C){if(!t.is(ut)){var F=C?p:k(p);ae(_,"transform","translate"+u("X")+"("+F+"px)"),p!==F&&o(Pn)}}function k(p){if(t.is(He)){var C=y(p),F=C>n.Controller.getEnd(),j=C<0;(j||F)&&(p=P(p,F))}return p}function P(p,C){var F=p-W(C),j=v();return p-=f(j*(rt(J(F)/j)||1))*(C?1:-1),p}function A(){D(G(),!0),E.cancel()}function y(p){for(var C=n.Slides.get(),F=0,j=1/0,q=0;q<C.length;q++){var pe=C[q].index,T=J(L(pe,!0)-p);if(T<=j)j=T,F=pe;else break}return F}function L(p,C){var F=f(s(p-1)-Y(p));return C?V(F):F}function G(){var p=u("left");return re(_)[p]-re(h)[p]+f(d(!1))}function V(p){return e.trimSpace&&t.is(Ue)&&(p=Ve(p,0,f(v(!0)-m()))),p}function Y(p){var C=e.focus;return C==="center"?(m()-l(p,!0))/2:+C*l(p)||0}function W(p){return L(p?n.Controller.getEnd():0,!!e.trimSpace)}function w(p){var C=f(P(G(),p));return p?C>=0:C<=_[u("scrollWidth")]-re(h)[u("width")]}function b(p,C){C=$e(C)?G():C;var F=p!==!0&&f(C)<f(W(!1)),j=p!==!1&&f(C)>f(W(!0));return F||j}return{mount:O,move:x,jump:S,translate:D,shift:P,cancel:A,toIndex:y,toPosition:L,getPosition:G,getLimit:W,exceededLimit:b,reposition:R}}function Zr(t,n,e){var r=X(t),i=r.on,o=r.emit,a=n.Move,c=a.getPosition,l=a.getLimit,d=a.toPosition,s=n.Slides,m=s.isEnough,v=s.getLength,g=e.omitEnd,u=t.is(He),f=t.is(Ue),I=U(G,!1),_=U(G,!0),h=e.start||0,E,O=h,R,x,S;function D(){k(),i([Q,K,St],k),i(Xt,P)}function k(){R=v(!0),x=e.perMove,S=e.perPage,E=w();var T=Ve(h,0,g?E:R-1);T!==h&&(h=T,a.reposition())}function P(){E!==w()&&o(St)}function A(T,H,ee){if(!pe()){var $=L(T),Z=W($);Z>-1&&(H||Z!==h)&&(F(Z),a.move($,Z,O,ee))}}function y(T,H,ee,$){n.Scroll.scroll(T,H,ee,function(){var Z=W(a.toIndex(c()));F(g?Ie(Z,E):Z),$&&$()})}function L(T){var H=h;if(me(T)){var ee=T.match(/([+\-<>])(\d+)?/)||[],$=ee[1],Z=ee[2];$==="+"||$==="-"?H=V(h+ +(""+$+(+Z||1)),h):$===">"?H=Z?b(+Z):I(!0):$==="<"&&(H=_(!0))}else H=u?T:Ve(T,0,E);return H}function G(T,H){var ee=x||(q()?1:S),$=V(h+ee*(T?-1:1),h,!(x||q()));return $===-1&&f&&!Tn(c(),l(!T),1)?T?0:E:H?$:W($)}function V(T,H,ee){if(m()||q()){var $=Y(T);$!==T&&(H=T,T=$,ee=!1),T<0||T>E?!x&&(Tt(0,T,H,!0)||Tt(E,H,T,!0))?T=b(p(T)):u?T=ee?T<0?-(R%S||S):R:T:e.rewind?T=T<0?E:0:T=-1:ee&&T!==H&&(T=b(p(H)+(T<H?-1:1)))}else T=-1;return T}function Y(T){if(f&&e.trimSpace==="move"&&T!==h)for(var H=c();H===d(T,!0)&&Tt(T,0,t.length-1,!e.rewind);)T<h?--T:++T;return T}function W(T){return u?(T+R)%R||0:T}function w(){for(var T=R-(q()||u&&x?1:S);g&&T-- >0;)if(d(R-1,!0)!==d(T,!0)){T++;break}return Ve(T,0,R-1)}function b(T){return Ve(q()?T:S*T,0,E)}function p(T){return q()?Ie(T,E):It((T>=E?R-1:T)/S)}function C(T){var H=a.toIndex(T);return f?Ve(H,0,E):H}function F(T){T!==h&&(O=h,h=T)}function j(T){return T?O:h}function q(){return!$e(e.focus)||e.isNavigation}function pe(){return t.state.is([xe,We])&&!!e.waitForTransition}return{mount:D,go:A,scroll:y,getNext:I,getPrev:_,getAdjacent:G,getEnd:w,setIndex:F,getIndex:j,toIndex:b,toPage:p,toDest:C,hasFocus:q,isBusy:pe}}var Jr="http://www.w3.org/2000/svg",Qr="m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z",bt=40;function ei(t,n,e){var r=X(t),i=r.on,o=r.bind,a=r.emit,c=e.classes,l=e.i18n,d=n.Elements,s=n.Controller,m=d.arrows,v=d.track,g=m,u=d.prev,f=d.next,I,_,h={};function E(){R(),i(Q,O)}function O(){x(),E()}function R(){var y=e.arrows;y&&!(u&&f)&&k(),u&&f&&(Qe(h,{prev:u,next:f}),et(g,y?"":"none"),ue(g,_=Zt+"--"+e.direction),y&&(S(),A(),M([u,f],ot,v.id),a(pr,u,f)))}function x(){r.destroy(),ge(g,_),I?(Ne(m?[u,f]:g),u=f=null):ce([u,f],qt)}function S(){i([De,it,K,ke,St],A),o(f,"click",U(D,">")),o(u,"click",U(D,"<"))}function D(y){s.go(y,!0)}function k(){g=m||Ge("div",c.arrows),u=P(!0),f=P(!1),I=!0,qe(g,[u,f]),!m&&Vt(g,v)}function P(y){var L='<button class="'+c.arrow+" "+(y?c.prev:c.next)+'" type="button"><svg xmlns="'+Jr+'" viewBox="0 0 '+bt+" "+bt+'" width="'+bt+'" height="'+bt+'" focusable="false"><path d="'+(e.arrowPath||Qr)+'" />';return _n(L)}function A(){if(u&&f){var y=t.index,L=s.getPrev(),G=s.getNext(),V=L>-1&&y<L?l.last:l.prev,Y=G>-1&&y>G?l.first:l.next;u.disabled=L<0,f.disabled=G<0,M(u,ie,V),M(f,ie,Y),a(Ar,u,f,L,G)}}return{arrows:h,mount:E,destroy:x,update:A}}var ti=Ut+"-interval";function ni(t,n,e){var r=X(t),i=r.on,o=r.bind,a=r.emit,c=pt(e.interval,t.go.bind(t,">"),S),l=c.isPaused,d=n.Elements,s=n.Elements,m=s.root,v=s.toggle,g=e.autoplay,u,f,I=g==="pause";function _(){g&&(h(),v&&M(v,ot,d.track.id),I||E(),x())}function h(){e.pauseOnHover&&o(m,"mouseenter mouseleave",function(k){u=k.type==="mouseenter",R()}),e.pauseOnFocus&&o(m,"focusin focusout",function(k){f=k.type==="focusin",R()}),v&&o(v,"click",function(){I?E():O(!0)}),i([Te,Wt,K],c.rewind),i(Te,D)}function E(){l()&&n.Slides.isEnough()&&(c.start(!e.resetProgress),f=u=I=!1,x(),a(Rn))}function O(k){k===void 0&&(k=!0),I=!!k,x(),l()||(c.pause(),a(Dn))}function R(){I||(u||f?O(!1):E())}function x(){v&&(de(v,Ce,!I),M(v,ie,e.i18n[I?"play":"pause"]))}function S(k){var P=d.bar;P&&ae(P,"width",k*100+"%"),a(Nr,k)}function D(k){var P=n.Slides.getAt(k);c.set(P&&+oe(P.slide,ti)||e.interval)}return{mount:_,destroy:c.cancel,play:E,pause:O,isPaused:l}}function ri(t,n,e){var r=X(t),i=r.on;function o(){e.cover&&(i(Cn,U(c,!0)),i([De,Q,K],U(a,!0)))}function a(l){n.Slides.forEach(function(d){var s=Ze(d.container||d.slide,"img");s&&s.src&&c(l,s,d)})}function c(l,d,s){s.style("background",l?'center/cover no-repeat url("'+d.src+'")':"",!0),et(d,l?"none":"")}return{mount:o,destroy:U(a,!1)}}var ii=10,ai=600,oi=.6,si=1.5,ui=800;function ci(t,n,e){var r=X(t),i=r.on,o=r.emit,a=t.state.set,c=n.Move,l=c.getPosition,d=c.getLimit,s=c.exceededLimit,m=c.translate,v=t.is(Ue),g,u,f=1;function I(){i(Te,O),i([Q,K],R)}function _(S,D,k,P,A){var y=l();if(O(),k&&(!v||!s())){var L=n.Layout.sliderSize(),G=Ht(S)*L*It(J(S)/L)||0;S=c.toPosition(n.Controller.toDest(S%L))+G}var V=Tn(y,S,1);f=1,D=V?0:D||_t(J(S-y)/si,ui),u=P,g=pt(D,h,U(E,y,S,A),1),a(We),o(Wt),g.start()}function h(){a(Me),u&&u(),o(ke)}function E(S,D,k,P){var A=l(),y=S+(D-S)*x(P),L=(y-A)*f;m(A+L),v&&!k&&s()&&(f*=oi,J(L)<ii&&_(d(s(!0)),ai,!1,u,!0))}function O(){g&&g.cancel()}function R(){g&&!g.isPaused()&&(O(),h())}function x(S){var D=e.easingFunc;return D?D(S):1-Math.pow(1-S,4)}return{mount:I,destroy:O,scroll:_,cancel:R}}var Be={passive:!1,capture:!0};function li(t,n,e){var r=X(t),i=r.on,o=r.emit,a=r.bind,c=r.unbind,l=t.state,d=n.Move,s=n.Scroll,m=n.Controller,v=n.Elements.track,g=n.Media.reduce,u=n.Direction,f=u.resolve,I=u.orient,_=d.getPosition,h=d.exceededLimit,E,O,R,x,S,D=!1,k,P,A;function y(){a(v,en,wt,Be),a(v,tn,wt,Be),a(v,Zn,G,Be),a(v,"click",W,{capture:!0}),a(v,"dragstart",ve),i([De,Q],L)}function L(){var N=e.drag;Qn(!N),x=N==="free"}function G(N){if(k=!1,!P){var B=Z(N);$(N.target)&&(B||!N.button)&&(m.isBusy()?ve(N,!0):(A=B?v:window,S=l.is([xe,We]),R=null,a(A,en,V,Be),a(A,tn,Y,Be),d.cancel(),s.cancel(),w(N)))}}function V(N){if(l.is(gt)||(l.set(gt),o(_r)),N.cancelable)if(S){d.translate(E+ee(q(N)));var B=pe(N)>jn,Pe=D!==(D=h());(B||Pe)&&w(N),k=!0,o(Ir),ve(N)}else C(N)&&(S=p(N),ve(N))}function Y(N){l.is(gt)&&(l.set(Me),o(Tr)),S&&(b(N),ve(N)),c(A,en,V),c(A,tn,Y),S=!1}function W(N){!P&&k&&ve(N,!0)}function w(N){R=O,O=N,E=_()}function b(N){var B=F(N),Pe=j(B),lt=e.rewind&&e.rewindByDrag;g(!1),x?m.scroll(Pe,0,e.snap):t.is(ut)?m.go(I(Ht(B))<0?lt?"<":"-":lt?">":"+"):t.is(Ue)&&D&&lt?m.go(h(!0)?">":"<"):m.go(m.toDest(Pe),!0),g(!0)}function p(N){var B=e.dragMinThreshold,Pe=Ye(B),lt=Pe&&B.mouse||0,Oi=(Pe?B.touch:+B)||10;return J(q(N))>(Z(N)?Oi:lt)}function C(N){return J(q(N))>J(q(N,!0))}function F(N){if(t.is(He)||!D){var B=pe(N);if(B&&B<jn)return q(N)/B}return 0}function j(N){return _()+Ht(N)*Ie(J(N)*(e.flickPower||600),x?1/0:n.Layout.listSize()*(e.flickMaxPages||1))}function q(N,B){return H(N,B)-H(T(N),B)}function pe(N){return Ft(N)-Ft(T(N))}function T(N){return O===N&&R||O}function H(N,B){return(Z(N)?N.changedTouches[0]:N)["page"+f(B?"Y":"X")]}function ee(N){return N/(D&&t.is(Ue)?zr:1)}function $(N){var B=e.noDrag;return!je(N,"."+Wn+", ."+Dt)&&(!B||!je(N,B))}function Z(N){return typeof TouchEvent<"u"&&N instanceof TouchEvent}function Pi(){return S}function Qn(N){P=N}return{mount:y,disable:Qn,isDragging:Pi}}var fi={Spacebar:" ",Right:Lt,Left:yt,Up:On,Down:wn};function nn(t){return t=me(t)?t:t.key,fi[t]||t}var Jn="keydown";function di(t,n,e){var r=X(t),i=r.on,o=r.bind,a=r.unbind,c=t.root,l=n.Direction.resolve,d,s;function m(){v(),i(Q,g),i(Q,v),i(Te,f)}function v(){var _=e.keyboard;_&&(d=_==="global"?window:c,o(d,Jn,I))}function g(){a(d,Jn)}function u(_){s=_}function f(){var _=s;s=!0,ln(function(){s=_})}function I(_){if(!s){var h=nn(_);h===l(yt)?t.go("<"):h===l(Lt)&&t.go(">")}}return{mount:m,destroy:g,disable:u}}var ct=Ut+"-lazy",Pt=ct+"-srcset",vi="["+ct+"], ["+Pt+"]";function gi(t,n,e){var r=X(t),i=r.on,o=r.off,a=r.bind,c=r.emit,l=e.lazyLoad==="sequential",d=[it,ke],s=[];function m(){e.lazyLoad&&(v(),i(K,v))}function v(){fe(s),g(),l?_():(o(d),i(d,u),u())}function g(){n.Slides.forEach(function(h){kt(h.slide,vi).forEach(function(E){var O=oe(E,ct),R=oe(E,Pt);if(O!==E.src||R!==E.srcset){var x=e.classes.spinner,S=E.parentElement,D=Ze(S,"."+x)||Ge("span",x,S);s.push([E,h,D]),E.src||et(E,"none")}})})}function u(){s=s.filter(function(h){var E=e.perPage*((e.preloadPages||1)+1)-1;return h[1].isWithin(t.index,E)?f(h):!0}),s.length||o(d)}function f(h){var E=h[0];ue(h[1].slide,Qt),a(E,"load error",U(I,h)),M(E,"src",oe(E,ct)),M(E,"srcset",oe(E,Pt)),ce(E,ct),ce(E,Pt)}function I(h,E){var O=h[0],R=h[1];ge(R.slide,Qt),E.type!=="error"&&(Ne(h[2]),et(O,""),c(Cn,O,R),c(at)),l&&_()}function _(){s.length&&f(s.shift())}return{mount:m,destroy:U(fe,s),check:u}}function Ei(t,n,e){var r=X(t),i=r.on,o=r.emit,a=r.bind,c=n.Slides,l=n.Elements,d=n.Controller,s=d.hasFocus,m=d.getIndex,v=d.go,g=n.Direction.resolve,u=l.pagination,f=[],I,_;function h(){E(),i([Q,K,St],h);var P=e.pagination;u&&et(u,P?"":"none"),P&&(i([Te,Wt,ke],k),O(),k(),o(yr,{list:I,items:f},D(t.index)))}function E(){I&&(Ne(u?he(I.children):I),ge(I,_),fe(f),I=null),r.destroy()}function O(){var P=t.length,A=e.classes,y=e.i18n,L=e.perPage,G=s()?d.getEnd()+1:rt(P/L);I=u||Ge("ul",A.pagination,l.track.parentElement),ue(I,_=Ct+"--"+S()),M(I,Ee,"tablist"),M(I,ie,y.select),M(I,Kt,S()===Nt?"vertical":"");for(var V=0;V<G;V++){var Y=Ge("li",null,I),W=Ge("button",{class:A.page,type:"button"},Y),w=c.getIn(V).map(function(p){return p.slide.id}),b=!s()&&L>1?y.pageX:y.slideX;a(W,"click",U(R,V)),e.paginationKeyboard&&a(W,"keydown",U(x,V)),M(Y,Ee,"presentation"),M(W,Ee,"tab"),M(W,ot,w.join(" ")),M(W,ie,Bt(b,V+1)),M(W,Fe,-1),f.push({li:Y,button:W,page:V})}}function R(P){v(">"+P,!0)}function x(P,A){var y=f.length,L=nn(A),G=S(),V=-1;L===g(Lt,!1,G)?V=++P%y:L===g(yt,!1,G)?V=(--P+y)%y:L==="Home"?V=0:L==="End"&&(V=y-1);var Y=f[V];Y&&(hn(Y.button),v(">"+V),ve(A,!0))}function S(){return e.paginationDirection||e.direction}function D(P){return f[d.toPage(P)]}function k(){var P=D(m(!0)),A=D(m());if(P){var y=P.button;ge(y,Ce),ce(y,Gn),M(y,Fe,-1)}if(A){var L=A.button;ue(L,Ce),M(L,Gn,!0),M(L,Fe,"")}o(Lr,{list:I,items:f},P,A)}return{items:f,mount:h,destroy:E,getAt:D,update:k}}var hi=[" ","Enter"];function mi(t,n,e){var r=e.isNavigation,i=e.slideFocus,o=[];function a(){t.splides.forEach(function(u){u.isParent||(d(t,u.splide),d(u.splide,t))}),r&&s()}function c(){o.forEach(function(u){u.destroy()}),fe(o)}function l(){c(),a()}function d(u,f){var I=X(u);I.on(Te,function(_,h,E){f.go(f.is(He)?E:_)}),o.push(I)}function s(){var u=X(t),f=u.on;f(yn,v),f(bn,g),f([De,Q],m),o.push(u),u.emit(Nn,t.splides)}function m(){M(n.Elements.list,Kt,e.direction===Nt?"vertical":"")}function v(u){t.go(u.index)}function g(u,f){Gt(hi,nn(f))&&(v(u),ve(f))}return{setup:U(n.Media.set,{slideFocus:$e(i)?r:i},!0),mount:a,destroy:c,remount:l}}function _i(t,n,e){var r=X(t),i=r.bind,o=0;function a(){e.wheel&&i(n.Elements.track,"wheel",c,Be)}function c(d){if(d.cancelable){var s=d.deltaY,m=s<0,v=Ft(d),g=e.wheelMinThreshold||0,u=e.wheelSleep||0;J(s)>g&&v-o>u&&(t.go(m?"<":">"),o=v),l(m)&&ve(d)}}function l(d){return!e.releaseWheel||t.state.is(xe)||n.Controller.getAdjacent(d)!==-1}return{mount:a}}var Ii=90;function Ti(t,n,e){var r=X(t),i=r.on,o=n.Elements.track,a=e.live&&!e.isNavigation,c=Ge("span",kr),l=pt(Ii,U(s,!1));function d(){a&&(v(!n.Autoplay.isPaused()),M(o,Un,!0),c.textContent="…",i(Rn,U(v,!0)),i(Dn,U(v,!1)),i([it,ke],U(s,!0)))}function s(g){M(o,Fn,g),g?(qe(o,c),l.start()):(Ne(c),l.cancel())}function m(){ce(o,[kn,Un,Fn]),Ne(c)}function v(g){a&&M(o,kn,g?"off":"polite")}return{mount:d,disable:v,destroy:m}}var Si=Object.freeze({__proto__:null,Media:Cr,Direction:br,Elements:Xr,Slides:Yr,Layout:$r,Clones:qr,Move:jr,Controller:Zr,Arrows:ei,Autoplay:ni,Cover:ri,Scroll:ci,Drag:li,Keyboard:di,LazyLoad:gi,Pagination:Ei,Sync:mi,Wheel:_i,Live:Ti}),pi={prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay",carousel:"carousel",slide:"slide",select:"Select a slide to show",slideLabel:"%s of %s"},Ai={type:"slide",role:"region",speed:400,perPage:1,cloneStatus:!0,arrows:!0,pagination:!0,paginationKeyboard:!0,interval:5e3,pauseOnHover:!0,pauseOnFocus:!0,resetProgress:!0,easing:"cubic-bezier(0.25, 1, 0.5, 1)",drag:!0,direction:"ltr",trimSpace:!0,focusableNodes:"a, button, textarea, input, select, iframe",live:!0,classes:Hr,i18n:pi,reducedMotion:{speed:0,rewindSpeed:0,autoplay:"pause"}};function yi(t,n,e){var r=n.Slides;function i(){X(t).on([De,K],o)}function o(){r.forEach(function(c){c.style("transform","translateX(-"+100*c.index+"%)")})}function a(c,l){r.style("transition","opacity "+e.speed+"ms "+e.easing),ln(l)}return{mount:i,start:a,cancel:wt}}function Li(t,n,e){var r=n.Move,i=n.Controller,o=n.Scroll,a=n.Elements.list,c=U(ae,a,"transition"),l;function d(){X(t).bind(a,"transitionend",function(g){g.target===a&&l&&(m(),l())})}function s(g,u){var f=r.toPosition(g,!0),I=r.getPosition(),_=v(g);J(f-I)>=1&&_>=1?e.useScroll?o.scroll(f,_,!1,u):(c("transform "+_+"ms "+e.easing),r.translate(f,!0),l=u):(r.jump(g),u())}function m(){c(""),o.cancel()}function v(g){var u=e.rewindSpeed;if(t.is(Ue)&&u){var f=i.getIndex(!0),I=i.getEnd();if(f===0&&g>=I||f>=I&&g===0)return u}return e.speed}return{mount:d,start:s,cancel:m}}var Ni=(function(){function t(e,r){this.event=X(),this.Components={},this.state=Rr(we),this.splides=[],this._o={},this._E={};var i=me(e)?In(document,e):e;nt(i,i+" is invalid."),this.root=i,r=_e({label:oe(i,ie)||"",labelledby:oe(i,$t)||""},Ai,t.defaults,r||{});try{_e(r,JSON.parse(oe(i,Ut)))}catch{nt(!1,"Invalid JSON")}this._o=Object.create(_e({},r))}var n=t.prototype;return n.mount=function(r,i){var o=this,a=this.state,c=this.Components;nt(a.is([we,Et]),"Already mounted!"),a.set(we),this._C=c,this._T=i||this._T||(this.is(ut)?yi:Li),this._E=r||this._E;var l=Qe({},Si,this._E,{Transition:this._T});return Le(l,function(d,s){var m=d(o,c,o._o);c[s]=m,m.setup&&m.setup()}),Le(c,function(d){d.mount&&d.mount()}),this.emit(De),ue(this.root,Fr),a.set(Me),this.emit(An),this},n.sync=function(r){return this.splides.push({splide:r}),r.splides.push({splide:this,isParent:!0}),this.state.is(Me)&&(this._C.Sync.remount(),r.Components.Sync.remount()),this},n.go=function(r){return this._C.Controller.go(r),this},n.on=function(r,i){return this.event.on(r,i),this},n.off=function(r){return this.event.off(r),this},n.emit=function(r){var i;return(i=this.event).emit.apply(i,[r].concat(he(arguments,1))),this},n.add=function(r,i){return this._C.Slides.add(r,i),this},n.remove=function(r){return this._C.Slides.remove(r),this},n.is=function(r){return this._o.type===r},n.refresh=function(){return this.emit(K),this},n.destroy=function(r){r===void 0&&(r=!0);var i=this.event,o=this.state;return o.is(we)?X(this).on(An,this.destroy.bind(this,r)):(Le(this._C,function(a){a.destroy&&a.destroy(r)},!0),i.emit(Ln),i.destroy(),r&&fe(this.splides),o.set(Et)),this},lr(t,[{key:"options",get:function(){return this._o},set:function(r){this._C.Media.set(r,!0,!0)}},{key:"length",get:function(){return this._C.Slides.getLength(!0)}},{key:"index",get:function(){return this._C.Controller.getIndex()}}]),t})(),rn=Ni;rn.defaults={},rn.STATES=dr;class Ri{constructor(){this.isDestroyed=!1}initialize(n){try{return this.destroy(),this.splideInstance=new rn(`.${z.slider.dom.splideClass}`,n),this.splideInstance.mount(),this.isDestroyed=!1,this.splideInstance&&z.env==="development"&&this.addDebugEventListeners(),this.splideInstance}catch{return}}destroy(){if(this.splideInstance&&!this.isDestroyed)try{this.splideInstance.destroy()}catch{}finally{delete this.splideInstance,this.isDestroyed=!0}}isInitialized(){return this.splideInstance!==null&&!this.isDestroyed}getInstance(){if(this.isInitialized())return this.splideInstance}reinitialize(n){return this.destroy(),this.initialize(n)}isContainerAvailable(){try{return document.querySelector(`.${z.slider.dom.splideClass}`)!==null}catch{return!1}}initializeWithDelay(n,e=dt.CHECK_INTERVAL){setTimeout(()=>{this.isContainerAvailable()&&this.initialize(n)},e)}getStatus(){return{isInitialized:this.isInitialized(),isDestroyed:this.isDestroyed,hasInstance:this.splideInstance!==null,containerAvailable:this.isContainerAvailable()}}addDebugEventListeners(){this.splideInstance&&this.splideInstance.on("moved",(n,e)=>{if(z.env==="development"){let r="forward";n<=e&&(r="backward"),globalThis.__splideDebug={lastMove:{from:e,to:n,direction:r},timestamp:Date.now()}}})}}class Di{constructor(){this.checkTime=z.slider.checkTime,this.configManager=new or,this.domBuilder=new sr,this.dataManager=new ur,this.lifecycleManager=new Ri}attachAdvertiseHeader(n){try{this.destroy();const e=this.dataManager.fetchSlideData();if(e.length===te.EMPTY_LENGTH)return;const r=this.domBuilder.createContainer(),i=this.domBuilder.createSplideElement(r),o=this.domBuilder.createSplideTrack(i),a=this.populateSlides(o,e);this.domBuilder.createPagination(i),this.domBuilder.appendToDOM(r),setTimeout(()=>{this.initializeSplide(a)},this.checkTime)}catch{}}populateSlides(n,e){let r=te.EMPTY_LENGTH;for(const i of e)if(i.isValid){const o=this.domBuilder.createSlide(i.imageSrc,i.imageLink);Xe(n,o),r+=ft.SLIDE_INCREMENT}return r}initializeSplide(n){try{const e=this.dataManager.getTransitionTime(),r=this.configManager.calculateConfiguration(n,e);if(!this.configManager.validateConfiguration(r.finalConfig).isValid)return;this.lifecycleManager.initialize(r.finalConfig)}catch{}}destroy(){this.lifecycleManager.destroy(),this.domBuilder.cleanup()}}class ze{constructor(){this.errorLog=[],this.isInitialized=!1}static getInstance(){return ze.instance||(ze.instance=new ze),ze.instance}initialize(){try{return this.isInitialized||(this.setupGlobalErrorHandling(),this.isInitialized=!0),!0}catch{return!1}}handleSync(n,e){try{return n()}catch(r){return this.logError(r,e),!1}}handleAsync(n,e){return n().catch(r=>(this.logError(r,e),!1))}logError(n,e){try{const r={timestamp:new Date,error:n,context:e};this.errorLog.push(r),this.errorLog.length>ir.MAX_ERROR_LOG_ENTRIES&&this.errorLog.shift()}catch{}}setupGlobalErrorHandling(){try{globalThis.addEventListener("unhandledrejection",n=>{this.logError(new Error(String(n.reason)),"Unhandled Promise Rejection")})}catch{}}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[]}}class be{constructor(){}static getInstance(){return be.instance||(be.instance=new be),be.instance}isTagsPage(){try{return Ae.current.get("routeName")==="tags"}catch{try{return globalThis.location.pathname.includes("/tags")}catch{return!1}}}getConfig(){return z}isSlideshowConfigured(){try{for(let r=1;r<=z.slider.maxSlides;r+=1)if(Ae.forum.attribute(`wusong8899-header-advertisement.Image${r}`))return!0;return!1}catch{return!1}}}Ae.initializers.add(z.app.extensionId,()=>{const t=ze.getInstance(),n=be.getInstance();if(!t.initialize())return;const e=new Di;er.extend(tr.prototype,"view",function(i){t.handleSync(()=>{n.isTagsPage()&&Ci(i,e)},"HeaderPrimary view extension")})});const Ci=(t,n,e)=>{try{if(be.getInstance().isSlideshowConfigured())try{n.attachAdvertiseHeader(t)}catch{}!Ae.session.user&&un()&&bi()}catch{}},bi=()=>{let t=document.getElementById(z.ui.headerIconId);if(t===null){const n=Ae.forum.attribute("wusong8899-header-advertisement.HeaderIconUrl")||z.ui.headerIconUrl;t=document.createElement("div"),t.id=z.ui.headerIconId,t.className="HeaderIcon-container mobile-only",t.innerHTML=`<img src="${n}" alt="Header Icon" class="HeaderIcon-image" />`;const e=document.querySelector("#app-navigation .Navigation.ButtonGroup.App-backControl");e&&e.appendChild(t)}}})(flarum.core.compat["common/extend"],flarum.core.compat["forum/app"],flarum.core.compat["forum/components/HeaderPrimary"]);
//# sourceMappingURL=forum.js.map

module.exports={};