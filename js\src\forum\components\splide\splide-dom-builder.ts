import * as DOMUtils from '../../utils/dom-utils';
import { isMobileDevice } from '../../utils/mobile-detection';
import { defaultConfig } from '../../../common/config';
import type { ISplideeDOMBuilder } from '../../../common/config/types';

/**
 * Splide DOM builder
 * Handles all DOM creation and manipulation for Splide components
 */
export class SplideD<PERSON><PERSON>uilder implements ISplideeDOMBuilder {
  private container: HTMLElement | undefined;

  /**
   * Create main container element
   */
  createContainer(): HTMLElement {
    this.removeExistingNavigation();

    const container = DOMUtils.createElement('div', {
      id: defaultConfig.slider.dom.containerId,
      className: 'adContainer adContainer--forced'
    });

    this.container = container;
    return container;
  }

  /**
   * Create Splide element
   */
  createSplideElement(container: HTMLElement): HTMLElement {
    let className = `splide ${defaultConfig.slider.dom.splideClass} adSplide--forced`;

    // Add mobile-specific class if on mobile device
    if (isMobileDevice()) {
      className += ' adSplide--mobile';
    }

    const splide = DOMUtils.createElement('div', {
      className
    });

    DOMUtils.appendChild(container, splide);
    return splide;
  }

  /**
   * Create Splide track
   */
  createSplideTrack(splide: HTMLElement): HTMLElement {
    const track = DOMUtils.createElement('div', {
      className: 'splide__track splide__track--forced'
    });

    const list = DOMUtils.createElement('ul', {
      className: 'splide__list splide__list--forced'
    });

    DOMUtils.appendChild(track, list);
    DOMUtils.appendChild(splide, track);
    return list;
  }

  /**
   * Create individual slide
   */
  createSlide(imageSrc: string, imageLink: string): HTMLElement {
    const slide = DOMUtils.createElement('li', {
      className: 'splide__slide splide__slide--forced'
    });

    let clickHandler = '';
    if (imageLink) {
      clickHandler = `window.location.href="${imageLink}"`;
    }

    // Create image with CSS class instead of inline styles
    slide.innerHTML = `<img onclick='${clickHandler}' src='${imageSrc}' class='splide__slide__image' />`;

    return slide;
  }

  /**
   * Create pagination element
   */
  createPagination(splide: HTMLElement): void {
    const pagination = DOMUtils.createElement('ul', {
      className: 'splide__pagination'
    });
    DOMUtils.appendChild(splide, pagination);
  }

  /**
   * Create navigation elements
   */
  createNavigation(splide: HTMLElement): void {
    const prevButton = DOMUtils.createElement('button', {
      className: 'splide__arrow splide__arrow--prev splide__arrow--prev--custom'
    });
    const nextButton = DOMUtils.createElement('button', {
      className: 'splide__arrow splide__arrow--next splide__arrow--next--custom'
    });

    DOMUtils.appendChild(splide, prevButton);
    DOMUtils.appendChild(splide, nextButton);
  }

  /**
   * Append slideshow to DOM
   */
  appendToDOM(container: HTMLElement): void {
    const contentContainer = DOMUtils.querySelector("#content .container");
    if (contentContainer) {
      DOMUtils.prependChild(contentContainer, container);
    }
  }

  /**
   * Clean up DOM elements
   */
  cleanup(): void {
    if (this.container) {
      DOMUtils.removeElement(this.container);
      delete this.container;
    }
  }

  /**
   * Get the current container element
   */
  getContainer(): HTMLElement | undefined {
    return this.container;
  }

  /**
   * Remove existing navigation elements
   */
  private removeExistingNavigation(): void {
    const existingContainer = DOMUtils.querySelector(`#${defaultConfig.slider.dom.containerId}`);
    if (existingContainer) {
      DOMUtils.removeElement(existingContainer);
    }

    const navElements = DOMUtils.querySelectorAll(".item-nav");
    for (const element of navElements) {
      DOMUtils.removeElement(element);
    }
  }
}
